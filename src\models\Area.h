#ifndef AREA_H
#define AREA_H

#include <QString>
#include <QDateTime>
#include <QList>
#include <memory>

namespace AccessControl {

/**
 * @brief 区域数据模型
 * 支持树形结构，每个区域可以有上级区域和下级区域
 */
class Area {
public:
    /**
     * @brief 区域状态枚举
     */
    enum Status {
        Active = 1,    // 启用
        Inactive = 0   // 禁用
    };

    /**
     * @brief 构造函数
     */
    Area();
    
    /**
     * @brief 带参数的构造函数
     * @param name 区域名称
     * @param code 区域代码
     * @param parentId 上级区域ID
     */
    Area(const QString& name, const QString& code, int parentId = 0);
    
    /**
     * @brief 拷贝构造函数
     */
    Area(const Area& other);
    
    /**
     * @brief 赋值操作符
     */
    Area& operator=(const Area& other);
    
    /**
     * @brief 析构函数
     */
    ~Area();

    // ========== Getter 方法 ==========
    
    /**
     * @brief 获取区域ID
     */
    int id() const { return m_id; }
    
    /**
     * @brief 获取区域名称
     */
    QString name() const { return m_name; }
    
    /**
     * @brief 获取区域代码
     */
    QString code() const { return m_code; }
    
    /**
     * @brief 获取上级区域ID
     */
    int parentId() const { return m_parentId; }
    
    /**
     * @brief 获取区域描述
     */
    QString description() const { return m_description; }
    
    /**
     * @brief 获取区域状态
     */
    Status status() const { return m_status; }
    
    /**
     * @brief 获取排序顺序
     */
    int sortOrder() const { return m_sortOrder; }
    
    /**
     * @brief 获取创建时间
     */
    QDateTime createdAt() const { return m_createdAt; }
    
    /**
     * @brief 获取更新时间
     */
    QDateTime updatedAt() const { return m_updatedAt; }
    
    /**
     * @brief 获取区域层级（从根区域开始计算）
     */
    int level() const { return m_level; }
    
    /**
     * @brief 获取区域完整路径（如：园区/东区/1号楼）
     */
    QString fullPath() const { return m_fullPath; }

    // ========== Setter 方法 ==========
    
    /**
     * @brief 设置区域ID
     */
    void setId(int id) { m_id = id; }
    
    /**
     * @brief 设置区域名称
     */
    void setName(const QString& name) { m_name = name; }
    
    /**
     * @brief 设置区域代码
     */
    void setCode(const QString& code) { m_code = code; }
    
    /**
     * @brief 设置上级区域ID
     */
    void setParentId(int parentId) { m_parentId = parentId; }
    
    /**
     * @brief 设置区域描述
     */
    void setDescription(const QString& description) { m_description = description; }
    
    /**
     * @brief 设置区域状态
     */
    void setStatus(Status status) { m_status = status; }
    
    /**
     * @brief 设置排序顺序
     */
    void setSortOrder(int sortOrder) { m_sortOrder = sortOrder; }
    
    /**
     * @brief 设置创建时间
     */
    void setCreatedAt(const QDateTime& createdAt) { m_createdAt = createdAt; }
    
    /**
     * @brief 设置更新时间
     */
    void setUpdatedAt(const QDateTime& updatedAt) { m_updatedAt = updatedAt; }
    
    /**
     * @brief 设置区域层级
     */
    void setLevel(int level) { m_level = level; }
    
    /**
     * @brief 设置区域完整路径
     */
    void setFullPath(const QString& fullPath) { m_fullPath = fullPath; }

    // ========== 工具方法 ==========
    
    /**
     * @brief 检查是否为根区域
     */
    bool isRoot() const { return m_parentId == 0; }
    
    /**
     * @brief 检查是否为启用状态
     */
    bool isActive() const { return m_status == Active; }
    
    /**
     * @brief 获取状态文本
     */
    QString statusText() const;
    
    /**
     * @brief 检查区域数据是否有效
     */
    bool isValid() const;
    
    /**
     * @brief 转换为调试字符串
     */
    QString toString() const;

private:
    int m_id;                    // 区域ID
    QString m_name;              // 区域名称
    QString m_code;              // 区域代码
    int m_parentId;              // 上级区域ID（0表示根区域）
    QString m_description;       // 区域描述
    Status m_status;             // 区域状态
    int m_sortOrder;             // 排序顺序
    QDateTime m_createdAt;       // 创建时间
    QDateTime m_updatedAt;       // 更新时间
    int m_level;                 // 区域层级
    QString m_fullPath;          // 完整路径
};

/**
 * @brief 区域树节点
 * 用于构建区域树形结构
 */
class AreaTreeNode {
public:
    AreaTreeNode(const Area& area);
    ~AreaTreeNode();
    
    // 区域数据
    Area area;
    
    // 父节点
    AreaTreeNode* parent;
    
    // 子节点列表
    QList<std::shared_ptr<AreaTreeNode>> children;
    
    /**
     * @brief 添加子节点
     */
    void addChild(std::shared_ptr<AreaTreeNode> child);
    
    /**
     * @brief 移除子节点
     */
    bool removeChild(int areaId);
    
    /**
     * @brief 查找子节点
     */
    std::shared_ptr<AreaTreeNode> findChild(int areaId);
    
    /**
     * @brief 获取所有后代节点
     */
    QList<std::shared_ptr<AreaTreeNode>> getAllDescendants();
    
    /**
     * @brief 获取节点路径（从根到当前节点）
     */
    QList<int> getPath();
};

} // namespace AccessControl

#endif // AREA_H 