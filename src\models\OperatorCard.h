#ifndef OPERATORCARD_H
#define OPERATORCARD_H

#include <QString>
#include <QDateTime>
#include <QVariant>

namespace AccessControl {

/**
 * @brief 用户卡片模型类
 * 管理用户的各种门禁卡信息
 */
class OperatorCard {
public:
    /**
     * @brief 卡片类型枚举
     */
    enum class CardType {
        IC_ID = 0,      // IC/ID卡
        CPU = 1,        // CPU卡
        Fingerprint = 2, // 指纹
        Face = 3,       // 人脸
        Phone = 4,      // 手机号
        IDCard = 5,     // 身份证
        Panic = 6,      // 胁迫卡
        Master = 7      // 母卡
    };

    /**
     * @brief 卡片状态枚举
     */
    enum class CardStatus {
        Active = 0,     // 启用
        Disabled = 1,   // 禁用
        Lost = 2,       // 挂失
        Expired = 3     // 过期
    };

public:
    OperatorCard();
    OperatorCard(int userId, CardType type, const QString& cardNumber);
    ~OperatorCard() = default;

    // ========== 基本属性 ==========
    int id() const { return m_id; }
    void setId(int id) { m_id = id; }

    int userId() const { return m_userId; }
    void setUserId(int userId) { m_userId = userId; }

    CardType cardType() const { return m_cardType; }
    void setCardType(CardType type) { m_cardType = type; }

    QString cardNumber() const { return m_cardNumber; }
    void setCardNumber(const QString& cardNumber) { m_cardNumber = cardNumber; }

    bool isPrimary() const { return m_isPrimary; }
    void setIsPrimary(bool primary) { m_isPrimary = primary; }

    CardStatus status() const { return m_status; }
    void setStatus(CardStatus status) { m_status = status; }

    // ========== 时间属性 ==========
    QDateTime bindDate() const { return m_bindDate; }
    void setBindDate(const QDateTime& dateTime) { m_bindDate = dateTime; }

    QDateTime lastUsed() const { return m_lastUsed; }
    void setLastUsed(const QDateTime& dateTime) { m_lastUsed = dateTime; }

    QDateTime createdAt() const { return m_createdAt; }
    void setCreatedAt(const QDateTime& dateTime) { m_createdAt = dateTime; }

    // ========== 其他属性 ==========
    QString remarks() const { return m_remarks; }
    void setRemarks(const QString& remarks) { m_remarks = remarks; }

    // ========== 验证方法 ==========
    bool isValid() const;
    bool isActive() const;
    bool isBiometric() const;
    bool isPhysicalCard() const;
    bool canBePrimary() const;

    // ========== 序列化 ==========
    QVariantMap toVariantMap() const;
    void fromVariantMap(const QVariantMap& map);

    // ========== 静态方法 ==========
    static QString cardTypeToString(CardType type);
    static CardType stringToCardType(const QString& typeStr);
    static QString cardStatusToString(CardStatus status);
    static CardStatus stringToCardStatus(const QString& statusStr);
    static QString getCardTypeDescription(CardType type);

private:
    // 基本信息
    int m_id;
    int m_userId;
    CardType m_cardType;
    QString m_cardNumber;
    bool m_isPrimary;
    CardStatus m_status;

    // 时间信息
    QDateTime m_bindDate;
    QDateTime m_lastUsed;
    QDateTime m_createdAt;

    // 其他信息
    QString m_remarks;
};

} // namespace AccessControl

#endif // OPERATORCARD_H 