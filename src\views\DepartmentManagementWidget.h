#ifndef DEPARTMENTMANAGEMENTWIDGET_H
#define DEPARTMENTMANAGEMENTWIDGET_H

#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QTreeWidget>
#include <QTreeWidgetItem>
#include <QPushButton>
#include <QLineEdit>
#include <QTextEdit>
#include <QComboBox>
#include <QLabel>
#include <QGroupBox>
#include <QSplitter>
#include <QMessageBox>
#include <QMenu>
#include <QAction>
#include <QHeaderView>
#include <QSpinBox>
#include <QFileDialog>
#include <QStandardPaths>
#include <QProgressDialog>
#include <QApplication>
#include <QTextStream>
#include <QDir>
#include <memory>

#include "../models/Department.h"
#include "../database/dao/DepartmentDao.h"
#include "../database/IDatabaseProvider.h"

namespace AccessControl {

/**
 * @brief 部门管理界面
 * 提供部门的树形视图和CRUD操作功能
 */
class DepartmentManagementWidget : public QWidget
{
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param dbProvider 数据库提供者
     * @param parent 父组件
     */
    explicit DepartmentManagementWidget(std::shared_ptr<IDatabaseProvider> dbProvider, 
                                       QWidget *parent = nullptr);
    
    /**
     * @brief 析构函数
     */
    ~DepartmentManagementWidget();

    /**
     * @brief 刷新部门数据
     */
    void refreshDepartments();

private slots:
    /**
     * @brief 树形视图选择改变
     */
    void onTreeSelectionChanged();
    
    /**
     * @brief 添加部门
     */
    void onAddDepartment();
    
    /**
     * @brief 添加子部门
     */
    void onAddSubDepartment();
    
    /**
     * @brief 编辑部门
     */
    void onEditDepartment();
    
    /**
     * @brief 删除部门
     */
    void onDeleteDepartment();
    
    /**
     * @brief 保存部门
     */
    void onSaveDepartment();
    
    /**
     * @brief 取消编辑
     */
    void onCancelEdit();
    
    /**
     * @brief 搜索部门
     */
    void onSearchDepartments();
    
    /**
     * @brief 清空搜索
     */
    void onClearSearch();
    
    /**
     * @brief 展开所有节点
     */
    void onExpandAll();
    
    /**
     * @brief 收起所有节点
     */
    void onCollapseAll();
    
    /**
     * @brief 导入部门
     */
    void onImportDepartments();
    
    /**
     * @brief 导出部门
     */
    void onExportDepartments();
    
    /**
     * @brief 显示右键菜单
     */
    void onShowContextMenu(const QPoint& pos);

private:
    /**
     * @brief 初始化UI界面
     */
    void initializeUI();
    
    /**
     * @brief 初始化左侧面板（树形视图）
     */
    void initializeLeftPanel();
    
    /**
     * @brief 初始化右侧面板（详细信息）
     */
    void initializeRightPanel();
    
    /**
     * @brief 初始化工具栏
     */
    void initializeToolbar();
    
    /**
     * @brief 初始化右键菜单
     */
    void initializeContextMenu();
    
    /**
     * @brief 连接信号和槽
     */
    void connectSignals();
    
    /**
     * @brief 加载部门树
     */
    void loadDepartmentTree();
    
    /**
     * @brief 构建树形项目
     * @param nodes 部门树节点列表
     * @param parentItem 父树形项目
     */
    void buildTreeItems(const QList<std::shared_ptr<DepartmentTreeNode>>& nodes, 
                       QTreeWidgetItem* parentItem = nullptr);
    
    /**
     * @brief 显示部门详细信息
     * @param department 部门对象
     */
    void showDepartmentDetails(const Department& department);
    
    /**
     * @brief 清空详细信息表单
     */
    void clearDetailsForm();
    
    /**
     * @brief 清空表单用于添加新部门（保持父部门选择）
     */
    void clearDetailsFormForAdd();
    
    /**
     * @brief 设置表单编辑模式
     * @param editMode 是否为编辑模式
     */
    void setFormEditMode(bool editMode);
    
    /**
     * @brief 从表单获取部门对象
     * @return 部门对象
     */
    Department getDepartmentFromForm();
    
    /**
     * @brief 验证表单数据
     * @return 验证结果和错误消息
     */
    QPair<bool, QString> validateForm();
    
    /**
     * @brief 查找树形项目
     * @param departmentId 部门ID
     * @param parentItem 父项目（nullptr表示从根开始）
     * @return 找到的树形项目
     */
    QTreeWidgetItem* findTreeItem(int departmentId, QTreeWidgetItem* parentItem = nullptr);
    
    /**
     * @brief 更新部门统计信息
     * @param departmentId 部门ID
     */
    void updateDepartmentStats(int departmentId);
    
    /**
     * @brief 应用搜索过滤
     * @param keyword 搜索关键词
     */
    void applySearchFilter(const QString& keyword);
    
    /**
     * @brief 递归过滤树形项目
     * @param item 树形项目
     * @param keyword 搜索关键词
     * @return 是否匹配
     */
    bool filterTreeItem(QTreeWidgetItem* item, const QString& keyword);
    
    /**
     * @brief 导出部门数据到CSV文件
     * @param fileName 文件路径
     * @return 是否成功
     */
    bool exportDepartmentsToCSV(const QString& fileName);
    
    /**
     * @brief 从CSV文件导入部门数据
     * @param fileName 文件路径
     * @return 导入结果信息
     */
    QPair<bool, QString> importDepartmentsFromCSV(const QString& fileName);
    
    /**
     * @brief 生成CSV示例文件
     * @param fileName 文件路径
     * @return 是否成功
     */
    bool generateCSVTemplate(const QString& fileName);
    
    /**
     * @brief 验证导入的部门数据
     * @param departments 部门列表
     * @return 验证结果和错误信息
     */
    QPair<bool, QString> validateImportData(const QList<Department>& departments);
    
    /**
     * @brief 解析CSV行数据为部门对象
     * @param fields CSV字段列表
     * @param lineNumber 行号（用于错误报告）
     * @return 部门对象和是否成功
     */
    QPair<Department, bool> parseCSVDepartment(const QStringList& fields, int lineNumber);
    
    /**
     * @brief 解析CSV行字符串为字段列表
     * @param line CSV行字符串
     * @return 字段列表
     */
    QStringList parseCSVLine(const QString& line);
    
    /**
     * @brief 按层级排序部门列表
     * @param departments 部门列表
     * @return 排序后的部门列表
     */
    QList<Department> sortDepartmentsByLevel(const QList<Department>& departments);

private:
    // 数据访问
    std::shared_ptr<IDatabaseProvider> m_dbProvider;
    std::unique_ptr<DepartmentDao> m_departmentDao;
    
    // 当前状态
    Department m_currentDepartment;
    bool m_isEditMode;
    
    // 主布局
    QHBoxLayout* m_mainLayout;
    QSplitter* m_splitter;
    
    // 左侧面板（树形视图）
    QWidget* m_leftPanel;
    QVBoxLayout* m_leftLayout;
    
    // 工具栏
    QWidget* m_toolbar;
    QHBoxLayout* m_toolbarLayout;
    QPushButton* m_btnAdd;
    QPushButton* m_btnAddSub;
    QPushButton* m_btnEdit;
    QPushButton* m_btnDelete;
    QPushButton* m_btnExpandAll;
    QPushButton* m_btnCollapseAll;
    QPushButton* m_btnImport;
    QPushButton* m_btnExport;
    
    // 搜索区域
    QWidget* m_searchWidget;
    QHBoxLayout* m_searchLayout;
    QLineEdit* m_searchEdit;
    QPushButton* m_btnSearch;
    QPushButton* m_btnClearSearch;
    QPushButton* m_btnRefresh;
    
    // 树形视图
    QTreeWidget* m_treeWidget;
    
    // 右侧面板（详细信息）
    QWidget* m_rightPanel;
    QVBoxLayout* m_rightLayout;
    
    // 详细信息表单
    QGroupBox* m_detailsGroup;
    QGridLayout* m_detailsLayout;
    QLabel* m_lblName;
    QLineEdit* m_editName;
    QLabel* m_lblCode;
    QLineEdit* m_editCode;
    QLabel* m_lblParent;
    QComboBox* m_comboParent;
    QLabel* m_lblDescription;
    QTextEdit* m_editDescription;
    QLabel* m_lblStatus;
    QComboBox* m_comboStatus;
    QLabel* m_lblSortOrder;
    QSpinBox* m_spinSortOrder;
    
    // 操作按钮
    QWidget* m_buttonWidget;
    QHBoxLayout* m_buttonLayout;
    QPushButton* m_btnSave;
    QPushButton* m_btnCancel;
    
    // 统计信息
    QGroupBox* m_statsGroup;
    QGridLayout* m_statsLayout;
    QLabel* m_lblChildCount;
    QLabel* m_lblDirectUserCount;
    QLabel* m_lblTotalUserCount;
    QLabel* m_lblCreatedAt;
    QLabel* m_lblUpdatedAt;
    
    // 右键菜单
    QMenu* m_contextMenu;
    QAction* m_actionAdd;
    QAction* m_actionAddSub;
    QAction* m_actionEdit;
    QAction* m_actionDelete;
    QAction* m_actionRefresh;
};

} // namespace AccessControl

#endif // DEPARTMENTMANAGEMENTWIDGET_H 