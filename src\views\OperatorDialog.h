#ifndef OPERATORDIALOG_H
#define OPERATORDIALOG_H

#include <QDialog>
#include <QLineEdit>
#include <QPushButton>
#include <QCheckBox>

QT_BEGIN_NAMESPACE
class QFormLayout;
class QVBoxLayout;
class QHBoxLayout;
QT_END_NAMESPACE

namespace AccessControl {

class Operator;

/**
 * @brief 操作员对话框
 * 
 * 用于添加和编辑操作员信息的对话框
 */
class OperatorDialog : public QDialog
{
    Q_OBJECT

public:
    /**
     * @brief 对话框模式
     */
    enum class Mode {
        Add,    ///< 添加模式
        Edit    ///< 编辑模式
    };

    /**
     * @brief 构造函数
     * @param parent 父窗口
     * @param mode 对话框模式
     * @param operatorId 操作员ID（编辑模式时使用）
     */
    explicit OperatorDialog(QWidget *parent = nullptr, Mode mode = Mode::Add, int operatorId = -1);
    
    /**
     * @brief 析构函数
     */
    ~OperatorDialog();

protected:
    /**
     * @brief 关闭事件处理
     * @param event 关闭事件
     */
    void closeEvent(QCloseEvent *event) override;

private slots:
    /**
     * @brief 保存操作员信息
     */
    void saveOperator();

    /**
     * @brief 取消操作
     */
    void cancel();

private:
    /**
     * @brief 初始化UI
     */
    void initUI();

    /**
     * @brief 填充操作员数据（编辑模式）
     */
    void fillOperatorData();

    /**
     * @brief 验证表单数据
     * @return 验证结果
     */
    bool validateForm();

    /**
     * @brief 保存操作员信息到数据库
     * @return 保存结果
     */
    bool saveOperatorToDatabase();

private:
    Mode m_mode;                  ///< 对话框模式
    int m_operatorId;            ///< 操作员ID

    // UI控件
    QLineEdit* m_usernameEdit;   ///< 用户名输入框
    QLineEdit* m_passwordEdit;   ///< 密码输入框
    QLineEdit* m_confirmPasswordEdit; ///< 确认密码输入框
    QLineEdit* m_realNameEdit;   ///< 真实姓名输入框
    QCheckBox* m_isActiveCheck;  ///< 是否启用复选框
    
    QPushButton* m_saveButton;   ///< 保存按钮
    QPushButton* m_cancelButton; ///< 取消按钮
};

} // namespace AccessControl

#endif // OPERATORDIALOG_H 