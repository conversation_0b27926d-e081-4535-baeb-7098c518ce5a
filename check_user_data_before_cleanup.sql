-- 用户数据核对脚本 - 删除前必须执行
-- 目的：全面检查user相关表的数据，确保无遗漏迁移
-- 创建时间：2025-01-26
-- 警告：只有在确认所有数据都已正确迁移后才能删除user表

-- ========== 1. 查看数据库中所有表 ==========

-- 列出所有表
SELECT '所有数据库表' as category, name as table_name, type 
FROM sqlite_master 
WHERE type='table' 
ORDER BY name;

-- ========== 2. 检查user相关表的存在情况 ==========

-- 查找所有user相关的表
SELECT '需要检查的user相关表' as category, name as table_name, 
       sql as create_statement
FROM sqlite_master 
WHERE type='table' AND (
    name LIKE '%user%' OR 
    name = 'users'
)
ORDER BY name;

-- ========== 3. 详细检查每个user表的数据 ==========

-- 检查users表（如果存在）
SELECT 'users表检查' as check_type;
SELECT CASE 
    WHEN EXISTS (SELECT 1 FROM sqlite_master WHERE type='table' AND name='users')
    THEN '✓ users表存在'
    ELSE '- users表不存在'
END as users_table_status;

-- 如果users表存在，显示数据概况
SELECT CASE 
    WHEN EXISTS (SELECT 1 FROM sqlite_master WHERE type='table' AND name='users')
    THEN (SELECT COUNT(*) FROM users)
    ELSE 0
END as users_record_count;

-- 如果users表存在，显示数据详情
.mode column
.headers on
SELECT 'users表数据样本' as info;
SELECT id, username, real_name, role, status, work_number, phone_number, 
       access_enabled, attendance_enabled, created_at
FROM users 
WHERE EXISTS (SELECT 1 FROM sqlite_master WHERE type='table' AND name='users')
LIMIT 10;

-- ========== 4. 检查user_cards表 ==========

SELECT 'user_cards表检查' as check_type;
SELECT CASE 
    WHEN EXISTS (SELECT 1 FROM sqlite_master WHERE type='table' AND name='user_cards')
    THEN '✓ user_cards表存在'
    ELSE '- user_cards表不存在'
END as user_cards_status;

-- 如果存在，显示记录数
SELECT CASE 
    WHEN EXISTS (SELECT 1 FROM sqlite_master WHERE type='table' AND name='user_cards')
    THEN (SELECT COUNT(*) FROM user_cards)
    ELSE 0
END as user_cards_count;

-- 显示user_cards数据样本
SELECT 'user_cards表数据样本' as info;
SELECT id, user_id, card_number, card_type, status, is_primary, created_at
FROM user_cards 
WHERE EXISTS (SELECT 1 FROM sqlite_master WHERE type='table' AND name='user_cards')
LIMIT 10;

-- ========== 5. 检查user_profiles表 ==========

SELECT 'user_profiles表检查' as check_type;
SELECT CASE 
    WHEN EXISTS (SELECT 1 FROM sqlite_master WHERE type='table' AND name='user_profiles')
    THEN '✓ user_profiles表存在'
    ELSE '- user_profiles表不存在'
END as user_profiles_status;

-- 如果存在，显示记录数和样本
SELECT CASE 
    WHEN EXISTS (SELECT 1 FROM sqlite_master WHERE type='table' AND name='user_profiles')
    THEN (SELECT COUNT(*) FROM user_profiles)
    ELSE 0
END as user_profiles_count;

-- ========== 6. 检查新表的数据完整性 ==========

-- consumers表数据检查
SELECT 'consumers表数据检查' as check_type;
SELECT COUNT(*) as consumers_count FROM consumers;
SELECT id, work_number, real_name, phone_number, status, created_at
FROM consumers LIMIT 5;

-- consumer_cards表数据检查  
SELECT 'consumer_cards表数据检查' as check_type;
SELECT COUNT(*) as consumer_cards_count FROM consumer_cards;
SELECT id, consumer_id, card_number, card_type, status, is_primary
FROM consumer_cards LIMIT 5;

-- operators表数据检查
SELECT 'operators表数据检查' as check_type;
SELECT COUNT(*) as operators_count FROM operators;
SELECT id, username, real_name, role, status, created_at
FROM operators LIMIT 5;

-- ========== 7. 数据一致性检查 ==========

-- 检查是否有users数据未迁移到consumers
SELECT '数据迁移一致性检查' as check_type;

-- 如果users表存在，检查未迁移的门禁用户
SELECT CASE 
    WHEN EXISTS (SELECT 1 FROM sqlite_master WHERE type='table' AND name='users')
    THEN (
        SELECT COUNT(*) 
        FROM users u 
        WHERE u.role > 1  -- 门禁用户
        AND NOT EXISTS (
            SELECT 1 FROM consumers c 
            WHERE c.work_number = u.work_number
        )
    )
    ELSE 0
END as unmigrated_users_count;

-- 检查是否有user_cards数据未迁移到consumer_cards
SELECT CASE 
    WHEN EXISTS (SELECT 1 FROM sqlite_master WHERE type='table' AND name='user_cards')
    THEN (
        SELECT COUNT(*) 
        FROM user_cards uc
        WHERE NOT EXISTS (
            SELECT 1 FROM consumer_cards cc 
            WHERE cc.card_number = uc.card_number
        )
    )
    ELSE 0
END as unmigrated_cards_count;

-- ========== 8. 外键依赖检查 ==========

-- 检查是否有其他表引用user相关表
SELECT '外键依赖检查' as check_type;
SELECT name as table_name, sql as create_sql
FROM sqlite_master 
WHERE type='table' 
AND (sql LIKE '%user_id%' OR sql LIKE '%REFERENCES users%' OR sql LIKE '%REFERENCES user_%')
AND name NOT LIKE '%user%';

-- ========== 9. 生成迁移建议 ==========

SELECT '=== 迁移建议 ===' as recommendation;

SELECT CASE 
    WHEN EXISTS (SELECT 1 FROM sqlite_master WHERE type='table' AND name='users')
         AND (SELECT COUNT(*) FROM users WHERE role > 1) > 0
    THEN '⚠ 发现users表中有未迁移的门禁用户数据，需要先迁移'
    WHEN EXISTS (SELECT 1 FROM sqlite_master WHERE type='table' AND name='user_cards')
         AND (SELECT COUNT(*) FROM user_cards) > 0
    THEN '⚠ 发现user_cards表中有未迁移的卡片数据，需要先迁移'
    WHEN EXISTS (SELECT 1 FROM sqlite_master WHERE type='table' AND name='user_profiles')
         AND (SELECT COUNT(*) FROM user_profiles) > 0
    THEN '⚠ 发现user_profiles表中有扩展信息数据，需要评估是否迁移'
    ELSE '✓ 检查完成，可以安全删除user相关表'
END as migration_status;

-- ========== 10. 最终安全检查清单 ==========

SELECT '=== 删除前安全检查清单 ===' as checklist;
SELECT '1. consumers表记录数: ' || (SELECT COUNT(*) FROM consumers) as check1;
SELECT '2. consumer_cards表记录数: ' || (SELECT COUNT(*) FROM consumer_cards) as check2;
SELECT '3. operators表记录数: ' || (SELECT COUNT(*) FROM operators) as check3;
SELECT '4. 外键完整性检查' as check4;
PRAGMA foreign_key_check; 