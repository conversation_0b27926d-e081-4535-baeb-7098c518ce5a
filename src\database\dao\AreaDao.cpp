#include "AreaDao.h"
#include <QSqlQuery>
#include <QSqlError>
#include <QSqlDatabase>
#include <QDebug>
#include <QVariant>
#include <QDateTime>
#include <QPair>
#include <QFile>
#include <QIODevice>
#include <QTextStream>
#include <QStringConverter>

namespace AccessControl {

AreaDao::AreaDao(std::shared_ptr<IDatabaseProvider> dbProvider)
    : m_dbProvider(dbProvider)
{
}

AreaDao::~AreaDao()
{
}

// ========== 基本CRUD操作 ==========

int AreaDao::createArea(Area& area)
{
    if (!m_dbProvider) {
        qWarning() << "AreaDao: Database provider is null";
        return -1;
    }
    auto validation = validateArea(area);
    if (!validation.first) {
        qWarning() << "AreaDao: Validation failed:" << validation.second;
        return -1;
    }
    if (isCodeExists(area.code())) {
        qWarning() << "AreaDao: Area code already exists:" << area.code();
        return -1;
    }
    QSqlDatabase db = m_dbProvider->getDatabase();
    if (!db.isOpen()) {
        qWarning() << "AreaDao: Database is not open";
        return -1;
    }
    QSqlQuery query(db);
    QString sql = "INSERT INTO areas (area_name, area_code, parent_id, description, enabled, sort_order, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
    query.prepare(sql);
    QDateTime currentTime = QDateTime::currentDateTime();
    query.addBindValue(area.name());
    query.addBindValue(area.code());
    if (area.parentId() <= 0) {
        query.addBindValue(QVariant());
    } else {
        query.addBindValue(area.parentId());
    }
    query.addBindValue(area.description());
    query.addBindValue(static_cast<int>(area.status()));
    query.addBindValue(area.sortOrder());
    query.addBindValue(currentTime);
    query.addBindValue(currentTime);
    if (!query.exec()) {
        qWarning() << "AreaDao: Failed to create area:" << query.lastError().text();
        return -1;
    }
    int areaId = query.lastInsertId().toInt();
    area.setId(areaId);
    updateAreaPaths(areaId);
    return areaId;
}

bool AreaDao::updateArea(const Area& area)
{
    if (!m_dbProvider || area.id() <= 0) {
        return false;
    }
    auto validation = validateArea(area);
    if (!validation.first) {
        qWarning() << "AreaDao: Validation failed:" << validation.second;
        return false;
    }
    if (isCodeExists(area.code(), area.id())) {
        qWarning() << "AreaDao: Area code already exists:" << area.code();
        return false;
    }
    QSqlDatabase db = m_dbProvider->getDatabase();
    if (!db.isOpen()) {
        return false;
    }
    QSqlQuery query(db);
    query.prepare("UPDATE areas SET area_name = ?, area_code = ?, parent_id = ?, description = ?, enabled = ?, sort_order = ?, updated_at = ? WHERE id = ?");
    query.addBindValue(area.name());
    query.addBindValue(area.code());
    if (area.parentId() <= 0) {
        query.addBindValue(QVariant());
    } else {
        query.addBindValue(area.parentId());
    }
    query.addBindValue(area.description());
    query.addBindValue(static_cast<int>(area.status()));
    query.addBindValue(area.sortOrder());
    query.addBindValue(QDateTime::currentDateTime());
    query.addBindValue(area.id());
    if (!query.exec()) {
        qWarning() << "AreaDao: Failed to update area:" << query.lastError().text();
        return false;
    }
    updateAreaPaths(area.id());
    return query.numRowsAffected() > 0;
}

bool AreaDao::deleteArea(int areaId)
{
    if (!m_dbProvider || areaId <= 0) {
        return false;
    }
    if (!canDelete(areaId)) {
        qWarning() << "AreaDao: Cannot delete area, has children or controllers";
        return false;
    }
    QSqlDatabase db = m_dbProvider->getDatabase();
    if (!db.isOpen()) {
        return false;
    }
    QSqlQuery query(db);
    query.prepare("DELETE FROM areas WHERE id = ?");
    query.addBindValue(areaId);
    if (!query.exec()) {
        qWarning() << "AreaDao: Failed to delete area:" << query.lastError().text();
        return false;
    }
    return query.numRowsAffected() > 0;
}

Area AreaDao::findById(int areaId)
{
    Area area;
    if (!m_dbProvider || areaId <= 0) {
        return area;
    }
    QSqlDatabase db = m_dbProvider->getDatabase();
    if (!db.isOpen()) {
        return area;
    }
    QSqlQuery query(db);
    query.prepare("SELECT * FROM areas WHERE id = ?");
    query.addBindValue(areaId);
    if (query.exec() && query.next()) {
        area = buildAreaFromQuery(query);
    }
    return area;
}

Area AreaDao::findByCode(const QString& code)
{
    Area area;
    if (!m_dbProvider) {
        return area;
    }
    QSqlDatabase db = m_dbProvider->getDatabase();
    if (!db.isOpen()) {
        return area;
    }
    QSqlQuery query(db);
    query.prepare("SELECT * FROM areas WHERE area_code = ?");
    query.addBindValue(code);
    if (query.exec() && query.next()) {
        area = buildAreaFromQuery(query);
    }
    return area;
}

QList<Area> AreaDao::findAll()
{
    QList<Area> list;
    if (!m_dbProvider) {
        return list;
    }
    QSqlDatabase db = m_dbProvider->getDatabase();
    if (!db.isOpen()) {
        return list;
    }
    QSqlQuery query(db);
    query.prepare("SELECT * FROM areas ORDER BY sort_order, id");
    if (query.exec()) {
        while (query.next()) {
            list.append(buildAreaFromQuery(query));
        }
    }
    return list;
}

QList<Area> AreaDao::findActive()
{
    QList<Area> list;
    if (!m_dbProvider) {
        return list;
    }
    QSqlDatabase db = m_dbProvider->getDatabase();
    if (!db.isOpen()) {
        return list;
    }
    QSqlQuery query(db);
    query.prepare("SELECT * FROM areas WHERE enabled = 1 ORDER BY sort_order, id");
    if (query.exec()) {
        while (query.next()) {
            list.append(buildAreaFromQuery(query));
        }
    }
    return list;
}

QList<Area> AreaDao::findByParentId(int parentId)
{
    QList<Area> list;
    if (!m_dbProvider) {
        return list;
    }
    QSqlDatabase db = m_dbProvider->getDatabase();
    if (!db.isOpen()) {
        return list;
    }
    QSqlQuery query(db);
    if (parentId <= 0) {
        query.prepare("SELECT * FROM areas WHERE parent_id IS NULL ORDER BY sort_order, id");
    } else {
        query.prepare("SELECT * FROM areas WHERE parent_id = ? ORDER BY sort_order, id");
        query.addBindValue(parentId);
    }
    if (query.exec()) {
        while (query.next()) {
            list.append(buildAreaFromQuery(query));
        }
    }
    return list;
}

QList<Area> AreaDao::findByName(const QString& name)
{
    QList<Area> list;
    if (!m_dbProvider) {
        return list;
    }
    QSqlDatabase db = m_dbProvider->getDatabase();
    if (!db.isOpen()) {
        return list;
    }
    QSqlQuery query(db);
    query.prepare("SELECT * FROM areas WHERE area_name LIKE ? ORDER BY sort_order, id");
    query.addBindValue("%" + name + "%");
    if (query.exec()) {
        while (query.next()) {
            list.append(buildAreaFromQuery(query));
        }
    }
    return list;
}

QList<std::shared_ptr<AreaTreeNode>> AreaDao::getAreaTree()
{
    QList<Area> allAreas = findAll();
    return buildTreeNodes(allAreas, 0);
}

QList<Area> AreaDao::getAllChildren(int areaId)
{
    QList<Area> result;
    QList<Area> allAreas = findAll();
    std::function<void(int)> collectChildren = [&](int parentId) {
        for (const Area& area : allAreas) {
            if (area.parentId() == parentId) {
                result.append(area);
                collectChildren(area.id());
            }
        }
    };
    collectChildren(areaId);
    return result;
}

QList<Area> AreaDao::getAreaPath(int areaId)
{
    QList<Area> path;
    Area current = findById(areaId);
    while (current.id() > 0) {
        path.prepend(current);
        current = findById(current.parentId());
    }
    return path;
}

bool AreaDao::moveArea(int areaId, int newParentId)
{
    if (!m_dbProvider || areaId <= 0) {
        return false;
    }
    QSqlDatabase db = m_dbProvider->getDatabase();
    if (!db.isOpen()) {
        return false;
    }
    QSqlQuery query(db);
    query.prepare("UPDATE areas SET parent_id = ? WHERE id = ?");
    if (newParentId <= 0) {
        query.addBindValue(QVariant());
    } else {
        query.addBindValue(newParentId);
    }
    query.addBindValue(areaId);
    if (!query.exec()) {
        qWarning() << "AreaDao: Failed to move area:" << query.lastError().text();
        return false;
    }
    updateAreaPaths(areaId);
    return query.numRowsAffected() > 0;
}

bool AreaDao::isCodeExists(const QString& code, int excludeId)
{
    if (!m_dbProvider) {
        return false;
    }
    QSqlDatabase db = m_dbProvider->getDatabase();
    if (!db.isOpen()) {
        return false;
    }
    QSqlQuery query(db);
    if (excludeId > 0) {
        query.prepare("SELECT COUNT(*) FROM areas WHERE area_code = ? AND id != ?");
        query.addBindValue(code);
        query.addBindValue(excludeId);
    } else {
        query.prepare("SELECT COUNT(*) FROM areas WHERE area_code = ?");
        query.addBindValue(code);
    }
    if (query.exec() && query.next()) {
        return query.value(0).toInt() > 0;
    }
    return false;
}

bool AreaDao::canDelete(int areaId)
{
    if (!m_dbProvider || areaId <= 0) {
        return false;
    }
    QSqlDatabase db = m_dbProvider->getDatabase();
    if (!db.isOpen()) {
        return false;
    }
    QSqlQuery query(db);
    // 检查是否有子区域
    query.prepare("SELECT COUNT(*) FROM areas WHERE parent_id = ?");
    query.addBindValue(areaId);
    if (query.exec() && query.next() && query.value(0).toInt() > 0) {
        return false;
    }
    // 检查是否有关联控制器（假设controllers表有area_id字段）
    QSqlQuery ctrlQuery(db);
    ctrlQuery.prepare("SELECT COUNT(*) FROM controllers WHERE area_id = ?");
    ctrlQuery.addBindValue(areaId);
    if (ctrlQuery.exec() && ctrlQuery.next() && ctrlQuery.value(0).toInt() > 0) {
        return false;
    }
    return true;
}

AreaDao::AreaStats AreaDao::getAreaStats(int areaId)
{
    AreaStats stats{0, 0, 0};
    if (!m_dbProvider || areaId <= 0) {
        return stats;
    }
    QSqlDatabase db = m_dbProvider->getDatabase();
    if (!db.isOpen()) {
        return stats;
    }
    QSqlQuery query(db);
    // 子区域数量
    query.prepare("SELECT COUNT(*) FROM areas WHERE parent_id = ?");
    query.addBindValue(areaId);
    if (query.exec() && query.next()) {
        stats.childCount = query.value(0).toInt();
    }
    // 直接控制器数量
    QSqlQuery ctrlQuery(db);
    ctrlQuery.prepare("SELECT COUNT(*) FROM controllers WHERE area_id = ?");
    ctrlQuery.addBindValue(areaId);
    if (ctrlQuery.exec() && ctrlQuery.next()) {
        stats.directControllerCount = ctrlQuery.value(0).toInt();
    }
    // 总控制器数量（包括所有子区域）
    QList<Area> allChildren = getAllChildren(areaId);
    stats.totalControllerCount = stats.directControllerCount;
    for (const Area& child : allChildren) {
        QSqlQuery childCtrlQuery(db);
        childCtrlQuery.prepare("SELECT COUNT(*) FROM controllers WHERE area_id = ?");
        childCtrlQuery.addBindValue(child.id());
        if (childCtrlQuery.exec() && childCtrlQuery.next()) {
            stats.totalControllerCount += childCtrlQuery.value(0).toInt();
        }
    }
    return stats;
}

bool AreaDao::updateAreaPaths(int areaId)
{
    // 这里可实现区域路径和层级的批量更新，暂略实现
    return true;
}

int AreaDao::batchImport(const QList<Area>& areas)
{
    int count = 0;
    for (Area area : areas) {
        if (isCodeExists(area.code())) {
            updateArea(area);
        } else {
            createArea(area);
        }
        ++count;
    }
    return count;
}

bool AreaDao::exportAreas(const QString& format, const QString& filePath)
{
    // 这里只做CSV导出示例
    if (format.toLower() != "csv") return false;
    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) return false;
    QTextStream out(&file);
    out.setEncoding(QStringConverter::Utf8);
    // 写入UTF-8 BOM以确保Excel正确识别编码
    out << "\xEF\xBB\xBF";
    out << "区域名称,区域代码,上级区域代码,描述,状态,排序,创建时间,更新时间\n";
    QList<Area> areas = findAll();
    for (const Area& area : areas) {
        out << area.name() << ","
            << area.code() << ","
            << (area.parentId() > 0 ? findById(area.parentId()).code() : "") << ","
            << area.description() << ","
            << (area.status() == Area::Active ? "启用" : "禁用") << ","
            << area.sortOrder() << ","
            << area.createdAt().toString("yyyy-MM-dd HH:mm:ss") << ","
            << area.updatedAt().toString("yyyy-MM-dd HH:mm:ss") << "\n";
    }
    file.close();
    return true;
}

Area AreaDao::buildAreaFromQuery(const QSqlQuery& query)
{
    Area area;
    area.setId(query.value("id").toInt());
    area.setName(query.value("area_name").toString());
    area.setCode(query.value("area_code").toString());
    area.setParentId(query.value("parent_id").isNull() ? 0 : query.value("parent_id").toInt());
    area.setDescription(query.value("description").toString());
    area.setStatus(static_cast<Area::Status>(query.value("enabled").toInt()));
    area.setSortOrder(query.value("sort_order").toInt());
    area.setCreatedAt(query.value("created_at").toDateTime());
    area.setUpdatedAt(query.value("updated_at").toDateTime());
    // 这里可补充level和fullPath等
    return area;
}

QList<std::shared_ptr<AreaTreeNode>> AreaDao::buildTreeNodes(const QList<Area>& areas, int parentId)
{
    QList<std::shared_ptr<AreaTreeNode>> nodes;
    for (const Area& area : areas) {
        if (area.parentId() == parentId) {
            auto node = std::make_shared<AreaTreeNode>(area);
            node->children = buildTreeNodes(areas, area.id());
            nodes.append(node);
        }
    }
    return nodes;
}

void AreaDao::calculateAreaPath(int areaId, QList<Area>& pathList)
{
    Area current = findById(areaId);
    if (current.id() > 0) {
        calculateAreaPath(current.parentId(), pathList);
        pathList.append(current);
    }
}

QPair<bool, QString> AreaDao::validateArea(const Area& area)
{
    if (area.name().trimmed().isEmpty()) return {false, "区域名称不能为空"};
    if (area.code().trimmed().isEmpty()) return {false, "区域代码不能为空"};
    if (area.code().length() > 20) return {false, "区域代码不能超过20字符"};
    if (area.name().length() > 100) return {false, "区域名称不能超过100字符"};
    if (area.id() != 0 && area.parentId() == area.id()) return {false, "上级区域不能为自身"};
    return {true, ""};
}

} // namespace AccessControl 