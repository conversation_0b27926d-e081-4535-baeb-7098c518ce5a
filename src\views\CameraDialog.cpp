#include "CameraDialog.h"
#include <QLabel>
#include <QMessageBox>
#include <QBuffer>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QCamera>
#include <QMediaCaptureSession>
#include <QImageCapture>
#include <QVideoWidget>
#include <QMediaDevices>
#include <QCameraDevice>
#include <QComboBox>
#include <QPushButton>
#include <QCloseEvent>
#include <QImage>
#include <QPixmap>

namespace AccessControl {

// 新增：证件照裁剪函数
static QImage cropToIDPhoto(const QImage &src, int targetW, int targetH) {
    double targetRatio = double(targetW) / targetH;
    int w = src.width();
    int h = src.height();
    double srcRatio = double(w) / h;
    int cropW, cropH, x, y;
    if (srcRatio > targetRatio) {
        cropH = h;
        cropW = int(h * targetRatio);
        x = (w - cropW) / 2;
        y = 0;
    } else {
        cropW = w;
        cropH = int(w / targetRatio);
        x = 0;
        y = (h - cropH) / 2;
    }
    return src.copy(x, y, cropW, cropH).scaled(targetW, targetH, Qt::KeepAspectRatio, Qt::SmoothTransformation);
}

CameraDialog::CameraDialog(QWidget *parent)
    : QDialog(parent)
    , m_camera(nullptr)
    , m_captureSession(nullptr)
    , m_imageCapture(nullptr)
    , m_viewfinder(nullptr)
    , m_imageLabel(nullptr)
    , m_cameraCombo(nullptr)
    , m_captureButton(nullptr)
    , m_acceptButton(nullptr)
    , m_retakeButton(nullptr)
    , m_cancelButton(nullptr)
    , m_isImageCaptured(false)
{
    setWindowTitle(tr("相机拍照"));
    setModal(true);
    resize(640, 480);
    initUI();
    initCamera();
}

CameraDialog::~CameraDialog()
{
    if (m_camera) {
        m_camera->stop();
    }
}

void CameraDialog::initUI()
{
    QVBoxLayout *mainLayout = new QVBoxLayout(this);
    // 证件照比例 3:4，推荐尺寸300x400
    const int idPhotoW = 300;
    const int idPhotoH = 400;

    // 顶部相机选择
    m_cameras = QMediaDevices::videoInputs();
    if (m_cameras.size() > 1) {
        QHBoxLayout *topLayout = new QHBoxLayout();
        m_cameraCombo = new QComboBox(this);
        for (const QCameraDevice &device : m_cameras) {
            m_cameraCombo->addItem(device.description());
        }
        connect(m_cameraCombo, QOverload<int>::of(&QComboBox::currentIndexChanged),
                this, &CameraDialog::switchCamera);
        topLayout->addWidget(new QLabel(tr("选择相机:"), this));
        topLayout->addWidget(m_cameraCombo);
        topLayout->addStretch();
        mainLayout->addLayout(topLayout);
    }

    // 预览窗口（3:4比例）
    m_viewfinder = new QVideoWidget(this);
    m_viewfinder->setFixedSize(idPhotoW, idPhotoH);
    mainLayout->addWidget(m_viewfinder, 0, Qt::AlignHCenter);

    // 拍摄图片显示区域（3:4比例）
    m_imageLabel = new QLabel(this);
    m_imageLabel->setFixedSize(idPhotoW, idPhotoH);
    m_imageLabel->setAlignment(Qt::AlignCenter);
    m_imageLabel->setStyleSheet("QLabel { border: 1px solid gray; background-color: black; }");
    m_imageLabel->setScaledContents(false); // 不拉伸
    m_imageLabel->hide();
    mainLayout->addWidget(m_imageLabel, 0, Qt::AlignHCenter);

    // 按钮区
    QHBoxLayout *buttonLayout = new QHBoxLayout();
    m_captureButton = new QPushButton(tr("拍照"), this);
    connect(m_captureButton, &QPushButton::clicked, this, &CameraDialog::captureImage);
    buttonLayout->addWidget(m_captureButton);

    m_acceptButton = new QPushButton(tr("确认"), this);
    m_acceptButton->hide();
    connect(m_acceptButton, &QPushButton::clicked, this, &CameraDialog::acceptPhoto);
    buttonLayout->addWidget(m_acceptButton);

    m_retakeButton = new QPushButton(tr("重拍"), this);
    m_retakeButton->hide();
    connect(m_retakeButton, &QPushButton::clicked, this, &CameraDialog::retakePhoto);
    buttonLayout->addWidget(m_retakeButton);

    buttonLayout->addStretch();

    m_cancelButton = new QPushButton(tr("取消"), this);
    connect(m_cancelButton, &QPushButton::clicked, this, &CameraDialog::cancelPhoto);
    buttonLayout->addWidget(m_cancelButton);

    mainLayout->addLayout(buttonLayout);
}

void CameraDialog::initCamera()
{
    m_cameras = QMediaDevices::videoInputs();
    if (m_cameras.isEmpty()) {
        // 没有相机，显示提示并禁用拍照
        QLabel *noCamera = new QLabel(tr("未检测到相机设备"), this);
        noCamera->setAlignment(Qt::AlignCenter);
        noCamera->setStyleSheet("QLabel { color: #999; font-size: 16px; }");
        m_viewfinder->hide();
        layout()->addWidget(noCamera);
        m_captureButton->setEnabled(false);
        return;
    }
    // 创建会话和拍照对象
    m_captureSession = new QMediaCaptureSession(this);
    m_imageCapture = new QImageCapture(this);
    m_captureSession->setImageCapture(m_imageCapture);
    m_captureSession->setVideoOutput(m_viewfinder);
    connect(m_imageCapture, &QImageCapture::imageCaptured, this, &CameraDialog::onImageCaptured);
    connect(m_imageCapture, &QImageCapture::errorOccurred, this, &CameraDialog::onCaptureError);
    setCamera(m_cameras.first());
}

void CameraDialog::setCamera(const QCameraDevice &device)
{
    if (m_camera) {
        m_camera->stop();
        delete m_camera;
        m_camera = nullptr;
    }
    m_camera = new QCamera(device, this);
    connect(m_camera, &QCamera::errorOccurred, this, &CameraDialog::onCameraError);
    m_captureSession->setCamera(m_camera);
    m_camera->start();
    showPreview();
}

void CameraDialog::switchCamera()
{
    int idx = m_cameraCombo ? m_cameraCombo->currentIndex() : 0;
    if (idx >= 0 && idx < m_cameras.size()) {
        setCamera(m_cameras[idx]);
    }
}

void CameraDialog::captureImage()
{
    if (m_imageCapture && m_imageCapture->isReadyForCapture()) {
        m_captureButton->setEnabled(false);
        m_imageCapture->capture();
    }
}

void CameraDialog::onImageCaptured(int, const QImage &image)
{
    // 拍照后自动裁剪为证件照比例
    const int idPhotoW = 300;
    const int idPhotoH = 400;
    m_capturedImage = cropToIDPhoto(image, idPhotoW, idPhotoH);
    m_isImageCaptured = true;
    showCapturedImage();
    m_captureButton->setEnabled(true);
}

void CameraDialog::acceptPhoto()
{
    if (!m_capturedImage.isNull()) {
        QBuffer buffer(&m_capturedImageData);
        buffer.open(QIODevice::WriteOnly);
        // 保存裁剪后的证件照
        m_capturedImage.save(&buffer, "JPEG", 90);
        emit imageCaptured(m_capturedImageData);
        accept();
    }
}

void CameraDialog::retakePhoto()
{
    m_isImageCaptured = false;
    showPreview();
    m_captureButton->setEnabled(true);
}

void CameraDialog::cancelPhoto()
{
    reject();
}

void CameraDialog::onCameraError()
{
    QMessageBox::warning(this, tr("相机错误"), tr("相机出现错误，请检查设备连接"));
    m_captureButton->setEnabled(false);
}

void CameraDialog::onCaptureError(int, QImageCapture::Error, const QString &errorString)
{
    QMessageBox::warning(this, tr("拍照错误"), errorString);
    m_captureButton->setEnabled(true);
}

void CameraDialog::showPreview()
{
    m_viewfinder->show();
    m_imageLabel->hide();
    m_captureButton->show();
    m_acceptButton->hide();
    m_retakeButton->hide();
}

void CameraDialog::showCapturedImage()
{
    if (!m_capturedImage.isNull()) {
        QPixmap pixmap = QPixmap::fromImage(m_capturedImage);
        // 不拉伸，居中显示
        QPixmap scaledPixmap = pixmap.scaled(m_imageLabel->size(), Qt::KeepAspectRatio, Qt::SmoothTransformation);
        m_imageLabel->setPixmap(scaledPixmap);
    }
    m_viewfinder->hide();
    m_imageLabel->show();
    m_captureButton->hide();
    m_acceptButton->show();
    m_retakeButton->show();
}

QByteArray CameraDialog::getCapturedImageData() const
{
    return m_capturedImageData;
}

void CameraDialog::closeEvent(QCloseEvent *event)
{
    if (m_camera) {
        m_camera->stop();
    }
    event->accept();
}

} // namespace AccessControl 