#include "OperatorDialog.h"

#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QFormLayout>
#include <QMessageBox>
#include <QCloseEvent>
#include <QLabel>

#include "../database/DatabaseFactory.h"
#include "../models/Operator.h"

namespace AccessControl {

OperatorDialog::OperatorDialog(QWidget *parent, Mode mode, int operatorId)
    : QDialog(parent)
    , m_mode(mode)
    , m_operatorId(operatorId)
    , m_usernameEdit(nullptr)
    , m_passwordEdit(nullptr)
    , m_confirmPasswordEdit(nullptr)
    , m_realNameEdit(nullptr)
    , m_isActiveCheck(nullptr)
    , m_saveButton(nullptr)
    , m_cancelButton(nullptr)
{
    // 设置窗口属性
    setWindowTitle(m_mode == Mode::Add ? tr("添加操作员") : tr("编辑操作员"));
    setFixedSize(400, 300);
    setWindowFlags(windowFlags() & ~Qt::WindowContextHelpButtonHint);
    
    // 初始化UI
    initUI();
    
    // 如果是编辑模式，填充操作员数据
    if (m_mode == Mode::Edit && m_operatorId > 0) {
        fillOperatorData();
    }
}

OperatorDialog::~OperatorDialog()
{
    // 析构函数内容
}

void OperatorDialog::closeEvent(QCloseEvent *event)
{
    // 关闭前确认
    if (QMessageBox::question(this, tr("确认"), tr("确定要关闭窗口吗？未保存的数据将丢失。"),
                             QMessageBox::Yes | QMessageBox::No) == QMessageBox::Yes) {
        event->accept();
    } else {
        event->ignore();
    }
}

void OperatorDialog::initUI()
{
    // 创建主布局
    QVBoxLayout *mainLayout = new QVBoxLayout(this);
    
    // 创建表单布局
    QFormLayout *formLayout = new QFormLayout();
    formLayout->setFieldGrowthPolicy(QFormLayout::AllNonFixedFieldsGrow);
    
    // 用户名
    m_usernameEdit = new QLineEdit(this);
    m_usernameEdit->setPlaceholderText(tr("请输入用户名"));
    formLayout->addRow(tr("用户名:"), m_usernameEdit);
    
    // 密码
    m_passwordEdit = new QLineEdit(this);
    m_passwordEdit->setEchoMode(QLineEdit::Password);
    m_passwordEdit->setPlaceholderText(tr("请输入密码"));
    formLayout->addRow(tr("密码:"), m_passwordEdit);
    
    // 确认密码
    m_confirmPasswordEdit = new QLineEdit(this);
    m_confirmPasswordEdit->setEchoMode(QLineEdit::Password);
    m_confirmPasswordEdit->setPlaceholderText(tr("请再次输入密码"));
    formLayout->addRow(tr("确认密码:"), m_confirmPasswordEdit);
    
    // 真实姓名
    m_realNameEdit = new QLineEdit(this);
    m_realNameEdit->setPlaceholderText(tr("请输入真实姓名"));
    formLayout->addRow(tr("真实姓名:"), m_realNameEdit);
    
    // 是否启用
    m_isActiveCheck = new QCheckBox(tr("启用"), this);
    m_isActiveCheck->setChecked(true);
    formLayout->addRow(tr("状态:"), m_isActiveCheck);
    
    // 添加表单布局到主布局
    mainLayout->addLayout(formLayout);
    
    // 添加一些垂直空间
    mainLayout->addStretch();
    
    // 创建按钮布局
    QHBoxLayout *buttonLayout = new QHBoxLayout();
    buttonLayout->addStretch();
    
    // 创建保存按钮
    m_saveButton = new QPushButton(tr("保存"), this);
    m_saveButton->setDefault(true);
    connect(m_saveButton, &QPushButton::clicked, this, &OperatorDialog::saveOperator);
    
    // 创建取消按钮
    m_cancelButton = new QPushButton(tr("取消"), this);
    connect(m_cancelButton, &QPushButton::clicked, this, &OperatorDialog::cancel);
    
    // 添加按钮到按钮布局
    buttonLayout->addWidget(m_saveButton);
    buttonLayout->addWidget(m_cancelButton);
    
    // 添加按钮布局到主布局
    mainLayout->addLayout(buttonLayout);
    
    // 设置主布局
    setLayout(mainLayout);
    
    // 设置Tab顺序
    setTabOrder(m_usernameEdit, m_passwordEdit);
    setTabOrder(m_passwordEdit, m_confirmPasswordEdit);
    setTabOrder(m_confirmPasswordEdit, m_realNameEdit);
    setTabOrder(m_realNameEdit, m_isActiveCheck);
    setTabOrder(m_isActiveCheck, m_saveButton);
    setTabOrder(m_saveButton, m_cancelButton);
}

void OperatorDialog::fillOperatorData()
{
    // TODO: 从数据库加载操作员数据
    // 这里是示例代码，实际应从数据库加载
    
    m_usernameEdit->setText("admin");
    m_realNameEdit->setText("管理员");
    m_isActiveCheck->setChecked(true);
    
    // 编辑模式下禁用用户名编辑
    m_usernameEdit->setReadOnly(true);
    
    // 编辑模式下密码字段为可选
    m_passwordEdit->setPlaceholderText(tr("留空则不修改密码"));
    m_confirmPasswordEdit->setPlaceholderText(tr("留空则不修改密码"));
}

void OperatorDialog::saveOperator()
{
    // 验证表单
    if (!validateForm()) {
        return;
    }
    
    // 保存操作员数据
    if (saveOperatorToDatabase()) {
        QMessageBox::information(this, tr("成功"), tr("操作员信息保存成功！"));
        accept();
    } else {
        QMessageBox::critical(this, tr("错误"), tr("保存操作员信息失败，请重试！"));
    }
}

void OperatorDialog::cancel()
{
    reject();
}

bool OperatorDialog::validateForm()
{
    // 验证用户名
    if (m_usernameEdit->text().trimmed().isEmpty()) {
        QMessageBox::warning(this, tr("警告"), tr("请输入用户名！"));
        m_usernameEdit->setFocus();
        return false;
    }
    
    // 验证真实姓名
    if (m_realNameEdit->text().trimmed().isEmpty()) {
        QMessageBox::warning(this, tr("警告"), tr("请输入真实姓名！"));
        m_realNameEdit->setFocus();
        return false;
    }
    
    // 新增模式下验证密码
    if (m_mode == Mode::Add) {
        if (m_passwordEdit->text().isEmpty()) {
            QMessageBox::warning(this, tr("警告"), tr("请输入密码！"));
            m_passwordEdit->setFocus();
        return false;
    }
    
        if (m_passwordEdit->text().length() < 4) {
            QMessageBox::warning(this, tr("警告"), tr("密码长度不能少于4位！"));
            m_passwordEdit->setFocus();
        return false;
    }
    
        if (m_passwordEdit->text() != m_confirmPasswordEdit->text()) {
            QMessageBox::warning(this, tr("警告"), tr("两次输入的密码不一致！"));
            m_confirmPasswordEdit->setFocus();
        return false;
    }
    } else {
        // 编辑模式下，如果输入了密码则验证
        if (!m_passwordEdit->text().isEmpty()) {
            if (m_passwordEdit->text().length() < 4) {
                QMessageBox::warning(this, tr("警告"), tr("密码长度不能少于4位！"));
                m_passwordEdit->setFocus();
        return false;
    }
    
            if (m_passwordEdit->text() != m_confirmPasswordEdit->text()) {
                QMessageBox::warning(this, tr("警告"), tr("两次输入的密码不一致！"));
                m_confirmPasswordEdit->setFocus();
        return false;
            }
        }
    }
    
    return true;
}

bool OperatorDialog::saveOperatorToDatabase()
{
    // TODO: 实现数据库保存逻辑
    // 这里是示例代码，实际应保存到数据库
    
    QString username = m_usernameEdit->text().trimmed();
    QString password = m_passwordEdit->text();
    QString realName = m_realNameEdit->text().trimmed();
    bool isActive = m_isActiveCheck->isChecked();
    
    qDebug() << "保存操作员信息:";
    qDebug() << "用户名:" << username;
    qDebug() << "真实姓名:" << realName;
    qDebug() << "是否启用:" << isActive;
    qDebug() << "密码长度:" << password.length();
    
    // 这里应该调用OperatorDao来保存数据
    // 暂时返回true表示保存成功
    return true;
}

} // namespace AccessControl 