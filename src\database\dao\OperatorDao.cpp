#include "OperatorDao.h"
#include <QSqlQuery>
#include <QSqlError>
#include <QVariant>
#include <QDebug>

namespace AccessControl {

OperatorDao::OperatorDao(DatabaseProviderPtr provider) : m_provider(provider) {}

bool OperatorDao::create(Operator& user) {
    QSqlQuery query = m_provider->prepareQuery(
        "INSERT INTO operators (username, password_hash, salt, role, status, real_name, email, "
        "phone_number, id_number, department_id, work_number) "
        "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
    
    bindOperatorToQuery(query, user, false);

    if (query.exec()) {
        user.setId(m_provider->lastInsertId().toInt());
        return true;
    }
    
    qDebug() << "UserDao::create failed:" << query.lastError().text();
    return false;
}

std::optional<Operator> OperatorDao::getById(int id) {
    QSqlQuery query = m_provider->prepareQuery("SELECT * FROM operators WHERE id = ?");
    query.addBindValue(id);
    
    if (query.exec() && query.next()) {
        return mapRowToOperator(query);
    }
    return std::nullopt;
}

std::optional<Operator> OperatorDao::getByUsername(const QString& username) {
    QSqlQuery query = m_provider->prepareQuery("SELECT * FROM operators WHERE username = ?");
    query.addBindValue(username);
    
    if (query.exec() && query.next()) {
        return mapRowToOperator(query);
    }
    return std::nullopt;
}

bool OperatorDao::update(const Operator& user) {
    QSqlQuery query = m_provider->prepareQuery(
        "UPDATE operators SET username = ?, password_hash = ?, salt = ?, role = ?, status = ?, "
        "real_name = ?, email = ?, phone_number = ?, id_number = ?, department_id = ?, "
        "work_number = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?");
    
    bindOperatorToQuery(query, user, true);
    
    if (query.exec()) {
        return true;
    }
    
    qDebug() << "UserDao::update failed:" << query.lastError().text();
    return false;
}

bool OperatorDao::remove(int id) {
    QSqlQuery query = m_provider->prepareQuery("DELETE FROM operators WHERE id = ?");
    query.addBindValue(id);
    return query.exec();
}

bool OperatorDao::authenticateUser(const QString& username, const QString& password, Operator& user) {
    auto userOpt = getByUsername(username);
    if (userOpt && userOpt->checkPassword(password)) {
        // 检查用户状态是否有效
        if (userOpt->isValid()) {
            user = *userOpt;
            return true;
        } else {
            qDebug() << "UserDao::authenticateUser: User is not valid - status:" << static_cast<int>(userOpt->status())
                     << "isInValidPeriod:" << userOpt->isInValidPeriod();
        }
    } else {
        qDebug() << "UserDao::authenticateUser: Password check failed for user:" << username;
    }
    return false;
}

bool OperatorDao::updateLastLogin(int userId, const QDateTime& lastLoginAt, const QString& lastLoginIp) {
    QSqlQuery query = m_provider->prepareQuery(
        "UPDATE operators SET last_login_at = ?, last_login_ip = ?, login_attempts = 0 WHERE id = ?");
    query.addBindValue(lastLoginAt);
    query.addBindValue(lastLoginIp);
    query.addBindValue(userId);
    return query.exec();
}

std::vector<Operator> OperatorDao::getAllUsers() {
    std::vector<Operator> users;
    QSqlQuery query = m_provider->prepareQuery("SELECT * FROM operators ORDER BY id");

    qDebug() << "UserDao::getAllUsers: Executing query...";
    if (query.exec()) {
        int count = 0;
        while (query.next()) {
            Operator user = mapRowToOperator(query);
            users.push_back(user);
            count++;
            qDebug() << "UserDao::getAllUsers: Found user" << count << ":" << user.Operatorname() << user.realName();
        }
        qDebug() << "UserDao::getAllUsers: Total users found:" << count;
    } else {
        qDebug() << "UserDao::getAllUsers: Query failed:" << query.lastError().text();
    }
    return users;
}

std::vector<Operator> OperatorDao::getUsersByDepartment(int departmentId) {
    std::vector<Operator> users;
    QSqlQuery query = m_provider->prepareQuery("SELECT * FROM operators WHERE department_id = ? ORDER BY real_name");
    query.addBindValue(departmentId);
    
    if (query.exec()) {
        while (query.next()) {
            users.push_back(mapRowToOperator(query));
        }
    }
    return users;
}

std::vector<Operator> OperatorDao::searchUsers(const QString& searchTerm) {
    std::vector<Operator> users;
    QString sql = "SELECT * FROM operators WHERE "
                  "real_name LIKE ? OR username LIKE ? OR work_number LIKE ? OR "
                  "phone_number LIKE ? OR id_number LIKE ? "
                  "ORDER BY real_name";
    
    QSqlQuery query = m_provider->prepareQuery(sql);
    QString term = "%" + searchTerm + "%";
    query.addBindValue(term);
    query.addBindValue(term);
    query.addBindValue(term);
    query.addBindValue(term);
    query.addBindValue(term);
    
    if (query.exec()) {
        while (query.next()) {
            users.push_back(mapRowToOperator(query));
        }
    }
    return users;
}

std::vector<Operator> OperatorDao::searchUsersByName(const QString& name) {
    std::vector<Operator> users;
    QSqlQuery query = m_provider->prepareQuery(
        "SELECT * FROM operators WHERE real_name LIKE ? OR username LIKE ? ORDER BY real_name");
    QString term = "%" + name + "%";
    query.addBindValue(term);
    query.addBindValue(term);
    
    if (query.exec()) {
        while (query.next()) {
            users.push_back(mapRowToOperator(query));
        }
    }
    return users;
}

std::vector<Operator> OperatorDao::searchUsersByCard(const QString& cardNumber) {
    std::vector<Operator> users;
    QSqlQuery query = m_provider->prepareQuery(
        "SELECT * FROM operators WHERE work_number LIKE ? ORDER BY real_name");
    QString term = "%" + cardNumber + "%";
    query.addBindValue(term);
    
    if (query.exec()) {
        while (query.next()) {
            users.push_back(mapRowToOperator(query));
        }
    }
    return users;
}

std::vector<Operator> OperatorDao::getUsersWithAdvancedFilter(const QString& name, const QString& cardNumber, int departmentId) {
    std::vector<Operator> users;
    QString sql = "SELECT * FROM operators WHERE 1=1";
    QStringList conditions;
    
    if (!name.isEmpty()) {
        sql += " AND (real_name LIKE ? OR username LIKE ?)";
    }
    if (!cardNumber.isEmpty()) {
        sql += " AND work_number LIKE ?";
    }
    if (departmentId > 0) {
        sql += " AND department_id = ?";
    }
    sql += " ORDER BY real_name";
    
    QSqlQuery query = m_provider->prepareQuery(sql);
    
    if (!name.isEmpty()) {
        QString nameTerm = "%" + name + "%";
        query.addBindValue(nameTerm);
        query.addBindValue(nameTerm);
    }
    if (!cardNumber.isEmpty()) {
        query.addBindValue("%" + cardNumber + "%");
    }
    if (departmentId > 0) {
        query.addBindValue(departmentId);
    }
    
    if (query.exec()) {
        while (query.next()) {
            users.push_back(mapRowToOperator(query));
        }
    }
    return users;
}

bool OperatorDao::isUsernameExists(const QString& username, int excludeId) {
    QString sql = "SELECT COUNT(*) FROM operators WHERE username = ?";
    if (excludeId != -1) {
        sql += " AND id != ?";
    }
    
    QSqlQuery query = m_provider->prepareQuery(sql);
    query.addBindValue(username);
    if (excludeId != -1) {
        query.addBindValue(excludeId);
    }
    
    if (query.exec() && query.next()) {
        return query.value(0).toInt() > 0;
    }
    return false;
}

bool OperatorDao::isPhoneNumberExists(const QString& phoneNumber, int excludeId) {
    if (phoneNumber.isEmpty()) return false;
    
    QString sql = "SELECT COUNT(*) FROM operators WHERE phone_number = ?";
    if (excludeId != -1) {
        sql += " AND id != ?";
    }
    
    QSqlQuery query = m_provider->prepareQuery(sql);
    query.addBindValue(phoneNumber);
    if (excludeId != -1) {
        query.addBindValue(excludeId);
    }
    
    if (query.exec() && query.next()) {
        return query.value(0).toInt() > 0;
    }
    return false;
}

bool OperatorDao::isIdNumberExists(const QString& idNumber, int excludeId) {
    if (idNumber.isEmpty()) return false;
    
    QString sql = "SELECT COUNT(*) FROM operators WHERE id_number = ?";
    if (excludeId != -1) {
        sql += " AND id != ?";
    }
    
    QSqlQuery query = m_provider->prepareQuery(sql);
    query.addBindValue(idNumber);
    if (excludeId != -1) {
        query.addBindValue(excludeId);
    }
    
    if (query.exec() && query.next()) {
        return query.value(0).toInt() > 0;
    }
    return false;
}

bool OperatorDao::isWorkNumberExists(const QString& workNumber, int excludeId) {
    if (workNumber.isEmpty()) return false;
    
    QString sql = "SELECT COUNT(*) FROM operators WHERE work_number = ?";
    if (excludeId != -1) {
        sql += " AND id != ?";
    }
    
    QSqlQuery query = m_provider->prepareQuery(sql);
    query.addBindValue(workNumber);
    if (excludeId != -1) {
        query.addBindValue(excludeId);
    }
    
    if (query.exec() && query.next()) {
        return query.value(0).toInt() > 0;
    }
    return false;
}

int OperatorDao::getTotalUserCount() {
    QSqlQuery query = m_provider->prepareQuery("SELECT COUNT(*) FROM operators");
    if (query.exec() && query.next()) {
        return query.value(0).toInt();
    }
    return 0;
}

int OperatorDao::getActiveUserCount() {
    QSqlQuery query = m_provider->prepareQuery("SELECT COUNT(*) FROM operators WHERE status = ?");
    query.addBindValue(static_cast<int>(Operator::Status::Active));
    if (query.exec() && query.next()) {
        return query.value(0).toInt();
    }
    return 0;
}

int OperatorDao::getUserCountByDepartment(int departmentId) {
    QSqlQuery query = m_provider->prepareQuery("SELECT COUNT(*) FROM operators WHERE department_id = ?");
    query.addBindValue(departmentId);
    if (query.exec() && query.next()) {
        return query.value(0).toInt();
    }
    return 0;
}

bool OperatorDao::batchCreate(const std::vector<Operator>& users) {
    if (users.empty()) return true;
    
    QSqlQuery query = m_provider->prepareQuery(
        "INSERT INTO operators (username, password_hash, salt, role, status, real_name, email, "
        "phone_number, id_number, access_enabled, attendance_enabled, valid_from, valid_until, "
        "department_id, work_number, shift_work, created_at, updated_at) "
        "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
    
    for (const auto& user : users) {
        bindOperatorToQuery(query, user, false);
        if (!query.exec()) {
            qDebug() << "UserDao::batchCreate failed for user:" << user.Operatorname() << query.lastError().text();
            return false;
        }
    }
    return true;
}

bool OperatorDao::batchUpdate(const std::vector<Operator>& users) {
    if (users.empty()) return true;
    
    QSqlQuery query = m_provider->prepareQuery(
        "UPDATE operators SET username = ?, password_hash = ?, salt = ?, role = ?, status = ?, "
        "real_name = ?, email = ?, phone_number = ?, id_number = ?, access_enabled = ?, "
        "attendance_enabled = ?, valid_from = ?, valid_until = ?, department_id = ?, "
        "work_number = ?, shift_work = ?, updated_at = ? WHERE id = ?");
    
    for (const auto& user : users) {
        bindOperatorToQuery(query, user, true);
        if (!query.exec()) {
            qDebug() << "UserDao::batchUpdate failed for user:" << user.Operatorname() << query.lastError().text();
            return false;
        }
    }
    return true;
}

bool OperatorDao::batchDelete(const std::vector<int>& userIds) {
    if (userIds.empty()) return true;
    
    QSqlQuery query = m_provider->prepareQuery("DELETE FROM operators WHERE id = ?");
    for (int id : userIds) {
        query.addBindValue(id);
        if (!query.exec()) {
            qDebug() << "UserDao::batchDelete failed for user id:" << id << query.lastError().text();
            return false;
        }
    }
    return true;
}

// 辅助方法实现
Operator OperatorDao::mapRowToOperator(const QSqlQuery& query) {
            Operator user;
            user.setId(query.value("id").toInt());
            user.setOperatorname(query.value("username").toString());
            user.setPasswordHash(query.value("password_hash").toString());
            user.setSalt(query.value("salt").toString());
            user.setRole(static_cast<Operator::Role>(query.value("role").toInt()));
            user.setStatus(static_cast<Operator::Status>(query.value("status").toInt()));
            user.setRealName(query.value("real_name").toString());
            user.setEmail(query.value("email").toString());
            user.setCreatedAt(query.value("created_at").toDateTime());
            user.setUpdatedAt(query.value("updated_at").toDateTime());
            user.setLastLoginAt(query.value("last_login_at").toDateTime());
            user.setLastLoginIp(query.value("last_login_ip").toString());
            user.setLoginAttempts(query.value("login_attempts").toInt());
    
    // 扩展字段（只查询存在的字段）
    user.setPhoneNumber(query.value("phone_number").toString());
    user.setIdNumber(query.value("id_number").toString());
    user.setDepartmentId(query.value("department_id").toInt());
    user.setWorkNumber(query.value("work_number").toString());
    
    // 为不存在的字段设置默认值
    user.setAccessEnabled(true);           // 默认启用门禁
    user.setAttendanceEnabled(true);       // 默认启用考勤  
    user.setValidFrom(QDate::currentDate()); // 默认从今天开始有效
    user.setValidUntil(QDate(2099, 12, 31)); // 默认到2099年有效
    user.setShiftWork(false);              // 默认不是班次工作
    
    return user;
}

void OperatorDao::bindOperatorToQuery(QSqlQuery& query, const Operator& user, bool includeId) {
    query.addBindValue(user.Operatorname());
    query.addBindValue(user.passwordHash());
    query.addBindValue(user.salt());
    query.addBindValue(static_cast<int>(user.role()));
    query.addBindValue(static_cast<int>(user.status()));
    query.addBindValue(user.realName());
    query.addBindValue(user.email());
    query.addBindValue(user.phoneNumber());
    query.addBindValue(user.idNumber());
    query.addBindValue(user.departmentId());
    query.addBindValue(user.workNumber());
    
    if (includeId) {
        query.addBindValue(user.id());
    }
}

std::vector<Operator> OperatorDao::getOperators()
{
    QSqlDatabase db = m_provider->getDatabase();
    std::vector<Operator> operators;
    
    QSqlQuery query(db);
    query.prepare(
        "SELECT * FROM operators "
        "WHERE role <= 2 " // 只获取超级管理员、管理员和操作员（不包括普通门禁卡持有者）
        "ORDER BY id ASC"
    );
    
    if (!query.exec()) {
        qDebug() << "UserDao::getOperators - Failed to get operators:" << query.lastError().text();
        return operators;
    }
    
    while (query.next()) {
        operators.push_back(mapRowToOperator(query));
    }
    
    return operators;
}

std::vector<Operator> OperatorDao::getOperatorsWithFilter(const QString& usernameOrName, int roleFilter)
{
    QSqlDatabase db = m_provider->getDatabase();
    std::vector<Operator> operators;
    
    QString sql = "SELECT * FROM operators WHERE role <= 2 "; // 只获取超级管理员、管理员和操作员
    
    // 添加用户名或姓名过滤条件
    if (!usernameOrName.isEmpty()) {
        sql += "AND (username LIKE ? OR real_name LIKE ?) ";
    }
    
    // 添加角色过滤条件
    if (roleFilter >= 0) {
        sql += "AND role = ? ";
    }
    
    sql += "ORDER BY id ASC";
    
    QSqlQuery query(db);
    query.prepare(sql);
    
    // 绑定参数
    if (!usernameOrName.isEmpty()) {
        QString likePattern = "%" + usernameOrName + "%";
        query.addBindValue(likePattern);
        query.addBindValue(likePattern);
    }
    
    if (roleFilter >= 0) {
        query.addBindValue(roleFilter);
    }
    
    if (!query.exec()) {
        qDebug() << "UserDao::getOperatorsWithFilter - Failed to get operators with filter:" << query.lastError().text();
        return operators;
    }
    
    while (query.next()) {
        operators.push_back(mapRowToOperator(query));
    }
    
    return operators;
}

} // namespace AccessControl
