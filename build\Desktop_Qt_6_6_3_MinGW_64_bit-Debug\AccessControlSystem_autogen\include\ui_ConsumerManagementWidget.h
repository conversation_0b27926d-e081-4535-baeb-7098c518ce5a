/********************************************************************************
** Form generated from reading UI file 'ConsumerManagementWidget.ui'
**
** Created by: Qt User Interface Compiler version 6.6.3
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_CONSUMERMANAGEMENTWIDGET_H
#define UI_CONSUMERMANAGEMENTWIDGET_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QFrame>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QTableWidget>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_ConsumerManagementWidget
{
public:
    QVBoxLayout *verticalLayout;
    QFrame *buttonFrame;
    QHBoxLayout *horizontalLayout_2;
    QPushButton *batchAddButton;
    QPushButton *addButton;
    QPushButton *editButton;
    QPushButton *deleteButton;
    QPushButton *printButton;
    QPushButton *exportButton;
    QPushButton *importButton;
    QPushButton *lostButton;
    QPushButton *searchButton;
    QSpacerItem *horizontalSpacer;
    QFrame *searchFrame;
    QHBoxLayout *horizontalLayout;
    QLabel *nameLabel;
    QLineEdit *nameSearchEdit;
    QLabel *cardLabel;
    QLineEdit *cardSearchEdit;
    QLabel *deptLabel;
    QComboBox *deptSearchCombo;
    QPushButton *filterButton;
    QPushButton *clearFilterButton;
    QTableWidget *consumerTableWidget;
    QFrame *statusFrame;
    QHBoxLayout *horizontalLayout_3;
    QLabel *totalLabel;
    QSpacerItem *horizontalSpacer_2;
    QLabel *statusLabel;

    void setupUi(QWidget *ConsumerManagementWidget)
    {
        if (ConsumerManagementWidget->objectName().isEmpty())
            ConsumerManagementWidget->setObjectName("ConsumerManagementWidget");
        ConsumerManagementWidget->resize(800, 600);
        verticalLayout = new QVBoxLayout(ConsumerManagementWidget);
        verticalLayout->setObjectName("verticalLayout");
        buttonFrame = new QFrame(ConsumerManagementWidget);
        buttonFrame->setObjectName("buttonFrame");
        buttonFrame->setFrameShape(QFrame::StyledPanel);
        buttonFrame->setFrameShadow(QFrame::Raised);
        horizontalLayout_2 = new QHBoxLayout(buttonFrame);
        horizontalLayout_2->setObjectName("horizontalLayout_2");
        batchAddButton = new QPushButton(buttonFrame);
        batchAddButton->setObjectName("batchAddButton");

        horizontalLayout_2->addWidget(batchAddButton);

        addButton = new QPushButton(buttonFrame);
        addButton->setObjectName("addButton");

        horizontalLayout_2->addWidget(addButton);

        editButton = new QPushButton(buttonFrame);
        editButton->setObjectName("editButton");

        horizontalLayout_2->addWidget(editButton);

        deleteButton = new QPushButton(buttonFrame);
        deleteButton->setObjectName("deleteButton");

        horizontalLayout_2->addWidget(deleteButton);

        printButton = new QPushButton(buttonFrame);
        printButton->setObjectName("printButton");

        horizontalLayout_2->addWidget(printButton);

        exportButton = new QPushButton(buttonFrame);
        exportButton->setObjectName("exportButton");

        horizontalLayout_2->addWidget(exportButton);

        importButton = new QPushButton(buttonFrame);
        importButton->setObjectName("importButton");

        horizontalLayout_2->addWidget(importButton);

        lostButton = new QPushButton(buttonFrame);
        lostButton->setObjectName("lostButton");

        horizontalLayout_2->addWidget(lostButton);

        searchButton = new QPushButton(buttonFrame);
        searchButton->setObjectName("searchButton");

        horizontalLayout_2->addWidget(searchButton);

        horizontalSpacer = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        horizontalLayout_2->addItem(horizontalSpacer);


        verticalLayout->addWidget(buttonFrame);

        searchFrame = new QFrame(ConsumerManagementWidget);
        searchFrame->setObjectName("searchFrame");
        searchFrame->setFrameShape(QFrame::StyledPanel);
        searchFrame->setFrameShadow(QFrame::Raised);
        horizontalLayout = new QHBoxLayout(searchFrame);
        horizontalLayout->setObjectName("horizontalLayout");
        nameLabel = new QLabel(searchFrame);
        nameLabel->setObjectName("nameLabel");

        horizontalLayout->addWidget(nameLabel);

        nameSearchEdit = new QLineEdit(searchFrame);
        nameSearchEdit->setObjectName("nameSearchEdit");

        horizontalLayout->addWidget(nameSearchEdit);

        cardLabel = new QLabel(searchFrame);
        cardLabel->setObjectName("cardLabel");

        horizontalLayout->addWidget(cardLabel);

        cardSearchEdit = new QLineEdit(searchFrame);
        cardSearchEdit->setObjectName("cardSearchEdit");

        horizontalLayout->addWidget(cardSearchEdit);

        deptLabel = new QLabel(searchFrame);
        deptLabel->setObjectName("deptLabel");

        horizontalLayout->addWidget(deptLabel);

        deptSearchCombo = new QComboBox(searchFrame);
        deptSearchCombo->setObjectName("deptSearchCombo");

        horizontalLayout->addWidget(deptSearchCombo);

        filterButton = new QPushButton(searchFrame);
        filterButton->setObjectName("filterButton");

        horizontalLayout->addWidget(filterButton);

        clearFilterButton = new QPushButton(searchFrame);
        clearFilterButton->setObjectName("clearFilterButton");

        horizontalLayout->addWidget(clearFilterButton);


        verticalLayout->addWidget(searchFrame);

        consumerTableWidget = new QTableWidget(ConsumerManagementWidget);
        consumerTableWidget->setObjectName("consumerTableWidget");
        consumerTableWidget->setAlternatingRowColors(true);
        consumerTableWidget->setSelectionBehavior(QAbstractItemView::SelectRows);
        consumerTableWidget->setSortingEnabled(true);
        consumerTableWidget->horizontalHeader()->setStretchLastSection(true);

        verticalLayout->addWidget(consumerTableWidget);

        statusFrame = new QFrame(ConsumerManagementWidget);
        statusFrame->setObjectName("statusFrame");
        statusFrame->setFrameShape(QFrame::StyledPanel);
        statusFrame->setFrameShadow(QFrame::Raised);
        horizontalLayout_3 = new QHBoxLayout(statusFrame);
        horizontalLayout_3->setObjectName("horizontalLayout_3");
        totalLabel = new QLabel(statusFrame);
        totalLabel->setObjectName("totalLabel");

        horizontalLayout_3->addWidget(totalLabel);

        horizontalSpacer_2 = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        horizontalLayout_3->addItem(horizontalSpacer_2);

        statusLabel = new QLabel(statusFrame);
        statusLabel->setObjectName("statusLabel");

        horizontalLayout_3->addWidget(statusLabel);


        verticalLayout->addWidget(statusFrame);


        retranslateUi(ConsumerManagementWidget);

        QMetaObject::connectSlotsByName(ConsumerManagementWidget);
    } // setupUi

    void retranslateUi(QWidget *ConsumerManagementWidget)
    {
        ConsumerManagementWidget->setWindowTitle(QCoreApplication::translate("ConsumerManagementWidget", "\351\227\250\347\246\201\345\215\241\346\214\201\346\234\211\350\200\205\347\256\241\347\220\206", nullptr));
        batchAddButton->setText(QCoreApplication::translate("ConsumerManagementWidget", "\346\211\271\351\207\217\346\267\273\345\212\240", nullptr));
        addButton->setText(QCoreApplication::translate("ConsumerManagementWidget", "\346\267\273\345\212\240", nullptr));
        editButton->setText(QCoreApplication::translate("ConsumerManagementWidget", "\344\277\256\346\224\271", nullptr));
        deleteButton->setText(QCoreApplication::translate("ConsumerManagementWidget", "\345\210\240\351\231\244", nullptr));
        printButton->setText(QCoreApplication::translate("ConsumerManagementWidget", "\346\211\223\345\215\260", nullptr));
        exportButton->setText(QCoreApplication::translate("ConsumerManagementWidget", "\345\257\274\345\207\272Excel", nullptr));
        importButton->setText(QCoreApplication::translate("ConsumerManagementWidget", "\345\257\274\345\205\245\347\224\250\346\210\267", nullptr));
        lostButton->setText(QCoreApplication::translate("ConsumerManagementWidget", "\346\214\202\345\244\261", nullptr));
        searchButton->setText(QCoreApplication::translate("ConsumerManagementWidget", "\346\237\245\346\211\276", nullptr));
        nameLabel->setText(QCoreApplication::translate("ConsumerManagementWidget", "\345\247\223\345\220\215:", nullptr));
        nameSearchEdit->setPlaceholderText(QCoreApplication::translate("ConsumerManagementWidget", "\350\276\223\345\205\245\345\247\223\345\220\215", nullptr));
        cardLabel->setText(QCoreApplication::translate("ConsumerManagementWidget", "\345\267\245\345\217\267/\345\215\241\345\217\267:", nullptr));
        cardSearchEdit->setPlaceholderText(QCoreApplication::translate("ConsumerManagementWidget", "\350\276\223\345\205\245\345\267\245\345\217\267\346\210\226\345\215\241\345\217\267", nullptr));
        deptLabel->setText(QCoreApplication::translate("ConsumerManagementWidget", "\351\203\250\351\227\250:", nullptr));
        filterButton->setText(QCoreApplication::translate("ConsumerManagementWidget", "\346\237\245\350\257\242", nullptr));
        clearFilterButton->setText(QCoreApplication::translate("ConsumerManagementWidget", "\346\270\205\347\251\272\346\235\241\344\273\266", nullptr));
        totalLabel->setText(QCoreApplication::translate("ConsumerManagementWidget", "\346\200\273\350\256\241\357\274\2320 \346\235\241\350\256\260\345\275\225", nullptr));
        statusLabel->setText(QCoreApplication::translate("ConsumerManagementWidget", "\345\260\261\347\273\252", nullptr));
    } // retranslateUi

};

namespace Ui {
    class ConsumerManagementWidget: public Ui_ConsumerManagementWidget {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_CONSUMERMANAGEMENTWIDGET_H
