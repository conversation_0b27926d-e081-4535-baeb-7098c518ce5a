-- 迁移文件：008_create_consumers_table.sql
-- 描述：创建门禁卡持有者表，将门禁卡持有者与系统登录用户分离
-- 创建时间：2024-08-15
-- 作者：AI Assistant

-- ========== 创建consumers表 ==========

CREATE TABLE IF NOT EXISTS consumers (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    work_number VARCHAR(50) UNIQUE NOT NULL,  -- 工号
    real_name VARCHAR(100) NOT NULL,          -- 姓名
    phone_number VARCHAR(20),                 -- 手机号
    id_number VARCHAR(18),                    -- 身份证号
    department_id INTEGER DEFAULT -1,         -- 部门ID
    status INTEGER NOT NULL DEFAULT 0,        -- 0:正常, 1:停用, 2:过期
    access_enabled INTEGER NOT NULL DEFAULT 1, -- 门禁权限启用
    attendance_enabled INTEGER NOT NULL DEFAULT 1, -- 考勤权限启用
    shift_work INTEGER DEFAULT 0,             -- 是否倒班
    valid_from DATE DEFAULT (date('now')),    -- 有效期开始
    valid_until DATE DEFAULT '2099-12-31',    -- 有效期结束
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE SET NULL
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_consumers_work_number ON consumers(work_number);
CREATE INDEX IF NOT EXISTS idx_consumers_real_name ON consumers(real_name);
CREATE INDEX IF NOT EXISTS idx_consumers_phone_number ON consumers(phone_number);
CREATE INDEX IF NOT EXISTS idx_consumers_id_number ON consumers(id_number);
CREATE INDEX IF NOT EXISTS idx_consumers_department_id ON consumers(department_id);
CREATE INDEX IF NOT EXISTS idx_consumers_status ON consumers(status);

-- 创建触发器，在更新consumers时自动更新updated_at字段
CREATE TRIGGER IF NOT EXISTS trigger_consumers_update 
    AFTER UPDATE ON consumers
    FOR EACH ROW
BEGIN
    UPDATE consumers SET updated_at = CURRENT_TIMESTAMP WHERE id = OLD.id;
END;

-- ========== 修改user_cards表 ==========

-- 修改user_cards表，将user_id改为consumer_id
CREATE TABLE IF NOT EXISTS user_cards_new (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    consumer_id INTEGER NOT NULL,            -- 门禁卡持有者ID
    card_number VARCHAR(50) NOT NULL UNIQUE, -- 卡号
    card_type INTEGER NOT NULL DEFAULT 0,    -- 0:IC/ID卡, 1:CPU卡, 2:指纹, 3:人脸, 4:手机号, 5:身份证, 6:胁迫卡, 7:母卡
    status INTEGER NOT NULL DEFAULT 0,       -- 0:正常, 1:挂失, 2:注销
    valid_from DATE DEFAULT CURRENT_DATE,    -- 有效期开始
    valid_until DATE DEFAULT '2099-12-31',   -- 有效期结束
    is_primary BOOLEAN DEFAULT 0,            -- 是否主卡
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (consumer_id) REFERENCES consumers(id) ON DELETE CASCADE
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_user_cards_new_consumer_id ON user_cards_new(consumer_id);
CREATE INDEX IF NOT EXISTS idx_user_cards_new_card_number ON user_cards_new(card_number);
CREATE INDEX IF NOT EXISTS idx_user_cards_new_card_type ON user_cards_new(card_type);
CREATE INDEX IF NOT EXISTS idx_user_cards_new_status ON user_cards_new(status);
CREATE INDEX IF NOT EXISTS idx_user_cards_new_is_primary ON user_cards_new(is_primary);

-- 创建触发器，在更新user_cards_new时自动更新updated_at字段
CREATE TRIGGER IF NOT EXISTS trigger_user_cards_new_update 
    AFTER UPDATE ON user_cards_new
    FOR EACH ROW
BEGIN
    UPDATE user_cards_new SET updated_at = CURRENT_TIMESTAMP WHERE id = OLD.id;
END;

-- 注意：此迁移文件创建了新表，但不会自动迁移数据
-- 需要在应用程序中编写代码，将现有的用户数据迁移到consumers表中 