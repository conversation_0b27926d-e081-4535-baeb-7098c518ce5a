#ifndef CONSUMERFINGERPRINTWIDGET_H
#define CONSUMERFINGERPRINTWIDGET_H

#include <QWidget>
#include <QLabel>
#include <QPushButton>
#include <QByteArray>

namespace AccessControl {

/**
 * @brief 消费者指纹管理控件
 * 
 * 提供指纹录入和管理功能
 */
class ConsumerFingerprintWidget : public QWidget
{
    Q_OBJECT

public:
    /**
     * @brief 指纹类型枚举
     */
    enum class Finger {
        RightThumb = 1,    ///< 右手拇指
        RightIndex = 2,    ///< 右手食指
        RightMiddle = 3,   ///< 右手中指
        LeftThumb = 6,     ///< 左手拇指
        LeftIndex = 7,     ///< 左手食指
        LeftMiddle = 8     ///< 左手中指
    };

    /**
     * @brief 构造函数
     * @param parent 父窗口
     */
    explicit ConsumerFingerprintWidget(QWidget *parent = nullptr);
    
    /**
     * @brief 析构函数
     */
    ~ConsumerFingerprintWidget();

    /**
     * @brief 设置指纹数据
     * @param fingerprintData 指纹二进制数据
     * @param finger 指纹类型
     */
    void setFingerprintData(const QByteArray& fingerprintData, Finger finger);

    /**
     * @brief 获取指纹数据
     * @param finger 指纹类型
     * @return 指纹二进制数据
     */
    QByteArray fingerprintData(Finger finger) const;

    /**
     * @brief 清除指纹
     * @param finger 指纹类型
     */
    void clearFingerprint(Finger finger);

    /**
     * @brief 是否有指纹
     * @param finger 指纹类型
     * @return 是否有指纹
     */
    bool hasFingerprint(Finger finger) const;

signals:
    /**
     * @brief 指纹变更信号
     */
    void fingerprintChanged();

private slots:
    /**
     * @brief 录入指纹
     */
    void enrollFingerprint();

    /**
     * @brief 删除指纹
     */
    void deleteFingerprint();

private:
    /**
     * @brief 初始化UI
     */
    void initUI();

    /**
     * @brief 更新UI状态
     */
    void updateUIState();

private:
    QLabel* m_statusLabel;        ///< 状态显示标签
    QPushButton* m_enrollButton;  ///< 录入按钮
    QPushButton* m_deleteButton;  ///< 删除按钮
    
    QByteArray m_fingerprintData; ///< 指纹数据
    Finger m_currentFinger;       ///< 当前指纹类型
};

} // namespace AccessControl

#endif // CONSUMERFINGERPRINTWIDGET_H 