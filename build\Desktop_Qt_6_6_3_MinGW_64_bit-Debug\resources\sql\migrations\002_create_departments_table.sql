-- 迁移文件：002_create_departments_table.sql
-- 描述：创建部门表
-- 创建时间：2024-07-03
-- 作者：AI Assistant

-- ========== 创建departments表 ==========

CREATE TABLE IF NOT EXISTS departments (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(50),
    parent_id INTEGER DEFAULT -1,
    level INTEGER DEFAULT 1,
    path VARCHAR(500),
    description TEXT,
    manager_id INTEGER,
    manager_name VARCHAR(100),
    contact_info VARCHAR(200),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES departments(id) ON DELETE SET NULL
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_departments_name ON departments(name);
CREATE INDEX IF NOT EXISTS idx_departments_code ON departments(code);
CREATE INDEX IF NOT EXISTS idx_departments_parent_id ON departments(parent_id);
CREATE INDEX IF NOT EXISTS idx_departments_path ON departments(path);

-- 创建触发器，在更新departments时自动更新updated_at字段
CREATE TRIGGER IF NOT EXISTS trigger_departments_update 
    AFTER UPDATE ON departments
    FOR EACH ROW
BEGIN
    UPDATE departments SET updated_at = CURRENT_TIMESTAMP WHERE id = OLD.id;
END;

-- 插入根部门
INSERT OR IGNORE INTO departments (id, name, code, parent_id, level, path, description)
VALUES (1, '公司总部', 'HQ', -1, 1, '1', '公司总部'); 