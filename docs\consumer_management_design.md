# 门禁用户管理系统设计方案

## 📋 概述

本文档详细描述了门禁用户管理系统的设计方案，包括数据库结构、UI界面设计和卡片管理机制，以满足现代门禁系统的多样化需求。

## 🗃️ 数据库设计

### 1. 基本信息表 (consumers)

**表结构**：
```sql
CREATE TABLE consumers (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    work_number VARCHAR(50) UNIQUE NOT NULL,  -- 工号
    real_name VARCHAR(100) NOT NULL,          -- 姓名
    gender INTEGER DEFAULT 0,                 -- 性别：0-未知, 1-男, 2-女
    phone_number VARCHAR(20),                 -- 手机号
    work_phone VARCHAR(20),                   -- 工作电话
    home_phone VARCHAR(20),                   -- 家庭电话
    id_number VARCHAR(18),                    -- 身份证号
    department_id INTEGER DEFAULT -1,         -- 部门ID
    status INTEGER NOT NULL DEFAULT 0,        -- 0:正常, 1:停用, 2:过期
    access_enabled INTEGER NOT NULL DEFAULT 1, -- 门禁权限启用
    attendance_enabled INTEGER NOT NULL DEFAULT 1, -- 考勤权限启用
    shift_work INTEGER DEFAULT 0,             -- 是否倒班
    valid_from DATE DEFAULT (date('now')),    -- 有效期开始（默认当前日期）
    valid_until DATE DEFAULT '2099-12-31',    -- 有效期结束（默认2099-12-31）
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 2. 扩展信息表 (consumers_extended)

**表结构**：
```sql
CREATE TABLE consumers_extended (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    consumer_id INTEGER UNIQUE NOT NULL,     -- 关联consumers表的ID
    
    -- 个人信息
    nation VARCHAR(50),                       -- 民族
    religion VARCHAR(50),                     -- 宗教
    birthplace VARCHAR(100),                  -- 籍贯
    birth_date DATE,                          -- 出生年月
    marital_status INTEGER DEFAULT 0,        -- 婚姻状态：0-未知, 1-未婚, 2-已婚, 3-离异, 4-丧偶
    political_status VARCHAR(50),            -- 政治面貌
    education_level INTEGER DEFAULT 0,       -- 学历：0-未知, 1-小学...8-博士
    english_name VARCHAR(100),               -- 英文名
    
    -- 工作信息
    company VARCHAR(200),                     -- 单位
    job_title VARCHAR(100),                   -- 职称
    technical_level VARCHAR(100),            -- 技术等级
    entry_date DATE,                          -- 入职时间
    leave_date DATE,                          -- 离职时间
    
    -- 证件信息
    certificate_name VARCHAR(100),           -- 证件名称
    certificate_number VARCHAR(50),          -- 证件号
    social_security_number VARCHAR(50),      -- 社保号
    
    -- 联系信息
    email VARCHAR(100),                       -- 电子邮箱
    address VARCHAR(200),                     -- 通讯地址
    postal_code VARCHAR(10),                  -- 邮编
    
    -- 其他信息
    remarks TEXT,                             -- 备注
    
    FOREIGN KEY (consumer_id) REFERENCES consumers(id) ON DELETE CASCADE
);
```

### 3. 卡片管理表 (consumer_cards)

**设计原则**：
- 支持多种卡类型：IC/ID卡、CPU卡、手机APP、手机NFC、指纹、人脸、手机号、身份证
- 卡号必须为数字，范围：1-1152921504606846975
- 自动移除前导零
- 同一人可拥有多张卡
- 生物识别（指纹、人脸）数据绑定到特定卡片

**表结构**：
```sql
CREATE TABLE consumer_cards (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    consumer_id INTEGER NOT NULL,            -- 消费者ID
    card_number BIGINT NOT NULL,             -- 卡号（数字，范围1-1152921504606846975）
    card_type INTEGER NOT NULL DEFAULT 0,    -- 卡类型：0-IC/ID卡, 1-CPU卡, 2-手机APP, 3-手机NFC, 4-指纹, 5-人脸, 6-手机号, 7-身份证
    is_primary BOOLEAN DEFAULT 0,            -- 是否主卡
    is_biometric BOOLEAN DEFAULT 0,          -- 是否生物识别卡（指纹、人脸）
    status INTEGER NOT NULL DEFAULT 0,       -- 状态：0-正常, 1-挂失, 2-注销
    valid_from DATE DEFAULT (date('now')),   -- 有效期开始
    valid_until DATE DEFAULT '2099-12-31',   -- 有效期结束
    
    FOREIGN KEY (consumer_id) REFERENCES consumers(id) ON DELETE CASCADE,
    UNIQUE(card_number)  -- 卡号全局唯一
);
```

### 4. 生物识别数据表 (consumer_biometrics)

**表结构**：
```sql
CREATE TABLE consumer_biometrics (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    consumer_id INTEGER NOT NULL,            -- 消费者ID
    card_id INTEGER NOT NULL,                -- 关联的卡片ID
    biometric_type INTEGER NOT NULL,         -- 生物识别类型：1-指纹, 2-人脸
    finger_type INTEGER,                     -- 指纹类型（仅当biometric_type=1时）
    biometric_data BLOB,                     -- 生物识别数据
    template_data BLOB,                      -- 模板数据
    quality_score INTEGER DEFAULT 0,         -- 质量评分（0-100）
    
    FOREIGN KEY (consumer_id) REFERENCES consumers(id) ON DELETE CASCADE,
    FOREIGN KEY (card_id) REFERENCES consumer_cards(id) ON DELETE CASCADE
);
```

### 5. 照片数据表 (consumer_photos)

**表结构**：
```sql
CREATE TABLE consumer_photos (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    consumer_id INTEGER UNIQUE NOT NULL,     -- 消费者ID（每人只能有一张照片）
    photo_data BLOB NOT NULL,                -- 照片数据
    photo_format VARCHAR(10) DEFAULT 'JPEG', -- 照片格式
    photo_size INTEGER DEFAULT 0,            -- 照片大小（字节）
    width INTEGER DEFAULT 0,                 -- 宽度
    height INTEGER DEFAULT 0,                -- 高度
    
    FOREIGN KEY (consumer_id) REFERENCES consumers(id) ON DELETE CASCADE
);
```

## 🖥️ UI界面设计

### 1. 用户对话框结构

**标签页组织**：
- **基本信息**：常用的基础字段
- **扩展信息**：详细的个人、工作、证件信息
- **照片管理**：上传照片或拍照功能
- **指纹管理**：指纹录入和管理
- **卡片管理**：多卡片添加和管理

### 2. 基本信息页面

**字段清单**：
- 工号* (必填)
- 姓名* (必填)
- 性别 (下拉选择：未知/男/女)
- 手机号
- 工作电话
- 家庭电话
- 身份证号
- 部门 (下拉选择)

**权限设置组**：
- 启用考勤 (默认启用)
- 启用门禁 (默认启用)
- 倒班 (默认禁用)

**有效期设置组**：
- 起始日期 (默认当前日期)
- 截止日期 (默认2099-12-31)

**状态设置**：
- 启用此用户 (默认启用)

### 3. 扩展信息页面

**个人信息组**：
- 民族、宗教、籍贯、出生年月
- 婚姻状态 (下拉选择)
- 政治面貌
- 学历 (下拉选择：未知/小学/初中/高中/中专/大专/本科/硕士/博士)
- 英文名

**工作信息组**：
- 单位、职称、技术等级
- 入职时间、离职时间

**证件信息组**：
- 证件名称、证件号、社保号

**联系信息组**：
- 电子邮箱、通讯地址、邮编

**备注**：
- 多行文本输入框

### 4. 卡片管理页面

**卡片输入区域**：
- 卡号输入 (实时验证范围1-1152921504606846975)
- 卡类型选择 (下拉选择8种类型)
- 是否主卡 (复选框)
- 添加/编辑/删除按钮

**卡片列表**：
- 表格显示：卡号、卡类型、是否主卡、状态
- 支持选择和编辑

## 🔧 卡片管理机制

### 1. 卡号验证规则

**输入限制**：
- 只允许数字输入
- 自动移除非数字字符
- 范围：1-1152921504606846975
- 自动移除前导零

**验证流程**：
```javascript
// 伪代码
function validateCardNumber(input) {
    // 1. 移除非数字字符
    let cleaned = input.replace(/[^\d]/g, '');
    
    // 2. 移除前导零
    while (cleaned.startsWith('0') && cleaned.length > 1) {
        cleaned = cleaned.substring(1);
    }
    
    // 3. 验证范围
    let number = parseInt(cleaned);
    if (number < 1 || number > 1152921504606846975) {
        return null; // 无效
    }
    
    return cleaned;
}
```

### 2. 生物识别卡绑定策略

**设计原则**：
- 指纹、人脸等生物识别数据必须绑定到具体卡片
- 一个人的照片可以绑定到多个卡片（如果有多张生物识别卡）
- 同一张卡片可以同时支持指纹和人脸识别
- 每种生物识别方式在同一卡片上只能有一套数据

**绑定流程**：
1. 用户添加指纹或人脸卡类型的卡片
2. 系统自动将该卡片标记为生物识别卡 (is_biometric=1)
3. 用户在指纹/照片管理页面录入相应数据
4. 系统将生物识别数据与对应卡片ID关联存储

### 3. 主卡管理

**规则**：
- 每个用户只能有一张主卡
- 添加新主卡时，自动取消其他卡片的主卡状态
- 主卡用于默认门禁操作和显示

## 📝 实现建议

### 1. 数据库迁移

**执行顺序**：
1. 运行 `011_extend_consumers_table.sql` 扩展现有表结构
2. 更新 `DatabaseMigration.cpp` 添加新迁移文件
3. 测试数据库表创建和关联

### 2. UI组件开发

**开发优先级**：
1. ✅ 基本信息页面 - 已完成
2. ✅ 扩展信息页面 - 已完成  
3. ✅ 卡片管理页面 - 已完成
4. 🔄 照片管理组件 - 现有简化版本
5. 🔄 指纹管理组件 - 现有简化版本

### 3. 数据访问层

**需要扩展的DAO**：
- `ConsumerDao` - 添加扩展信息的CRUD操作
- 新建 `ConsumerCardDao` - 卡片管理
- 新建 `ConsumerBiometricDao` - 生物识别数据管理
- 新建 `ConsumerPhotoDao` - 照片数据管理

### 4. 验证和安全

**数据验证**：
- 工号唯一性验证
- 卡号范围和格式验证
- 邮箱格式验证
- 身份证号格式验证

**安全考虑**：
- 生物识别数据加密存储
- 照片数据压缩和格式标准化
- 敏感信息访问控制

## 🎯 总结

本设计方案完全满足您的需求：

✅ **界面分离**：基本信息和扩展信息分为两个标签页  
✅ **字段完整**：包含所有必需的用户信息字段  
✅ **多卡支持**：同一用户可拥有多张不同类型的卡  
✅ **生物识别**：指纹和人脸数据绑定到特定卡片  
✅ **卡号管理**：严格的数字验证和范围控制  
✅ **日期管理**：默认起始日期为当前时间，截止日期为2099-12-31  
✅ **数据分类**：常用信息和扩展信息分表存储，提高查询效率

该方案提供了灵活、可扩展的用户管理架构，既满足当前需求，也为未来功能扩展预留了空间。 