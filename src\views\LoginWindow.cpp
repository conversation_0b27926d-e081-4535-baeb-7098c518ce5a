#include "LoginWindow.h"
#include "../database/DatabaseFactory.h"
#include "../database/DatabaseMigration.h"
#include "../database/dao/OperatorDao.h"
#include "../models/Operator.h"
#include <QApplication>
#include <QMessageBox>
#include <QCryptographicHash>
#include <QKeyEvent>
#include <QDateTime>
#include <QNetworkInterface>
#include <QScreen>
#include <QDebug>

namespace AccessControl {

LoginWindow::LoginWindow(QWidget *parent)
    : QDialog(parent)
    , m_mainLayout(nullptr)
    , m_centerWidget(nullptr)
    , m_centerLayout(nullptr)
    , m_titleLabel(nullptr)
    , m_subtitleLabel(nullptr)
    , m_dbGroup(nullptr)
    , m_dbLayout(nullptr)
    , m_dbStatusLabel(nullptr)
    , m_dbConfigBtn(nullptr)
    , m_loginGroup(nullptr)
    , m_formLayout(nullptr)
    , m_usernameLabel(nullptr)
    , m_usernameEdit(nullptr)
    , m_passwordLabel(nullptr)
    , m_passwordEdit(nullptr)
    , m_rememberCheck(nullptr)
    , m_autoLoginCheck(nullptr)
    , m_buttonLayout(nullptr)
    , m_loginBtn(nullptr)
    , m_exitBtn(nullptr)
    , m_statusLabel(nullptr)
    , m_progressBar(nullptr)
    , m_settings(new QSettings("AccessControl", "LoginSettings", this))
    , m_loginTimer(new QTimer(this))
    , m_fadeAnimation(nullptr)
    , m_opacityEffect(nullptr)
    , m_isConnecting(false)
    , m_isLoggedIn(false)
    , m_loginAttempts(0)
    , m_operatorDao(nullptr)
{
    // 设置窗口属性
    setWindowTitle("门禁管理系统 - 用户登录");
    setWindowFlags(Qt::Dialog | Qt::WindowCloseButtonHint);
    setModal(true);
    setFixedSize(720, 480); // 改为横向布局：宽720，高480
    
    // 设置默认数据库配置
    m_dbConfig.type = DatabaseConfig::SQLite;
    m_dbConfig.filePath = "access_control.db";
    
    // 初始化界面和功能
    initializeUI();
    initializeStyles();
    initializeConnections();
    loadSettings();
    
    // 启动动画效果
    startFadeInAnimation();
    
    // 初始化数据库
    initializeDatabase();
}

LoginWindow::~LoginWindow() {
    saveSettings();
}

void LoginWindow::setDatabaseConfig(const DatabaseConfig& config) {
    m_dbConfig = config;
    initializeDatabase();
}

void LoginWindow::showLogin() {
    // 居中显示窗口
    QScreen* screen = QApplication::primaryScreen();
    QRect screenGeometry = screen->geometry();
    int x = (screenGeometry.width() - width()) / 2;
    int y = (screenGeometry.height() - height()) / 2;
    move(x, y);
    
    // 重置状态
    m_loginAttempts = 0;
    m_isLoggedIn = false;
    
    // 检查自动登录
    checkAutoLogin();
    
    show();
    m_usernameEdit->setFocus();
}

// ========== 槽函数实现 ==========

void LoginWindow::handleLogin() {
    if (m_isConnecting) {
        return;
    }
    
    if (!validateInput()) {
        return;
    }
    
    m_isConnecting = true;
    setControlsEnabled(false);
    showProgress(true, "正在验证用户身份...");
    
    // 启动登录超时定时器
    m_loginTimer->start(LOGIN_TIMEOUT_MS);
    
    // 异步执行认证（这里简化为同步）
    QApplication::processEvents();
    
    bool success = authenticateUser();
    
    m_loginTimer->stop();
    m_isConnecting = false;
    showProgress(false);
    setControlsEnabled(true);
    
    if (success) {
        updateLoginStatus("登录成功，正在进入系统...");
        
        // 保存设置
        if (m_rememberCheck->isChecked()) {
            saveSettings();
        } else {
            clearSavedPassword();
        }
        
        // 延迟一下让用户看到成功消息
        QTimer::singleShot(1000, this, [this]() {
            m_isLoggedIn = true;
            accept();
        });
    } else {
        m_loginAttempts++;
        QString message = QString("登录失败，您还有 %1 次尝试机会")
                         .arg(MAX_LOGIN_ATTEMPTS - m_loginAttempts);
        
        if (m_loginAttempts >= MAX_LOGIN_ATTEMPTS) {
            message = "登录失败次数过多，程序将退出";
            updateLoginStatus(message, true);
            QTimer::singleShot(2000, this, &QDialog::reject);
        } else {
            updateLoginStatus(message, true);
            m_passwordEdit->selectAll();
            m_passwordEdit->setFocus();
        }
    }
}

void LoginWindow::handleDatabaseConfig() {
    // 这里可以打开数据库配置对话框
    // 暂时使用简单的消息框提示
    QMessageBox::information(this, "数据库配置", 
                             QString("当前数据库类型: %1\n"
                                   "连接状态: %2")
                             .arg(DatabaseFactory::getTypeName(m_dbConfig.type))
                             .arg(m_databaseProvider && m_databaseProvider->isConnected() ? "已连接" : "未连接"));
}

void LoginWindow::handleRememberPassword(bool checked) {
    if (!checked) {
        m_autoLoginCheck->setChecked(false);
        clearSavedPassword();
    }
}

void LoginWindow::handleAutoLogin(bool checked) {
    if (checked && !m_rememberCheck->isChecked()) {
        m_rememberCheck->setChecked(true);
    }
}

void LoginWindow::onUsernameChanged() {
    // 用户名改变时，清除保存的密码（如果用户名不匹配）
    QString savedUsername = m_settings->value("username").toString();
    if (m_usernameEdit->text() != savedUsername) {
        m_passwordEdit->clear();
    }
    
    updateLoginStatus("");
}

void LoginWindow::onPasswordChanged() {
    updateLoginStatus("");
}

void LoginWindow::onLoginTimeout() {
    m_loginTimer->stop();
    m_isConnecting = false;
    showProgress(false);
    setControlsEnabled(true);
    updateLoginStatus("登录超时，请重试", true);
}

void LoginWindow::onFadeInFinished() {
    // 淡入动画完成后的处理
}

// ========== 私有方法实现 ==========

void LoginWindow::initializeUI() {
    // 创建主布局（横向）
    m_mainLayout = new QHBoxLayout(this);
    m_mainLayout->setSpacing(0);
    m_mainLayout->setContentsMargins(0, 0, 0, 0);
    
    // 创建左侧标题区域
    QWidget* leftWidget = new QWidget(this);
    leftWidget->setFixedWidth(320);
    leftWidget->setObjectName("leftPanel");
    QVBoxLayout* leftLayout = new QVBoxLayout(leftWidget);
    leftLayout->setContentsMargins(40, 60, 20, 60);
    leftLayout->setSpacing(20);
    
    // 标题区域
    m_titleLabel = new QLabel("门禁管理系统", this);
    m_titleLabel->setAlignment(Qt::AlignLeft);
    m_titleLabel->setObjectName("titleLabel");
    
    m_subtitleLabel = new QLabel("专业智能门禁管理平台", this);
    m_subtitleLabel->setAlignment(Qt::AlignLeft);
    m_subtitleLabel->setObjectName("subtitleLabel");
    
    // 添加装饰图标或信息
    QLabel* featureLabel = new QLabel("✓ 支持多数据库\n✓ 企业级安全\n✓ 实时监控\n✓ 权限管理", this);
    featureLabel->setObjectName("featureLabel");
    featureLabel->setAlignment(Qt::AlignLeft);
    
    leftLayout->addWidget(m_titleLabel);
    leftLayout->addWidget(m_subtitleLabel);
    leftLayout->addSpacing(40);
    leftLayout->addWidget(featureLabel);
    leftLayout->addStretch();
    
    // 创建右侧表单区域
    QWidget* rightWidget = new QWidget(this);
    rightWidget->setObjectName("rightPanel");
    m_centerLayout = new QVBoxLayout(rightWidget);
    m_centerLayout->setContentsMargins(20, 40, 40, 40);
    m_centerLayout->setSpacing(20);
    
    // 数据库连接状态区域
    m_dbGroup = new QGroupBox("数据库连接", this);
    m_dbLayout = new QHBoxLayout(m_dbGroup);
    
    m_dbStatusLabel = new QLabel("未连接", this);
    m_dbStatusLabel->setObjectName("dbStatusLabel");
    
    m_dbConfigBtn = new QPushButton("配置", this);
    m_dbConfigBtn->setMaximumWidth(60);
    m_dbConfigBtn->setObjectName("dbConfigBtn");
    
    m_dbLayout->addWidget(m_dbStatusLabel);
    m_dbLayout->addStretch();
    m_dbLayout->addWidget(m_dbConfigBtn);
    
    // 登录表单区域
    m_loginGroup = new QGroupBox("用户登录", this);
    m_formLayout = new QGridLayout(m_loginGroup);
    m_formLayout->setSpacing(15);
    
    m_usernameLabel = new QLabel("用户名:", this);
    m_usernameEdit = new QLineEdit(this);
    m_usernameEdit->setPlaceholderText("请输入用户名");
    m_usernameEdit->setObjectName("inputField");
    
    m_passwordLabel = new QLabel("密码:", this);
    m_passwordEdit = new QLineEdit(this);
    m_passwordEdit->setEchoMode(QLineEdit::Password);
    m_passwordEdit->setPlaceholderText("请输入密码");
    m_passwordEdit->setObjectName("inputField");
    
    m_rememberCheck = new QCheckBox("记住密码", this);
    m_autoLoginCheck = new QCheckBox("自动登录", this);
    
    // 添加控件到表单布局
    m_formLayout->addWidget(m_usernameLabel, 0, 0);
    m_formLayout->addWidget(m_usernameEdit, 0, 1);
    m_formLayout->addWidget(m_passwordLabel, 1, 0);
    m_formLayout->addWidget(m_passwordEdit, 1, 1);
    
    QHBoxLayout* checkLayout = new QHBoxLayout();
    checkLayout->addWidget(m_rememberCheck);
    checkLayout->addWidget(m_autoLoginCheck);
    checkLayout->addStretch();
    m_formLayout->addLayout(checkLayout, 2, 0, 1, 2);
    
    // 按钮区域
    m_buttonLayout = new QHBoxLayout();
    m_loginBtn = new QPushButton("登录", this);
    m_loginBtn->setDefault(true);
    m_loginBtn->setObjectName("loginBtn");
    
    m_exitBtn = new QPushButton("退出", this);
    m_exitBtn->setObjectName("exitBtn");
    
    m_buttonLayout->addStretch();
    m_buttonLayout->addWidget(m_loginBtn);
    m_buttonLayout->addWidget(m_exitBtn);
    
    // 状态区域
    m_statusLabel = new QLabel(this);
    m_statusLabel->setAlignment(Qt::AlignCenter);
    m_statusLabel->setObjectName("statusLabel");
    m_statusLabel->setWordWrap(true);
    
    m_progressBar = new QProgressBar(this);
    m_progressBar->setTextVisible(false);
    m_progressBar->setVisible(false);
    m_progressBar->setObjectName("progressBar");
    
    // 组装右侧布局
    m_centerLayout->addWidget(m_dbGroup);
    m_centerLayout->addWidget(m_loginGroup);
    m_centerLayout->addLayout(m_buttonLayout);
    m_centerLayout->addWidget(m_statusLabel);
    m_centerLayout->addWidget(m_progressBar);
    m_centerLayout->addStretch();
    
    // 将左右面板添加到主布局
    m_mainLayout->addWidget(leftWidget);
    m_mainLayout->addWidget(rightWidget);
}

void LoginWindow::initializeStyles() {
    // 设置整体样式
    setStyleSheet(R"(
        QDialog {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                                      stop:0 #1976D2, stop:1 #f6f6f6);
        }
        
        #leftPanel {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                                      stop:0 #1976D2, stop:1 #1565C0);
            border-radius: 0px;
        }
        
        #rightPanel {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 0px;
        }
        
        #titleLabel {
            font-size: 32px;
            font-weight: bold;
            color: white;
            margin: 10px 0;
        }
        
        #subtitleLabel {
            font-size: 16px;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 10px;
        }
        
        #featureLabel {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
            line-height: 1.8;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
        }
        
        QGroupBox {
            font-weight: bold;
            border: 2px solid #E0E0E0;
            border-radius: 8px;
            margin-top: 10px;
            padding-top: 5px;
            background-color: rgba(255, 255, 255, 0.8);
        }
        
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 8px 0 8px;
            color: #1976D2;
        }
        
        #inputField {
            padding: 10px;
            border: 2px solid #E0E0E0;
            border-radius: 6px;
            font-size: 14px;
            background-color: white;
        }
        
        #inputField:focus {
            border-color: #1976D2;
        }
        
        #loginBtn {
            background-color: #1976D2;
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 6px;
            font-weight: bold;
            font-size: 14px;
            min-width: 100px;
        }
        
        #loginBtn:hover {
            background-color: #1565C0;
        }
        
        #loginBtn:pressed {
            background-color: #0D47A1;
        }
        
        #loginBtn:disabled {
            background-color: #CCCCCC;
            color: #666666;
        }
        
        #exitBtn {
            background-color: #757575;
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 6px;
            font-weight: bold;
            font-size: 14px;
            min-width: 100px;
        }
        
        #exitBtn:hover {
            background-color: #616161;
        }
        
        #dbConfigBtn {
            background-color: #2196F3;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 12px;
        }
        
        #dbStatusLabel {
            color: #757575;
            font-weight: normal;
        }
        
        #statusLabel {
            color: #757575;
            font-size: 13px;
            margin: 10px 0;
        }
        
        #progressBar {
            border: 1px solid #E0E0E0;
            border-radius: 4px;
            background-color: #F5F5F5;
        }
        
        #progressBar::chunk {
            background-color: #1976D2;
            border-radius: 3px;
        }
        
        QCheckBox {
            color: #666666;
            font-size: 13px;
        }
        
        QCheckBox::indicator {
            width: 16px;
            height: 16px;
        }
        
        QCheckBox::indicator:unchecked {
            border: 2px solid #E0E0E0;
            border-radius: 3px;
            background-color: white;
        }
        
        QCheckBox::indicator:checked {
            border: 2px solid #1976D2;
            border-radius: 3px;
            background-color: #1976D2;
            image: url(:/icons/check.png);
        }
    )");
}

void LoginWindow::initializeConnections() {
    // 连接信号槽
    connect(m_loginBtn, &QPushButton::clicked, this, &LoginWindow::handleLogin);
    connect(m_exitBtn, &QPushButton::clicked, this, &QDialog::reject);
    connect(m_dbConfigBtn, &QPushButton::clicked, this, &LoginWindow::handleDatabaseConfig);
    
    connect(m_rememberCheck, &QCheckBox::toggled, this, &LoginWindow::handleRememberPassword);
    connect(m_autoLoginCheck, &QCheckBox::toggled, this, &LoginWindow::handleAutoLogin);
    
    connect(m_usernameEdit, &QLineEdit::textChanged, this, &LoginWindow::onUsernameChanged);
    connect(m_passwordEdit, &QLineEdit::textChanged, this, &LoginWindow::onPasswordChanged);
    
    // 回车键登录
    connect(m_usernameEdit, &QLineEdit::returnPressed, m_passwordEdit, 
            QOverload<>::of(&QLineEdit::setFocus));
    connect(m_passwordEdit, &QLineEdit::returnPressed, this, &LoginWindow::handleLogin);
    
    // 登录超时定时器
    connect(m_loginTimer, &QTimer::timeout, this, &LoginWindow::onLoginTimeout);
}

void LoginWindow::initializeDatabase() {
    updateLoginStatus("正在连接数据库...");
    
    if (connectToDatabase()) {
        updateDatabaseStatus(true, "已连接");
        updateLoginStatus("数据库连接成功，请输入用户名和密码");
        
        // 数据库连接成功后检查自动登录
        QTimer::singleShot(500, this, &LoginWindow::checkAutoLogin);
    } else {
        updateDatabaseStatus(false, "连接失败");
        updateLoginStatus("数据库连接失败，请检查配置", true);
    }
}

void LoginWindow::loadSettings() {
    // 加载保存的用户名
    QString savedUsername = m_settings->value("username").toString();
    if (!savedUsername.isEmpty()) {
        m_usernameEdit->setText(savedUsername);
    }
    
    // 加载记住密码设置
    bool rememberPassword = m_settings->value("rememberPassword", false).toBool();
    m_rememberCheck->setChecked(rememberPassword);
    
    if (rememberPassword) {
        QString encryptedPassword = m_settings->value("password").toString();
        if (!encryptedPassword.isEmpty()) {
            QString password = decryptPassword(encryptedPassword);
            m_passwordEdit->setText(password);
        }
    }
    
    // 加载自动登录设置
    bool autoLogin = m_settings->value("autoLogin", false).toBool();
    m_autoLoginCheck->setChecked(autoLogin);
    
    // 加载数据库配置
    m_dbConfig.type = static_cast<DatabaseConfig::DatabaseType>(
        m_settings->value("dbType", static_cast<int>(DatabaseConfig::SQLite)).toInt());
    m_dbConfig.filePath = m_settings->value("dbFilePath", "access_control.db").toString();
    m_dbConfig.host = m_settings->value("dbHost", "localhost").toString();
    m_dbConfig.port = m_settings->value("dbPort", 5432).toInt();
    m_dbConfig.database = m_settings->value("dbDatabase", "access_control").toString();
    m_dbConfig.username = m_settings->value("dbUsername", "postgres").toString();
    m_dbConfig.password = m_settings->value("dbPassword").toString();
}

void LoginWindow::saveSettings() {
    // 保存用户名
    m_settings->setValue("username", m_usernameEdit->text());
    
    // 保存记住密码设置
    m_settings->setValue("rememberPassword", m_rememberCheck->isChecked());
    
    if (m_rememberCheck->isChecked()) {
        // 加密保存密码
        QString encryptedPassword = encryptPassword(m_passwordEdit->text());
        m_settings->setValue("password", encryptedPassword);
    } else {
        m_settings->remove("password");
    }
    
    // 保存自动登录设置
    m_settings->setValue("autoLogin", m_autoLoginCheck->isChecked());
    
    // 保存数据库配置
    m_settings->setValue("dbType", static_cast<int>(m_dbConfig.type));
    m_settings->setValue("dbFilePath", m_dbConfig.filePath);
    m_settings->setValue("dbHost", m_dbConfig.host);
    m_settings->setValue("dbPort", m_dbConfig.port);
    m_settings->setValue("dbDatabase", m_dbConfig.database);
    m_settings->setValue("dbUsername", m_dbConfig.username);
    m_settings->setValue("dbPassword", m_dbConfig.password);
    
    m_settings->sync();
}

bool LoginWindow::connectToDatabase() {
    try {
        m_databaseProvider = DatabaseFactory::createAndConnect(m_dbConfig);
        
        if (!m_databaseProvider) {
            return false;
        }
        
        // 运行数据库迁移，确保所有表存在
        qDebug() << "LoginWindow: 开始运行数据库迁移...";
        DatabaseMigration migration(m_databaseProvider);
        if (!migration.runMigrations()) {
            qWarning() << "LoginWindow: 数据库迁移失败";
            return false;
        }
        qDebug() << "LoginWindow: 数据库迁移完成";
        
        m_operatorDao = std::make_unique<OperatorDao>(m_databaseProvider);
        return true;
        
    } catch (const std::exception& e) {
        qWarning() << "数据库连接异常:" << e.what();
        return false;
    }
}

bool LoginWindow::validateInput() {
    QString username = m_usernameEdit->text().trimmed();
    QString password = m_passwordEdit->text();
    
    if (username.isEmpty()) {
        updateLoginStatus("请输入用户名", true);
        m_usernameEdit->setFocus();
        return false;
    }
    
    if (password.isEmpty()) {
        updateLoginStatus("请输入密码", true);
        m_passwordEdit->setFocus();
        return false;
    }
    
    if (password.length() < 4) {
        updateLoginStatus("密码长度不能少于4位", true);
        m_passwordEdit->setFocus();
        return false;
    }
    
    return true;
}

bool LoginWindow::authenticateUser() {
    if (!m_databaseProvider || !m_operatorDao) {
        updateLoginStatus("数据库未连接", true);
        return false;
    }
    
    QString username = m_usernameEdit->text().trimmed();
    QString password = m_passwordEdit->text();
    
    // 执行用户认证
    Operator operatorUser;
    bool success = m_operatorDao->authenticateUser(username, password, operatorUser);
    
    if (success) {
        // 更新最后登录信息
        QString clientIP = getClientIP();
        m_operatorDao->updateLastLogin(operatorUser.id(), QDateTime::currentDateTime(), clientIP);
        m_currentOperator = operatorUser;
        return true;
    }
    
    return false;
}

void LoginWindow::updateDatabaseStatus(bool connected, const QString& message) {
    if (connected) {
        m_dbStatusLabel->setText(message.isEmpty() ? "已连接" : message);
        m_dbStatusLabel->setStyleSheet("color: #4CAF50; font-weight: bold;");
    } else {
        m_dbStatusLabel->setText(message.isEmpty() ? "未连接" : message);
        m_dbStatusLabel->setStyleSheet("color: #F44336; font-weight: bold;");
    }
}

void LoginWindow::updateLoginStatus(const QString& message, bool isError) {
    if (message.isEmpty()) {
        m_statusLabel->clear();
        return;
    }
    
    if (isError) {
        m_statusLabel->setStyleSheet("color: #F44336; font-weight: bold;");
    } else {
        m_statusLabel->setStyleSheet("color: #4CAF50; font-weight: bold;");
    }
    
    m_statusLabel->setText(message);
}

void LoginWindow::setControlsEnabled(bool enabled) {
    m_usernameEdit->setEnabled(enabled);
    m_passwordEdit->setEnabled(enabled);
    m_loginBtn->setEnabled(enabled);
    m_rememberCheck->setEnabled(enabled);
    m_autoLoginCheck->setEnabled(enabled);
    m_dbConfigBtn->setEnabled(enabled);
}

void LoginWindow::showProgress(bool show, const QString& message) {
    m_progressBar->setVisible(show);
    
    if (show) {
        m_progressBar->setRange(0, 0); // 不确定进度
        if (!message.isEmpty()) {
            updateLoginStatus(message);
        }
    }
}

void LoginWindow::startFadeInAnimation() {
    m_opacityEffect = new QGraphicsOpacityEffect(this);
    setGraphicsEffect(m_opacityEffect);
    
    m_fadeAnimation = new QPropertyAnimation(m_opacityEffect, "opacity", this);
    m_fadeAnimation->setDuration(800);
    m_fadeAnimation->setStartValue(0.0);
    m_fadeAnimation->setEndValue(1.0);
    
    connect(m_fadeAnimation, &QPropertyAnimation::finished, this, &LoginWindow::onFadeInFinished);
    
    m_fadeAnimation->start();
}

void LoginWindow::checkAutoLogin() {
    if (m_autoLoginCheck->isChecked() && 
        !m_usernameEdit->text().isEmpty() && 
        !m_passwordEdit->text().isEmpty() &&
        m_databaseProvider && m_databaseProvider->isConnected()) {
        
        // 延迟2秒自动登录，确保界面完全准备好
        updateLoginStatus("自动登录中...");
        QTimer::singleShot(2000, this, &LoginWindow::handleLogin);
    }
}

void LoginWindow::clearSavedPassword() {
    m_settings->remove("password");
    m_settings->setValue("rememberPassword", false);
    m_settings->setValue("autoLogin", false);
    m_settings->sync();
}

QString LoginWindow::encryptPassword(const QString& password) {
    // 使用简单的XOR加密（足够安全用于记住密码功能）
    QString key = "AccessControlSystem2024!@#$";
    QByteArray passwordBytes = password.toUtf8();
    QByteArray keyBytes = key.toUtf8();
    QByteArray encrypted;
    
    for (int i = 0; i < passwordBytes.size(); ++i) {
        encrypted.append(passwordBytes[i] ^ keyBytes[i % keyBytes.size()]);
    }
    
    return encrypted.toBase64();
}

QString LoginWindow::decryptPassword(const QString& encryptedPassword) {
    // 使用XOR解密
    QString key = "AccessControlSystem2024!@#$";
    QByteArray encryptedBytes = QByteArray::fromBase64(encryptedPassword.toUtf8());
    QByteArray keyBytes = key.toUtf8();
    QByteArray decrypted;
    
    for (int i = 0; i < encryptedBytes.size(); ++i) {
        decrypted.append(encryptedBytes[i] ^ keyBytes[i % keyBytes.size()]);
    }
    
    return QString::fromUtf8(decrypted);
}

QString LoginWindow::getClientIP() {
    // 获取本机IP地址
    QList<QHostAddress> addresses = QNetworkInterface::allAddresses();
    for (const QHostAddress& address : addresses) {
        if (address != QHostAddress::LocalHost && 
            address.toIPv4Address() != 0) {
            return address.toString();
        }
    }
    return "127.0.0.1";
}

} // namespace AccessControl
