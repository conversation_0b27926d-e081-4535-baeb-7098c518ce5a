#include "ConsumerCardListWidget.h"

#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QHeaderView>
#include <QMessageBox>
#include <QInputDialog>
#include <QTableWidgetItem>

namespace AccessControl {

ConsumerCardListWidget::ConsumerCardListWidget(QWidget *parent)
    : QWidget(parent)
    , m_cardTable(nullptr)
    , m_addButton(nullptr)
    , m_editButton(nullptr)
    , m_deleteButton(nullptr)
{
    initUI();
}

ConsumerCardListWidget::~ConsumerCardListWidget()
{
    // 析构函数内容
}

void ConsumerCardListWidget::initUI()
{
    // 创建主布局
    QVBoxLayout *mainLayout = new QVBoxLayout(this);
    
    // 创建表格
    m_cardTable = new QTableWidget(0, 2, this);
    m_cardTable->setHorizontalHeaderLabels(QStringList() << tr("卡号") << tr("状态"));
    m_cardTable->horizontalHeader()->setStretchLastSection(true);
    m_cardTable->setSelectionBehavior(QAbstractItemView::SelectRows);
    m_cardTable->setAlternatingRowColors(true);
    
    // 创建按钮布局
    QHBoxLayout *buttonLayout = new QHBoxLayout();
    
    // 添加按钮
    m_addButton = new QPushButton(tr("添加卡片"), this);
    connect(m_addButton, &QPushButton::clicked, this, &ConsumerCardListWidget::addCard);
    buttonLayout->addWidget(m_addButton);
    
    // 编辑按钮
    m_editButton = new QPushButton(tr("编辑卡片"), this);
    connect(m_editButton, &QPushButton::clicked, this, &ConsumerCardListWidget::editCard);
    buttonLayout->addWidget(m_editButton);
    
    // 删除按钮
    m_deleteButton = new QPushButton(tr("删除卡片"), this);
    connect(m_deleteButton, &QPushButton::clicked, this, &ConsumerCardListWidget::deleteCard);
    buttonLayout->addWidget(m_deleteButton);
    
    buttonLayout->addStretch();
    
    mainLayout->addWidget(m_cardTable);
    mainLayout->addLayout(buttonLayout);
    
    // 设置布局
    setLayout(mainLayout);
    
    // 连接表格选择变化信号
    connect(m_cardTable, &QTableWidget::itemSelectionChanged, this, [this]() {
        bool hasSelection = m_cardTable->currentRow() >= 0;
        m_editButton->setEnabled(hasSelection);
        m_deleteButton->setEnabled(hasSelection);
    });
    
    // 初始状态
    m_editButton->setEnabled(false);
    m_deleteButton->setEnabled(false);
}

void ConsumerCardListWidget::setCards(const QStringList& cards)
{
    m_cards = cards;
    refreshCardList();
}

QStringList ConsumerCardListWidget::cards() const
{
    return m_cards;
}

void ConsumerCardListWidget::addCard()
{
    bool ok;
    QString cardNumber = QInputDialog::getText(this, tr("添加卡片"), tr("请输入卡号:"), QLineEdit::Normal, QString(), &ok);
    
    if (ok && !cardNumber.isEmpty()) {
        // 检查卡号是否已存在
        if (m_cards.contains(cardNumber)) {
                QMessageBox::warning(this, tr("警告"), tr("卡号已存在！"));
                return;
            }
        
        m_cards.append(cardNumber);
        refreshCardList();
        
        emit cardsChanged();
    }
}

void ConsumerCardListWidget::editCard()
{
    int currentRow = m_cardTable->currentRow();
    if (currentRow < 0 || currentRow >= m_cards.size()) {
        return;
    }
    
    QString currentCard = m_cards[currentRow];
    
    bool ok;
    QString newCardNumber = QInputDialog::getText(this, tr("编辑卡片"), tr("请输入新卡号:"), 
                                                 QLineEdit::Normal, currentCard, &ok);
    
    if (ok && !newCardNumber.isEmpty()) {
        // 检查新卡号是否与其他卡片重复
        if (newCardNumber != currentCard && m_cards.contains(newCardNumber)) {
            QMessageBox::warning(this, tr("警告"), tr("卡号已存在！"));
            return;
        }
        
        m_cards[currentRow] = newCardNumber;
        refreshCardList();
        
        emit cardsChanged();
    }
}

void ConsumerCardListWidget::deleteCard()
{
    int currentRow = m_cardTable->currentRow();
    if (currentRow < 0 || currentRow >= m_cards.size()) {
        return;
    }
    
    QString cardNumber = m_cards[currentRow];
    
    if (QMessageBox::question(this, tr("确认删除"), 
                             tr("确定要删除卡号为 %1 的卡片吗？").arg(cardNumber),
                             QMessageBox::Yes | QMessageBox::No) == QMessageBox::Yes) {
        m_cards.removeAt(currentRow);
        refreshCardList();
    
    emit cardsChanged();
}
}

void ConsumerCardListWidget::refreshCardList()
{
    m_cardTable->setRowCount(m_cards.size());
    
    for (int i = 0; i < m_cards.size(); ++i) {
        const QString& cardNumber = m_cards[i];
        
        // 卡号
        m_cardTable->setItem(i, 0, new QTableWidgetItem(cardNumber));
        
        // 状态（简化为正常）
        m_cardTable->setItem(i, 1, new QTableWidgetItem(tr("正常")));
    }
}

} // namespace AccessControl 