/****************************************************************************
** Meta object code from reading C++ file 'ConsumerDialog.h'
**
** Created by: The Qt Meta Object Compiler version 68 (Qt 6.6.3)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../src/views/ConsumerDialog.h"
#include <QtGui/qtextcursor.h>
#include <QtCore/qmetatype.h>

#if __has_include(<QtCore/qtmochelpers.h>)
#include <QtCore/qtmochelpers.h>
#else
QT_BEGIN_MOC_NAMESPACE
#endif


#include <memory>

#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'ConsumerDialog.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 68
#error "This file was generated using the moc from 6.6.3. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {

#ifdef QT_MOC_HAS_STRINGDATA
struct qt_meta_stringdata_CLASSAccessControlSCOPEAddCardDialogENDCLASS_t {};
constexpr auto qt_meta_stringdata_CLASSAccessControlSCOPEAddCardDialogENDCLASS = QtMocHelpers::stringData(
    "AccessControl::AddCardDialog",
    "validateCardNumber",
    ""
);
#else  // !QT_MOC_HAS_STRING_DATA
struct qt_meta_stringdata_CLASSAccessControlSCOPEAddCardDialogENDCLASS_t {
    uint offsetsAndSizes[6];
    char stringdata0[29];
    char stringdata1[19];
    char stringdata2[1];
};
#define QT_MOC_LITERAL(ofs, len) \
    uint(sizeof(qt_meta_stringdata_CLASSAccessControlSCOPEAddCardDialogENDCLASS_t::offsetsAndSizes) + ofs), len 
Q_CONSTINIT static const qt_meta_stringdata_CLASSAccessControlSCOPEAddCardDialogENDCLASS_t qt_meta_stringdata_CLASSAccessControlSCOPEAddCardDialogENDCLASS = {
    {
        QT_MOC_LITERAL(0, 28),  // "AccessControl::AddCardDialog"
        QT_MOC_LITERAL(29, 18),  // "validateCardNumber"
        QT_MOC_LITERAL(48, 0)   // ""
    },
    "AccessControl::AddCardDialog",
    "validateCardNumber",
    ""
};
#undef QT_MOC_LITERAL
#endif // !QT_MOC_HAS_STRING_DATA
} // unnamed namespace

Q_CONSTINIT static const uint qt_meta_data_CLASSAccessControlSCOPEAddCardDialogENDCLASS[] = {

 // content:
      12,       // revision
       0,       // classname
       0,    0, // classinfo
       1,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // slots: name, argc, parameters, tag, flags, initial metatype offsets
       1,    0,   20,    2, 0x08,    1 /* Private */,

 // slots: parameters
    QMetaType::Void,

       0        // eod
};

Q_CONSTINIT const QMetaObject AccessControl::AddCardDialog::staticMetaObject = { {
    QMetaObject::SuperData::link<QDialog::staticMetaObject>(),
    qt_meta_stringdata_CLASSAccessControlSCOPEAddCardDialogENDCLASS.offsetsAndSizes,
    qt_meta_data_CLASSAccessControlSCOPEAddCardDialogENDCLASS,
    qt_static_metacall,
    nullptr,
    qt_incomplete_metaTypeArray<qt_meta_stringdata_CLASSAccessControlSCOPEAddCardDialogENDCLASS_t,
        // Q_OBJECT / Q_GADGET
        QtPrivate::TypeAndForceComplete<AddCardDialog, std::true_type>,
        // method 'validateCardNumber'
        QtPrivate::TypeAndForceComplete<void, std::false_type>
    >,
    nullptr
} };

void AccessControl::AddCardDialog::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<AddCardDialog *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->validateCardNumber(); break;
        default: ;
        }
    }
    (void)_a;
}

const QMetaObject *AccessControl::AddCardDialog::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *AccessControl::AddCardDialog::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_CLASSAccessControlSCOPEAddCardDialogENDCLASS.stringdata0))
        return static_cast<void*>(this);
    return QDialog::qt_metacast(_clname);
}

int AccessControl::AddCardDialog::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QDialog::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 1)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 1;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 1)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 1;
    }
    return _id;
}
namespace {

#ifdef QT_MOC_HAS_STRINGDATA
struct qt_meta_stringdata_CLASSAccessControlSCOPEConsumerDialogENDCLASS_t {};
constexpr auto qt_meta_stringdata_CLASSAccessControlSCOPEConsumerDialogENDCLASS = QtMocHelpers::stringData(
    "AccessControl::ConsumerDialog",
    "saveUser",
    "",
    "cancel",
    "addCard",
    "editCard",
    "deleteCard"
);
#else  // !QT_MOC_HAS_STRING_DATA
struct qt_meta_stringdata_CLASSAccessControlSCOPEConsumerDialogENDCLASS_t {
    uint offsetsAndSizes[14];
    char stringdata0[30];
    char stringdata1[9];
    char stringdata2[1];
    char stringdata3[7];
    char stringdata4[8];
    char stringdata5[9];
    char stringdata6[11];
};
#define QT_MOC_LITERAL(ofs, len) \
    uint(sizeof(qt_meta_stringdata_CLASSAccessControlSCOPEConsumerDialogENDCLASS_t::offsetsAndSizes) + ofs), len 
Q_CONSTINIT static const qt_meta_stringdata_CLASSAccessControlSCOPEConsumerDialogENDCLASS_t qt_meta_stringdata_CLASSAccessControlSCOPEConsumerDialogENDCLASS = {
    {
        QT_MOC_LITERAL(0, 29),  // "AccessControl::ConsumerDialog"
        QT_MOC_LITERAL(30, 8),  // "saveUser"
        QT_MOC_LITERAL(39, 0),  // ""
        QT_MOC_LITERAL(40, 6),  // "cancel"
        QT_MOC_LITERAL(47, 7),  // "addCard"
        QT_MOC_LITERAL(55, 8),  // "editCard"
        QT_MOC_LITERAL(64, 10)   // "deleteCard"
    },
    "AccessControl::ConsumerDialog",
    "saveUser",
    "",
    "cancel",
    "addCard",
    "editCard",
    "deleteCard"
};
#undef QT_MOC_LITERAL
#endif // !QT_MOC_HAS_STRING_DATA
} // unnamed namespace

Q_CONSTINIT static const uint qt_meta_data_CLASSAccessControlSCOPEConsumerDialogENDCLASS[] = {

 // content:
      12,       // revision
       0,       // classname
       0,    0, // classinfo
       5,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // slots: name, argc, parameters, tag, flags, initial metatype offsets
       1,    0,   44,    2, 0x08,    1 /* Private */,
       3,    0,   45,    2, 0x08,    2 /* Private */,
       4,    0,   46,    2, 0x08,    3 /* Private */,
       5,    0,   47,    2, 0x08,    4 /* Private */,
       6,    0,   48,    2, 0x08,    5 /* Private */,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

Q_CONSTINIT const QMetaObject AccessControl::ConsumerDialog::staticMetaObject = { {
    QMetaObject::SuperData::link<QDialog::staticMetaObject>(),
    qt_meta_stringdata_CLASSAccessControlSCOPEConsumerDialogENDCLASS.offsetsAndSizes,
    qt_meta_data_CLASSAccessControlSCOPEConsumerDialogENDCLASS,
    qt_static_metacall,
    nullptr,
    qt_incomplete_metaTypeArray<qt_meta_stringdata_CLASSAccessControlSCOPEConsumerDialogENDCLASS_t,
        // Q_OBJECT / Q_GADGET
        QtPrivate::TypeAndForceComplete<ConsumerDialog, std::true_type>,
        // method 'saveUser'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'cancel'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'addCard'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'editCard'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'deleteCard'
        QtPrivate::TypeAndForceComplete<void, std::false_type>
    >,
    nullptr
} };

void AccessControl::ConsumerDialog::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<ConsumerDialog *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->saveUser(); break;
        case 1: _t->cancel(); break;
        case 2: _t->addCard(); break;
        case 3: _t->editCard(); break;
        case 4: _t->deleteCard(); break;
        default: ;
        }
    }
    (void)_a;
}

const QMetaObject *AccessControl::ConsumerDialog::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *AccessControl::ConsumerDialog::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_CLASSAccessControlSCOPEConsumerDialogENDCLASS.stringdata0))
        return static_cast<void*>(this);
    return QDialog::qt_metacast(_clname);
}

int AccessControl::ConsumerDialog::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QDialog::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 5)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 5;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 5)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 5;
    }
    return _id;
}
QT_WARNING_POP
