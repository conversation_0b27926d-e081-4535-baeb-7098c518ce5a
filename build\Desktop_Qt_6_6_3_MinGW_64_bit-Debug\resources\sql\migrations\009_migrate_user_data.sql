-- 迁移文件：009_migrate_user_data.sql
-- 描述：将现有用户数据迁移到新的consumers表中，并更新user_cards表
-- 创建时间：2024-08-15
-- 作者：AI Assistant

-- ========== 迁移用户数据到consumers表 ==========

-- 将users表中的门禁卡持有者数据迁移到consumers表
INSERT INTO consumers (
    work_number, 
    real_name, 
    phone_number, 
    id_number, 
    department_id, 
    status, 
    access_enabled, 
    attendance_enabled, 
    shift_work, 
    valid_from, 
    valid_until, 
    created_at, 
    updated_at
)
SELECT
    work_number,
    real_name,
    phone_number,
    id_number,
    department_id,
    status,
    access_enabled,
    attendance_enabled,
    shift_work,
    valid_from,
    valid_until,
    created_at,
    updated_at
FROM users
WHERE role > 1; -- 只迁移操作员和查看者角色的用户，超级管理员和管理员保留在users表中

-- ========== 迁移卡片数据 ==========

-- 将user_cards表中的数据迁移到user_cards_new表
INSERT INTO user_cards_new (
    consumer_id,
    card_number,
    card_type,
    status,
    valid_from,
    valid_until,
    is_primary,
    created_at,
    updated_at
)
SELECT
    c.id, -- 使用新的consumer_id
    uc.card_number,
    uc.card_type,
    uc.status,
    uc.valid_from,
    uc.valid_until,
    uc.is_primary,
    uc.created_at,
    uc.updated_at
FROM user_cards uc
JOIN users u ON uc.user_id = u.id
JOIN consumers c ON u.work_number = c.work_number
WHERE u.role > 1; -- 只迁移操作员和查看者角色的用户的卡片

-- ========== 重命名表 ==========

-- 备份原始user_cards表
ALTER TABLE user_cards RENAME TO user_cards_old;

-- 将新表重命名为user_cards
ALTER TABLE user_cards_new RENAME TO user_cards;

-- ========== 清理数据 ==========

-- 从users表中删除已迁移的门禁卡持有者数据
-- 注意：此操作会永久删除数据，请确保已正确迁移
-- DELETE FROM users WHERE role > 1;

-- 注意：此迁移文件包含删除操作，请谨慎执行
-- 建议在执行前备份数据库，并在测试环境中验证迁移结果 