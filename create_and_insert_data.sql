-- 创建users表（如果不存在）
CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    display_name VARCHAR(100),
    email VARCHAR(100),
    phone VARCHAR(20),
    phone_number VARCHAR(20),
    id_number VA<PERSON>HAR(32),
    password_hash VARCHAR(255) NOT NULL,
    salt VARCHAR(50),
    role INTEGER DEFAULT 2,
    status INTEGER DEFAULT 0,
    access_enabled BOOLEAN DEFAULT 1,
    attendance_enabled BOOLEAN DEFAULT 1,
    valid_from DATE,
    valid_until DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login_at TIMESTAMP,
    password_changed_at TIMESTAMP,
    login_attempts INTEGER DEFAULT 0,
    last_login_ip VARCHAR(45),
    permissions TEXT
);

-- 创建user_profiles表（如果不存在）
CREATE TABLE IF NOT EXISTS user_profiles (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    gender INTEGER DEFAULT 0,
    ethnicity VARCHAR(50),
    religion VARCHAR(50),
    native_place VARCHAR(100),
    birth_date DATE,
    marital_status INTEGER DEFAULT 0,
    political_status VARCHAR(50),
    education VARCHAR(50),
    work_phone VARCHAR(20),
    home_phone VARCHAR(20),
    english_name VARCHAR(100),
    organization VARCHAR(200),
    job_title VARCHAR(100),
    skill_level VARCHAR(100),
    certificate_name VARCHAR(100),
    certificate_number VARCHAR(50),
    social_security_number VARCHAR(30),
    hire_date DATE,
    termination_date DATE,
    email_address VARCHAR(100),
    mailing_address TEXT,
    postal_code VARCHAR(10),
    remarks TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 清空现有数据
DELETE FROM user_profiles;
DELETE FROM users;

-- 插入测试数据
INSERT INTO users (
    username, display_name, email, phone, phone_number, id_number,
    password_hash, role, status, access_enabled, attendance_enabled,
    valid_from, valid_until, created_at
) VALUES 
('admin', '系统管理员', '<EMAIL>', '010-12345678', '13800138000', '110101199001011234', 'admin_hash', 0, 0, 1, 1, '2024-01-01', '2099-12-31', '2024-06-01 10:00:00'),
('zhangsan', '张三', '<EMAIL>', '021-87654321', '13900139000', '110101198512123456', 'user_hash', 2, 0, 1, 1, '2024-01-01', '2099-12-31', '2024-06-01 10:00:00'),
('lisi', '李四', '<EMAIL>', '0755-88889999', '13700137000', '110101197703045678', 'user_hash', 2, 0, 1, 0, '2024-01-01', '2099-12-31', '2024-06-01 10:00:00'),
('wangwu', '王五', '<EMAIL>', '020-11223344', '13600136000', '110101200001017890', 'user_hash', 2, 1, 0, 1, '2024-01-01', '2099-12-31', '2024-06-01 10:00:00'),
('zhaoliu', '赵六', '<EMAIL>', '023-55667788', '13500135000', '110101199912312345', 'user_hash', 2, 0, 1, 1, '2024-01-01', '2099-12-31', '2024-06-01 10:00:00'),
('sunqi', '孙七', '<EMAIL>', '029-99887766', '13400134000', '110101198806154321', 'user_hash', 2, 0, 1, 1, '2024-01-01', '2099-12-31', '2024-06-01 10:00:00'),
('manager1', '部门经理', '<EMAIL>', '010-11111111', '13300133000', '110101198203156789', 'manager_hash', 1, 0, 1, 1, '2024-01-01', '2099-12-31', '2024-06-01 10:00:00'),
('operator1', '操作员', '<EMAIL>', '010-22222222', '13200132000', '110101199504208765', 'operator_hash', 3, 0, 1, 1, '2024-01-01', '2099-12-31', '2024-06-01 10:00:00');

-- 插入一些用户扩展信息示例
INSERT INTO user_profiles (
    user_id, gender, ethnicity, religion, native_place, birth_date,
    marital_status, political_status, education, organization, job_title,
    work_phone, email_address, created_at, updated_at
) VALUES 
(2, 1, '汉族', '无', '北京市朝阳区', '1985-12-12', 1, '群众', '本科', '技术部', '软件工程师', '021-87654321', '<EMAIL>', '2024-06-01 10:00:00', '2024-06-01 10:00:00'),
(3, 2, '汉族', '无', '广东省深圳市', '1977-03-04', 1, '党员', '硕士', '产品部', '产品经理', '0755-88889999', '<EMAIL>', '2024-06-01 10:00:00', '2024-06-01 10:00:00'),
(4, 1, '汉族', '无', '广东省广州市', '2000-01-01', 0, '群众', '大专', '销售部', '销售代表', '020-11223344', '<EMAIL>', '2024-06-01 10:00:00', '2024-06-01 10:00:00');

-- 验证插入的数据
SELECT 'Users表数据:' as table_name;
SELECT username, display_name, phone_number, id_number FROM users;

SELECT 'User_profiles表数据:' as table_name;
SELECT up.user_id, u.username, up.ethnicity, up.organization, up.job_title 
FROM user_profiles up 
JOIN users u ON up.user_id = u.id; 