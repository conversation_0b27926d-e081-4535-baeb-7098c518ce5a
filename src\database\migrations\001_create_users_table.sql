-- 迁移文件：001_create_users_table.sql
-- 描述：创建用户表，包含基本信息字段
-- 创建时间：2024-08-03
-- 作者：AI Assistant

-- ========== 创建users表 ==========

-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(100) NOT NULL,
    salt VARCHAR(50) NOT NULL,
    role INTEGER NOT NULL DEFAULT 3, -- 0:超级管理员, 1:管理员, 2:操作员, 3:查看者
    status INTEGER NOT NULL DEFAULT 1, -- 0:正常, 1:停用, 2:锁定, 3:过期
    real_name VARCHAR(100),
    email VARCHAR(100),
    phone_number VARCHAR(20),
    id_number VARCHAR(18),
    access_enabled INTEGER NOT NULL DEFAULT 1,
    attendance_enabled INTEGER NOT NULL DEFAULT 1,
    valid_from DATE DEFAULT (date('now')),
    valid_until DATE DEFAULT '2099-12-31',
    department_id INTEGER DEFAULT -1,
    work_number VARCHAR(50),
    shift_work INTEGER DEFAULT 0,
    last_login_at TIMESTAMP,
    last_login_ip VARCHAR(50),
    login_attempts INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_real_name ON users(real_name);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_users_status ON users(status);
CREATE INDEX IF NOT EXISTS idx_users_department_id ON users(department_id);
CREATE INDEX IF NOT EXISTS idx_users_work_number ON users(work_number);
CREATE INDEX IF NOT EXISTS idx_users_phone_number ON users(phone_number);
CREATE INDEX IF NOT EXISTS idx_users_id_number ON users(id_number);

-- 创建触发器，在更新用户时自动更新updated_at字段
CREATE TRIGGER IF NOT EXISTS trigger_users_update 
    AFTER UPDATE ON users
    FOR EACH ROW
BEGIN
    UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- 创建默认的超级管理员用户（密码：admin）
INSERT INTO users (
    username, password_hash, salt, role, status, real_name, 
    email, created_at, updated_at
) VALUES (
    'admin',
    'f4efe8fa7456131801f6e16c55d204a2b0e0816c61cbc1bb38d13809e07d676b', -- admin+salt的SHA-256哈希值
    '00000000-0000-0000-0000-000000000000', 
    0, -- 超级管理员
    0, -- 正常
    '系统管理员',
    '<EMAIL>',
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
); 