#ifndef CONSUMERCARDDIALOG_H
#define CONSUMERCARDDIALOG_H

#include <QDialog>
#include <QLineEdit>
#include <QComboBox>
#include <QCheckBox>
#include <QPushButton>
#include <QTableWidget>

// 前向声明
class CardLineEdit;

namespace AccessControl {

/**
 * @brief 卡片管理对话框
 */
class ConsumerCardDialog : public QDialog
{
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param parent 父窗口
     */
    explicit ConsumerCardDialog(QWidget *parent = nullptr);

    /**
     * @brief 析构函数
     */
    ~ConsumerCardDialog();

    /**
     * @brief 获取卡片数据（用于与主对话框交换数据）
     * @return 卡片数据列表
     */
    QList<QStringList> getCardData() const;

    /**
     * @brief 设置卡片数据
     * @param cardData 卡片数据列表
     */
    void setCardData(const QList<QStringList>& cardData);

private slots:
    /**
     * @brief 添加卡片
     */
    void addCard();

    /**
     * @brief 编辑卡片
     */
    void editCard();

    /**
     * @brief 删除卡片
     */
    void deleteCard();

    /**
     * @brief 卡号输入验证
     */
    void validateCardNumber();

    /**
     * @brief 确认保存
     */
    void accept() override;

private:
    /**
     * @brief 初始化UI
     */
    void initUI();

    /**
     * @brief 格式化卡号（移除前导零，验证范围）
     * @param cardNumber 原始卡号
     * @return 格式化后的卡号
     */
    QString formatCardNumber(const QString& cardNumber);

private:
    // 卡片输入控件
    CardLineEdit* m_cardNumberEdit;      // 卡号输入（支持USB读卡器）
    QComboBox* m_cardTypeCombo;          // 卡类型
    QCheckBox* m_isPrimaryCardCheck;     // 是否主卡
    QPushButton* m_addCardButton;        // 添加卡片
    QPushButton* m_editCardButton;       // 编辑卡片
    QPushButton* m_deleteCardButton;     // 删除卡片

    // 卡片列表
    QTableWidget* m_cardTable;           // 卡片列表

    // 按钮
    QPushButton* m_okButton;             // 确定按钮
    QPushButton* m_cancelButton;         // 取消按钮
};

} // namespace AccessControl

#endif // CONSUMERCARDDIALOG_H