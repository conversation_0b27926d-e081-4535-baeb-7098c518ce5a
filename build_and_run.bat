@echo off
echo 正在构建 AccessControlSystem...

REM 创建build目录（如果不存在）
if not exist build mkdir build

REM 进入build目录
cd build

REM 设置Qt路径
set CMAKE_PREFIX_PATH=C:\Qt\6.6.3\mingw_64

REM 配置项目
cmake -G "MinGW Makefiles" -DCMAKE_PREFIX_PATH=C:\Qt\6.6.3\mingw_64 ..

REM 构建项目
cmake --build .

REM 如果构建成功，运行程序
if %ERRORLEVEL% EQU 0 (
    echo 构建成功，正在启动程序...
    cd ..
    .\build\AccessControlSystem.exe
) else (
    echo 构建失败，错误代码: %ERRORLEVEL%
    pause
) 