@echo off
echo 运行程序并捕获日志...

REM 创建日志目录
if not exist "logs" mkdir logs

REM 设置日志文件
set LOG_FILE=logs\app_log_%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%.txt
set LOG_FILE=%LOG_FILE: =0%

REM 重置数据库
echo 重置数据库...
set DB_PATH=%APPDATA%\AccessControl Inc.\AccessControlSystem\access_control.db
if exist "%DB_PATH%" (
    echo 删除现有数据库文件: %DB_PATH% >> %LOG_FILE%
    del /f /q "%DB_PATH%"
)

REM 设置环境变量，让应用记录详细日志
set QT_LOGGING_RULES="*.debug=true"
set QT_MESSAGE_PATTERN="[%{time hh:mm:ss.zzz}] [%{type}] %{function}: %{message}"

REM 运行应用程序并捕获输出
echo 运行应用程序...
echo ========== 程序日志开始 ==========  > %LOG_FILE%
echo 运行时间: %date% %time% >> %LOG_FILE%
.\build\simple\AccessControlSystem.exe >> %LOG_FILE% 2>&1
echo ========== 程序日志结束 ==========  >> %LOG_FILE%

echo 程序运行结束，日志保存在: %LOG_FILE%
notepad %LOG_FILE% 