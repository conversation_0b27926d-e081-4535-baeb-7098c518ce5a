#include "DepartmentManagementWidget.h"
#include <QMessageBox>

namespace AccessControl {

DepartmentManagementWidget::DepartmentManagementWidget(std::shared_ptr<IDatabaseProvider> db<PERSON><PERSON>ider, 
                                                     QWidget *parent)
    : QWidget(parent)
    , m_db<PERSON><PERSON><PERSON>(dbProvider)
    , m_isEditMode(false)
{
    m_departmentDao = std::make_unique<DepartmentDao>(m_dbProvider);
    initializeUI();
    connectSignals();
    setWindowTitle("部门管理");
    resize(1000, 700);
}

DepartmentManagementWidget::~DepartmentManagementWidget()
{
}

void DepartmentManagementWidget::refreshDepartments()
{
    loadDepartmentTree();
}

void DepartmentManagementWidget::initializeUI()
{
    m_mainLayout = new QHBoxLayout(this);
    m_splitter = new QSplitter(Qt::Horizontal);
    m_mainLayout->addWidget(m_splitter);
    
    initializeLeftPanel();
    initializeRightPanel();
}

void DepartmentManagementWidget::initializeLeftPanel()
{
    m_leftPanel = new QWidget();
    m_leftLayout = new QVBoxLayout(m_leftPanel);
    
    initializeToolbar();
    
    m_treeWidget = new QTreeWidget();
    m_treeWidget->setHeaderLabels({"部门", "代码", "状态"});
    
    m_leftLayout->addWidget(m_toolbar);
    m_leftLayout->addWidget(m_treeWidget);
    
    m_splitter->addWidget(m_leftPanel);
}

void DepartmentManagementWidget::initializeRightPanel()
{
    m_rightPanel = new QWidget();
    m_rightLayout = new QVBoxLayout(m_rightPanel);
    
    m_detailsGroup = new QGroupBox("部门信息");
    m_detailsLayout = new QGridLayout(m_detailsGroup);
    
    m_lblName = new QLabel("部门名称:");
    m_editName = new QLineEdit();
    m_lblCode = new QLabel("部门代码:");
    m_editCode = new QLineEdit();
    m_lblDescription = new QLabel("部门描述:");
    m_editDescription = new QTextEdit();
    m_lblStatus = new QLabel("状态:");
    m_comboStatus = new QComboBox();
    m_lblParent = new QLabel("上级部门:");
    m_comboParent = new QComboBox();
    m_lblSortOrder = new QLabel("排序:");
    m_spinSortOrder = new QSpinBox();
    
    m_comboStatus->addItem("启用", 1);
    m_comboStatus->addItem("禁用", 0);
    
    m_detailsLayout->addWidget(m_lblName, 0, 0);
    m_detailsLayout->addWidget(m_editName, 0, 1);
    m_detailsLayout->addWidget(m_lblCode, 0, 2);
    m_detailsLayout->addWidget(m_editCode, 0, 3);
    m_detailsLayout->addWidget(m_lblParent, 1, 0);
    m_detailsLayout->addWidget(m_comboParent, 1, 1, 1, 3);
    m_detailsLayout->addWidget(m_lblStatus, 2, 0);
    m_detailsLayout->addWidget(m_comboStatus, 2, 1);
    m_detailsLayout->addWidget(m_lblSortOrder, 2, 2);
    m_detailsLayout->addWidget(m_spinSortOrder, 2, 3);
    m_detailsLayout->addWidget(m_lblDescription, 3, 0);
    m_detailsLayout->addWidget(m_editDescription, 3, 1, 1, 3);
    
    m_buttonWidget = new QWidget();
    m_buttonLayout = new QHBoxLayout(m_buttonWidget);
    m_btnSave = new QPushButton("保存");
    m_btnCancel = new QPushButton("取消");
    m_buttonLayout->addStretch();
    m_buttonLayout->addWidget(m_btnSave);
    m_buttonLayout->addWidget(m_btnCancel);
    
    m_statsGroup = new QGroupBox("统计信息");
    m_statsLayout = new QGridLayout(m_statsGroup);
    m_lblChildCount = new QLabel("子部门数量: 0");
    m_lblDirectUserCount = new QLabel("直接人员: 0");
    m_lblTotalUserCount = new QLabel("总人员: 0");
    m_lblCreatedAt = new QLabel("创建时间: -");
    m_lblUpdatedAt = new QLabel("更新时间: -");
    
    m_statsLayout->addWidget(m_lblChildCount, 0, 0);
    m_statsLayout->addWidget(m_lblDirectUserCount, 0, 1);
    m_statsLayout->addWidget(m_lblTotalUserCount, 0, 2);
    m_statsLayout->addWidget(m_lblCreatedAt, 1, 0, 1, 2);
    m_statsLayout->addWidget(m_lblUpdatedAt, 1, 2);
    
    m_rightLayout->addWidget(m_detailsGroup);
    m_rightLayout->addWidget(m_buttonWidget);
    m_rightLayout->addWidget(m_statsGroup);
    m_rightLayout->addStretch();
    
    m_splitter->addWidget(m_rightPanel);
    
    setFormEditMode(false);
}

void DepartmentManagementWidget::initializeToolbar()
{
    m_toolbar = new QWidget();
    m_toolbarLayout = new QHBoxLayout(m_toolbar);
    
    m_btnAdd = new QPushButton("添加部门");
    m_btnAddSub = new QPushButton("添加子部门");
    m_btnEdit = new QPushButton("编辑");
    m_btnDelete = new QPushButton("删除");
    m_btnExpandAll = new QPushButton("展开全部");
    m_btnCollapseAll = new QPushButton("收起全部");
    m_btnImport = new QPushButton("导入");
    m_btnExport = new QPushButton("导出");
    
    m_btnAddSub->setEnabled(false);
    m_btnEdit->setEnabled(false);
    m_btnDelete->setEnabled(false);
    
    m_toolbarLayout->addWidget(m_btnAdd);
    m_toolbarLayout->addWidget(m_btnAddSub);
    m_toolbarLayout->addWidget(m_btnEdit);
    m_toolbarLayout->addWidget(m_btnDelete);
    m_toolbarLayout->addStretch();
    m_toolbarLayout->addWidget(m_btnExpandAll);
    m_toolbarLayout->addWidget(m_btnCollapseAll);
    m_toolbarLayout->addStretch();
    m_toolbarLayout->addWidget(m_btnImport);
    m_toolbarLayout->addWidget(m_btnExport);
}

void DepartmentManagementWidget::initializeContextMenu()
{
    // TODO: 实现右键菜单
}

void DepartmentManagementWidget::connectSignals()
{
    connect(m_treeWidget, &QTreeWidget::itemSelectionChanged,
            this, &DepartmentManagementWidget::onTreeSelectionChanged);
    connect(m_btnAdd, &QPushButton::clicked, this, &DepartmentManagementWidget::onAddDepartment);
    connect(m_btnAddSub, &QPushButton::clicked, this, &DepartmentManagementWidget::onAddSubDepartment);
    connect(m_btnEdit, &QPushButton::clicked, this, &DepartmentManagementWidget::onEditDepartment);
    connect(m_btnDelete, &QPushButton::clicked, this, &DepartmentManagementWidget::onDeleteDepartment);
    connect(m_btnExpandAll, &QPushButton::clicked, m_treeWidget, &QTreeWidget::expandAll);
    connect(m_btnCollapseAll, &QPushButton::clicked, m_treeWidget, &QTreeWidget::collapseAll);
    connect(m_btnSave, &QPushButton::clicked, this, &DepartmentManagementWidget::onSaveDepartment);
    connect(m_btnCancel, &QPushButton::clicked, this, &DepartmentManagementWidget::onCancelEdit);
}

void DepartmentManagementWidget::loadDepartmentTree()
{
    m_treeWidget->clear();
    
    QList<std::shared_ptr<DepartmentTreeNode>> rootNodes = m_departmentDao->getDepartmentTree();
    buildTreeItems(rootNodes);
    
    m_comboParent->clear();
    m_comboParent->addItem("无（根部门）", 0);
    
    QList<Department> departments = m_departmentDao->findActive();
    for (const Department& dept : departments) {
        QString displayText = dept.fullPath().isEmpty() ? dept.name() : dept.fullPath();
        m_comboParent->addItem(displayText, dept.id());
    }
}

void DepartmentManagementWidget::buildTreeItems(const QList<std::shared_ptr<DepartmentTreeNode>>& nodes, 
                                               QTreeWidgetItem* parentItem)
{
    for (auto& node : nodes) {
        QTreeWidgetItem* item = nullptr;
        
        if (parentItem) {
            item = new QTreeWidgetItem(parentItem);
        } else {
            item = new QTreeWidgetItem(m_treeWidget);
        }
        
        item->setText(0, node->department.name());
        item->setText(1, node->department.code());
        item->setText(2, node->department.statusText());
        item->setData(0, Qt::UserRole, node->department.id());
        
        buildTreeItems(node->children, item);
    }
}

// 槽函数实现
void DepartmentManagementWidget::onTreeSelectionChanged()
{
    QTreeWidgetItem* item = m_treeWidget->currentItem();
    bool hasSelection = (item != nullptr);
    
    m_btnAddSub->setEnabled(hasSelection);
    m_btnEdit->setEnabled(hasSelection);
    m_btnDelete->setEnabled(hasSelection);
    
    if (hasSelection) {
        int departmentId = item->data(0, Qt::UserRole).toInt();
        Department department = m_departmentDao->findById(departmentId);
        
        if (department.id() > 0) {
            m_currentDepartment = department;
            showDepartmentDetails(department);
            updateDepartmentStats(departmentId);
        }
    } else {
        m_currentDepartment = Department();
        clearDetailsForm();
    }
}

void DepartmentManagementWidget::onAddDepartment()
{
    m_currentDepartment = Department();
    m_currentDepartment.setParentId(0);
    m_isEditMode = true;
    
    clearDetailsForm();
    setFormEditMode(true);
    m_editName->setFocus();
}

void DepartmentManagementWidget::onAddSubDepartment()
{
    QTreeWidgetItem* item = m_treeWidget->currentItem();
    if (!item) return;
    
    int parentId = item->data(0, Qt::UserRole).toInt();
    
    m_currentDepartment = Department();
    m_currentDepartment.setParentId(parentId);
    m_isEditMode = true;
    
    clearDetailsForm();
    setFormEditMode(true);
    
    for (int i = 0; i < m_comboParent->count(); ++i) {
        if (m_comboParent->itemData(i).toInt() == parentId) {
            m_comboParent->setCurrentIndex(i);
            break;
        }
    }
    
    m_editName->setFocus();
}

void DepartmentManagementWidget::onEditDepartment()
{
    if (m_currentDepartment.id() <= 0) return;
    
    m_isEditMode = true;
    setFormEditMode(true);
    m_editName->setFocus();
}

void DepartmentManagementWidget::onDeleteDepartment()
{
    if (m_currentDepartment.id() <= 0) return;
    
    if (!m_departmentDao->canDelete(m_currentDepartment.id())) {
        QMessageBox::warning(this, "删除失败", 
                           "该部门包含子部门或关联用户，无法删除！");
        return;
    }
    
    int ret = QMessageBox::question(this, "确认删除", 
                                   QString("确定要删除部门 \"%1\" 吗？")
                                   .arg(m_currentDepartment.name()),
                                   QMessageBox::Yes | QMessageBox::No);
    
    if (ret == QMessageBox::Yes) {
        if (m_departmentDao->deleteDepartment(m_currentDepartment.id())) {
            QMessageBox::information(this, "删除成功", "部门删除成功！");
            refreshDepartments();
            clearDetailsForm();
        } else {
            QMessageBox::critical(this, "删除失败", "删除部门时发生错误！");
        }
    }
}

void DepartmentManagementWidget::onSaveDepartment()
{
    auto validation = validateForm();
    if (!validation.first) {
        QMessageBox::warning(this, "输入错误", validation.second);
        return;
    }
    
    Department department = getDepartmentFromForm();
    
    bool success = false;
    if (m_currentDepartment.id() <= 0) {
        int departmentId = m_departmentDao->createDepartment(department);
        success = (departmentId > 0);
        if (success) {
            m_currentDepartment = department;
            m_currentDepartment.setId(departmentId);
        }
    } else {
        department.setId(m_currentDepartment.id());
        department.setCreatedAt(m_currentDepartment.createdAt());
        success = m_departmentDao->updateDepartment(department);
        if (success) {
            m_currentDepartment = department;
        }
    }
    
    if (success) {
        QMessageBox::information(this, "保存成功", "部门信息保存成功！");
        setFormEditMode(false);
        m_isEditMode = false;
        refreshDepartments();
    } else {
        QMessageBox::critical(this, "保存失败", "保存部门信息时发生错误！");
    }
}

void DepartmentManagementWidget::onCancelEdit()
{
    setFormEditMode(false);
    m_isEditMode = false;
    
    if (m_currentDepartment.id() > 0) {
        showDepartmentDetails(m_currentDepartment);
    } else {
        clearDetailsForm();
    }
}

void DepartmentManagementWidget::onSearchDepartments()
{
    // TODO: 实现搜索功能
}

void DepartmentManagementWidget::onClearSearch()
{
    // TODO: 实现清空搜索功能
}

void DepartmentManagementWidget::onExpandAll()
{
    m_treeWidget->expandAll();
}

void DepartmentManagementWidget::onCollapseAll()
{
    m_treeWidget->collapseAll();
}

void DepartmentManagementWidget::onImportDepartments()
{
    QMessageBox::information(this, "功能提示", "部门导入功能正在开发中...");
}

void DepartmentManagementWidget::onExportDepartments()
{
    QMessageBox::information(this, "功能提示", "部门导出功能正在开发中...");
}

void DepartmentManagementWidget::onShowContextMenu(const QPoint& pos)
{
    // TODO: 实现右键菜单
}

void DepartmentManagementWidget::showDepartmentDetails(const Department& department)
{
    m_editName->setText(department.name());
    m_editCode->setText(department.code());
    m_editDescription->setPlainText(department.description());
    
    for (int i = 0; i < m_comboStatus->count(); ++i) {
        if (m_comboStatus->itemData(i).toInt() == static_cast<int>(department.status())) {
            m_comboStatus->setCurrentIndex(i);
            break;
        }
    }
    
    for (int i = 0; i < m_comboParent->count(); ++i) {
        if (m_comboParent->itemData(i).toInt() == department.parentId()) {
            m_comboParent->setCurrentIndex(i);
            break;
        }
    }
    
    m_spinSortOrder->setValue(department.sortOrder());
    
    m_lblCreatedAt->setText(QString("创建时间: %1").arg(department.createdAt().toString("yyyy-MM-dd hh:mm:ss")));
    m_lblUpdatedAt->setText(QString("更新时间: %1").arg(department.updatedAt().toString("yyyy-MM-dd hh:mm:ss")));
}

void DepartmentManagementWidget::clearDetailsForm()
{
    m_editName->clear();
    m_editCode->clear();
    m_editDescription->clear();
    m_comboStatus->setCurrentIndex(0);
    m_comboParent->setCurrentIndex(0);
    m_spinSortOrder->setValue(0);
    
    m_lblChildCount->setText("子部门数量: 0");
    m_lblDirectUserCount->setText("直接人员: 0");
    m_lblTotalUserCount->setText("总人员: 0");
    m_lblCreatedAt->setText("创建时间: -");
    m_lblUpdatedAt->setText("更新时间: -");
}

void DepartmentManagementWidget::setFormEditMode(bool editMode)
{
    m_editName->setReadOnly(!editMode);
    m_editCode->setReadOnly(!editMode);
    m_editDescription->setReadOnly(!editMode);
    m_comboStatus->setEnabled(editMode);
    m_comboParent->setEnabled(editMode);
    m_spinSortOrder->setReadOnly(!editMode);
    
    m_btnSave->setVisible(editMode);
    m_btnCancel->setVisible(editMode);
}

Department DepartmentManagementWidget::getDepartmentFromForm()
{
    Department department;
    
    department.setName(m_editName->text().trimmed());
    department.setCode(m_editCode->text().trimmed());
    department.setDescription(m_editDescription->toPlainText().trimmed());
    department.setStatus(static_cast<Department::Status>(m_comboStatus->currentData().toInt()));
    department.setParentId(m_comboParent->currentData().toInt());
    department.setSortOrder(m_spinSortOrder->value());
    
    return department;
}

QPair<bool, QString> DepartmentManagementWidget::validateForm()
{
    QString name = m_editName->text().trimmed();
    QString code = m_editCode->text().trimmed();
    
    if (name.isEmpty()) {
        return qMakePair(false, "部门名称不能为空！");
    }
    
    if (code.isEmpty()) {
        return qMakePair(false, "部门代码不能为空！");
    }
    
    if (m_departmentDao->isCodeExists(code, m_currentDepartment.id())) {
        return qMakePair(false, QString("部门代码 \"%1\" 已存在！").arg(code));
    }
    
    return qMakePair(true, "");
}

QTreeWidgetItem* DepartmentManagementWidget::findTreeItem(int departmentId, QTreeWidgetItem* parentItem)
{
    // TODO: 实现查找树形项目
    return nullptr;
}

void DepartmentManagementWidget::updateDepartmentStats(int departmentId)
{
    if (departmentId <= 0) return;
    
    auto stats = m_departmentDao->getDepartmentStats(departmentId);
    
    m_lblChildCount->setText(QString("子部门数量: %1").arg(stats.childCount));
    m_lblDirectUserCount->setText(QString("直接人员: %1").arg(stats.directUserCount));
    m_lblTotalUserCount->setText(QString("总人员: %1").arg(stats.totalUserCount));
}

void DepartmentManagementWidget::applySearchFilter(const QString& keyword)
{
    // TODO: 实现搜索过滤
}

bool DepartmentManagementWidget::filterTreeItem(QTreeWidgetItem* item, const QString& keyword)
{
    // TODO: 实现树形项目过滤
    return true;
}

} // namespace AccessControl 