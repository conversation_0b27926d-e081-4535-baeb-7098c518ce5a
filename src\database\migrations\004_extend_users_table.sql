-- 迁移文件：004_extend_users_table.sql
-- 描述：扩展用户表结构，添加手机号、身份证等字段，并创建用户扩展信息、用户卡片、用户生物识别表
-- 创建时间：2024-12-19
-- 作者：AI Assistant

-- ========== 扩展users表 ==========

-- 添加手机号字段
ALTER TABLE users ADD COLUMN phone_number VARCHAR(20);

-- 添加身份证号字段
ALTER TABLE users ADD COLUMN id_number VARCHAR(18);

-- 添加门禁启用状态字段
ALTER TABLE users ADD COLUMN access_enabled INTEGER NOT NULL DEFAULT 1;

-- 添加考勤启用状态字段
ALTER TABLE users ADD COLUMN attendance_enabled INTEGER NOT NULL DEFAULT 1;

-- 添加权限有效期开始时间字段
ALTER TABLE users ADD COLUMN valid_from DATE DEFAULT (date('now'));

-- 添加权限有效期结束时间字段
ALTER TABLE users ADD COLUMN valid_until DATE DEFAULT '2099-12-31';

-- ========== 创建用户扩展信息表 ==========

CREATE TABLE IF NOT EXISTS user_profiles (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    gender INTEGER DEFAULT 0,  -- 0:未知, 1:男, 2:女
    ethnicity VARCHAR(50),     -- 民族
    religion VARCHAR(50),      -- 宗教
    native_place VARCHAR(100), -- 籍贯
    birth_date DATE,           -- 出生年月
    marital_status INTEGER DEFAULT 0, -- 0:未知, 1:未婚, 2:已婚, 3:离异, 4:丧偶
    political_status VARCHAR(50),     -- 政治面貌
    education VARCHAR(50),            -- 学历
    work_phone VARCHAR(20),           -- 工作电话
    home_phone VARCHAR(20),           -- 家庭电话
    english_name VARCHAR(100),        -- 英文名
    organization VARCHAR(200),        -- 单位
    job_title VARCHAR(100),           -- 职称
    skill_level VARCHAR(100),         -- 技术等级
    certificate_name VARCHAR(100),    -- 证件名称
    certificate_number VARCHAR(50),   -- 证件号
    social_security_number VARCHAR(30), -- 社保号
    hire_date DATE,                   -- 入职时间
    termination_date DATE,            -- 离职时间
    email_address VARCHAR(100),       -- 电子邮箱
    mailing_address TEXT,             -- 通讯地址
    postal_code VARCHAR(10),          -- 邮编
    remarks TEXT,                     -- 备注
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_user_profiles_user_id ON user_profiles(user_id);
CREATE INDEX IF NOT EXISTS idx_user_profiles_birth_date ON user_profiles(birth_date);
CREATE INDEX IF NOT EXISTS idx_user_profiles_organization ON user_profiles(organization);

-- ========== 创建用户卡片表 ==========

CREATE TABLE IF NOT EXISTS user_cards (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    card_number VARCHAR(50) UNIQUE NOT NULL,  -- 卡号
    card_type INTEGER NOT NULL DEFAULT 0,     -- 0:IC/ID卡, 1:CPU卡, 2:指纹, 3:人脸, 4:手机号, 5:身份证, 6:胁迫卡, 7:母卡, 8:自定义
    card_name VARCHAR(100),                   -- 卡片名称
    is_physical_card INTEGER NOT NULL DEFAULT 1, -- 是否物理卡
    is_primary_card INTEGER NOT NULL DEFAULT 0,  -- 是否主卡
    status INTEGER NOT NULL DEFAULT 0,        -- 0:正常, 1:挂失, 2:停用, 3:过期
    issue_date DATE,                          -- 发卡日期
    expiry_date DATE,                         -- 过期日期
    last_used TIMESTAMP,                      -- 最后使用时间
    use_count INTEGER DEFAULT 0,              -- 使用次数
    remarks TEXT,                             -- 备注
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_user_cards_user_id ON user_cards(user_id);
CREATE INDEX IF NOT EXISTS idx_user_cards_card_number ON user_cards(card_number);
CREATE INDEX IF NOT EXISTS idx_user_cards_card_type ON user_cards(card_type);
CREATE INDEX IF NOT EXISTS idx_user_cards_status ON user_cards(status);
CREATE INDEX IF NOT EXISTS idx_user_cards_is_primary ON user_cards(is_primary_card);

-- ========== 创建用户生物识别表 ==========

CREATE TABLE IF NOT EXISTS user_biometric (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    photo_path VARCHAR(500),          -- 照片文件路径
    photo_data BLOB,                  -- 照片数据
    photo_format VARCHAR(10),         -- 照片格式
    photo_size INTEGER,               -- 照片大小
    fingerprint_template BLOB,        -- 指纹模板数据
    fingerprint_quality INTEGER,      -- 指纹质量
    face_template BLOB,               -- 人脸模板数据
    face_quality INTEGER,             -- 人脸质量
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_user_biometric_user_id ON user_biometric(user_id);

-- ========== 添加约束和触发器 ==========

-- 确保每个用户只能有一个扩展信息记录
CREATE UNIQUE INDEX IF NOT EXISTS idx_user_profiles_unique_user ON user_profiles(user_id);

-- 确保每个用户只能有一个生物识别记录
CREATE UNIQUE INDEX IF NOT EXISTS idx_user_biometric_unique_user ON user_biometric(user_id);

-- 触发器：更新用户扩展信息的updated_at字段
CREATE TRIGGER IF NOT EXISTS trigger_user_profiles_update 
    AFTER UPDATE ON user_profiles
    FOR EACH ROW
BEGIN
    UPDATE user_profiles SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- 触发器：更新用户卡片的updated_at字段
CREATE TRIGGER IF NOT EXISTS trigger_user_cards_update 
    AFTER UPDATE ON user_cards
    FOR EACH ROW
BEGIN
    UPDATE user_cards SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- 触发器：更新用户生物识别的updated_at字段
CREATE TRIGGER IF NOT EXISTS trigger_user_biometric_update 
    AFTER UPDATE ON user_biometric
    FOR EACH ROW
BEGIN
    UPDATE user_biometric SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- 触发器：确保每个用户只有一个主卡
CREATE TRIGGER IF NOT EXISTS trigger_user_cards_primary_unique
    AFTER UPDATE ON user_cards
    FOR EACH ROW
    WHEN NEW.is_primary_card = 1
BEGIN
    UPDATE user_cards 
    SET is_primary_card = 0 
    WHERE user_id = NEW.user_id AND id != NEW.id AND is_primary_card = 1;
END;

-- 触发器：插入时确保每个用户只有一个主卡
CREATE TRIGGER IF NOT EXISTS trigger_user_cards_primary_insert
    AFTER INSERT ON user_cards
    FOR EACH ROW
    WHEN NEW.is_primary_card = 1
BEGIN
    UPDATE user_cards 
    SET is_primary_card = 0 
    WHERE user_id = NEW.user_id AND id != NEW.id AND is_primary_card = 1;
END; 