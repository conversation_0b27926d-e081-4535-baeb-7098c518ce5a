/****************************************************************************
** Meta object code from reading C++ file 'AreaManagementWidget.h'
**
** Created by: The Qt Meta Object Compiler version 68 (Qt 6.6.3)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../src/views/AreaManagementWidget.h"
#include <QtGui/qtextcursor.h>
#include <QtCore/qmetatype.h>

#if __has_include(<QtCore/qtmochelpers.h>)
#include <QtCore/qtmochelpers.h>
#else
QT_BEGIN_MOC_NAMESPACE
#endif


#include <memory>

#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'AreaManagementWidget.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 68
#error "This file was generated using the moc from 6.6.3. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {

#ifdef QT_MOC_HAS_STRINGDATA
struct qt_meta_stringdata_CLASSAccessControlSCOPEAreaManagementWidgetENDCLASS_t {};
constexpr auto qt_meta_stringdata_CLASSAccessControlSCOPEAreaManagementWidgetENDCLASS = QtMocHelpers::stringData(
    "AccessControl::AreaManagementWidget",
    "onTreeSelectionChanged",
    "",
    "onAddArea",
    "onAddSubArea",
    "onEditArea",
    "onDeleteArea",
    "onSaveArea",
    "onCancelEdit",
    "onSearchAreas",
    "onClearSearch",
    "onExpandAll",
    "onCollapseAll",
    "onImportAreas",
    "onExportAreas",
    "onShowContextMenu",
    "pos"
);
#else  // !QT_MOC_HAS_STRING_DATA
struct qt_meta_stringdata_CLASSAccessControlSCOPEAreaManagementWidgetENDCLASS_t {
    uint offsetsAndSizes[34];
    char stringdata0[36];
    char stringdata1[23];
    char stringdata2[1];
    char stringdata3[10];
    char stringdata4[13];
    char stringdata5[11];
    char stringdata6[13];
    char stringdata7[11];
    char stringdata8[13];
    char stringdata9[14];
    char stringdata10[14];
    char stringdata11[12];
    char stringdata12[14];
    char stringdata13[14];
    char stringdata14[14];
    char stringdata15[18];
    char stringdata16[4];
};
#define QT_MOC_LITERAL(ofs, len) \
    uint(sizeof(qt_meta_stringdata_CLASSAccessControlSCOPEAreaManagementWidgetENDCLASS_t::offsetsAndSizes) + ofs), len 
Q_CONSTINIT static const qt_meta_stringdata_CLASSAccessControlSCOPEAreaManagementWidgetENDCLASS_t qt_meta_stringdata_CLASSAccessControlSCOPEAreaManagementWidgetENDCLASS = {
    {
        QT_MOC_LITERAL(0, 35),  // "AccessControl::AreaManagement..."
        QT_MOC_LITERAL(36, 22),  // "onTreeSelectionChanged"
        QT_MOC_LITERAL(59, 0),  // ""
        QT_MOC_LITERAL(60, 9),  // "onAddArea"
        QT_MOC_LITERAL(70, 12),  // "onAddSubArea"
        QT_MOC_LITERAL(83, 10),  // "onEditArea"
        QT_MOC_LITERAL(94, 12),  // "onDeleteArea"
        QT_MOC_LITERAL(107, 10),  // "onSaveArea"
        QT_MOC_LITERAL(118, 12),  // "onCancelEdit"
        QT_MOC_LITERAL(131, 13),  // "onSearchAreas"
        QT_MOC_LITERAL(145, 13),  // "onClearSearch"
        QT_MOC_LITERAL(159, 11),  // "onExpandAll"
        QT_MOC_LITERAL(171, 13),  // "onCollapseAll"
        QT_MOC_LITERAL(185, 13),  // "onImportAreas"
        QT_MOC_LITERAL(199, 13),  // "onExportAreas"
        QT_MOC_LITERAL(213, 17),  // "onShowContextMenu"
        QT_MOC_LITERAL(231, 3)   // "pos"
    },
    "AccessControl::AreaManagementWidget",
    "onTreeSelectionChanged",
    "",
    "onAddArea",
    "onAddSubArea",
    "onEditArea",
    "onDeleteArea",
    "onSaveArea",
    "onCancelEdit",
    "onSearchAreas",
    "onClearSearch",
    "onExpandAll",
    "onCollapseAll",
    "onImportAreas",
    "onExportAreas",
    "onShowContextMenu",
    "pos"
};
#undef QT_MOC_LITERAL
#endif // !QT_MOC_HAS_STRING_DATA
} // unnamed namespace

Q_CONSTINIT static const uint qt_meta_data_CLASSAccessControlSCOPEAreaManagementWidgetENDCLASS[] = {

 // content:
      12,       // revision
       0,       // classname
       0,    0, // classinfo
      14,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // slots: name, argc, parameters, tag, flags, initial metatype offsets
       1,    0,   98,    2, 0x08,    1 /* Private */,
       3,    0,   99,    2, 0x08,    2 /* Private */,
       4,    0,  100,    2, 0x08,    3 /* Private */,
       5,    0,  101,    2, 0x08,    4 /* Private */,
       6,    0,  102,    2, 0x08,    5 /* Private */,
       7,    0,  103,    2, 0x08,    6 /* Private */,
       8,    0,  104,    2, 0x08,    7 /* Private */,
       9,    0,  105,    2, 0x08,    8 /* Private */,
      10,    0,  106,    2, 0x08,    9 /* Private */,
      11,    0,  107,    2, 0x08,   10 /* Private */,
      12,    0,  108,    2, 0x08,   11 /* Private */,
      13,    0,  109,    2, 0x08,   12 /* Private */,
      14,    0,  110,    2, 0x08,   13 /* Private */,
      15,    1,  111,    2, 0x08,   14 /* Private */,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QPoint,   16,

       0        // eod
};

Q_CONSTINIT const QMetaObject AccessControl::AreaManagementWidget::staticMetaObject = { {
    QMetaObject::SuperData::link<QWidget::staticMetaObject>(),
    qt_meta_stringdata_CLASSAccessControlSCOPEAreaManagementWidgetENDCLASS.offsetsAndSizes,
    qt_meta_data_CLASSAccessControlSCOPEAreaManagementWidgetENDCLASS,
    qt_static_metacall,
    nullptr,
    qt_incomplete_metaTypeArray<qt_meta_stringdata_CLASSAccessControlSCOPEAreaManagementWidgetENDCLASS_t,
        // Q_OBJECT / Q_GADGET
        QtPrivate::TypeAndForceComplete<AreaManagementWidget, std::true_type>,
        // method 'onTreeSelectionChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onAddArea'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onAddSubArea'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onEditArea'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onDeleteArea'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onSaveArea'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onCancelEdit'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onSearchAreas'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onClearSearch'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onExpandAll'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onCollapseAll'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onImportAreas'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onExportAreas'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onShowContextMenu'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QPoint &, std::false_type>
    >,
    nullptr
} };

void AccessControl::AreaManagementWidget::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<AreaManagementWidget *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->onTreeSelectionChanged(); break;
        case 1: _t->onAddArea(); break;
        case 2: _t->onAddSubArea(); break;
        case 3: _t->onEditArea(); break;
        case 4: _t->onDeleteArea(); break;
        case 5: _t->onSaveArea(); break;
        case 6: _t->onCancelEdit(); break;
        case 7: _t->onSearchAreas(); break;
        case 8: _t->onClearSearch(); break;
        case 9: _t->onExpandAll(); break;
        case 10: _t->onCollapseAll(); break;
        case 11: _t->onImportAreas(); break;
        case 12: _t->onExportAreas(); break;
        case 13: _t->onShowContextMenu((*reinterpret_cast< std::add_pointer_t<QPoint>>(_a[1]))); break;
        default: ;
        }
    }
}

const QMetaObject *AccessControl::AreaManagementWidget::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *AccessControl::AreaManagementWidget::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_CLASSAccessControlSCOPEAreaManagementWidgetENDCLASS.stringdata0))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int AccessControl::AreaManagementWidget::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 14)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 14;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 14)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 14;
    }
    return _id;
}
QT_WARNING_POP
