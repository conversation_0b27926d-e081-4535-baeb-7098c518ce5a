#include "ConsumerFingerprintWidget.h"

#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QMessageBox>

namespace AccessControl {

ConsumerFingerprintWidget::ConsumerFingerprintWidget(QWidget *parent)
    : QWidget(parent)
    , m_statusLabel(nullptr)
    , m_enrollButton(nullptr)
    , m_deleteButton(nullptr)
    , m_currentFinger(Finger::RightIndex)
{
    initUI();
    updateUIState();
}

ConsumerFingerprintWidget::~ConsumerFingerprintWidget()
{
    // 析构函数内容
}

void ConsumerFingerprintWidget::initUI()
{
    // 创建主布局
    QVBoxLayout *mainLayout = new QVBoxLayout(this);
    
    // 创建状态标签
    m_statusLabel = new QLabel(tr("指纹状态：未录入"), this);
    mainLayout->addWidget(m_statusLabel);
    
    // 创建按钮布局
    QHBoxLayout *buttonLayout = new QHBoxLayout();
    
    // 创建录入按钮
    m_enrollButton = new QPushButton(tr("录入指纹"), this);
    connect(m_enrollButton, &QPushButton::clicked, this, &ConsumerFingerprintWidget::enrollFingerprint);
    buttonLayout->addWidget(m_enrollButton);
    
    // 创建删除按钮
    m_deleteButton = new QPushButton(tr("删除指纹"), this);
    connect(m_deleteButton, &QPushButton::clicked, this, &ConsumerFingerprintWidget::deleteFingerprint);
    buttonLayout->addWidget(m_deleteButton);
    
    // 添加按钮布局到主布局
    mainLayout->addLayout(buttonLayout);
    
    // 设置主布局
    setLayout(mainLayout);
}

void ConsumerFingerprintWidget::setFingerprintData(const QByteArray& fingerprintData, Finger finger)
{
    m_currentFinger = finger;
    m_fingerprintData = fingerprintData;
    updateUIState();
    emit fingerprintChanged();
}

QByteArray ConsumerFingerprintWidget::fingerprintData(Finger finger) const
{
    if (finger == m_currentFinger) {
        return m_fingerprintData;
    }
    return QByteArray();
}

void ConsumerFingerprintWidget::clearFingerprint(Finger finger)
{
    if (finger == m_currentFinger) {
        m_fingerprintData.clear();
        updateUIState();
        emit fingerprintChanged();
    }
}

bool ConsumerFingerprintWidget::hasFingerprint(Finger finger) const
{
    if (finger == m_currentFinger) {
        return !m_fingerprintData.isEmpty();
    }
    return false;
}

void ConsumerFingerprintWidget::enrollFingerprint()
{
    // 模拟指纹录入过程
    QByteArray simulatedData;
    simulatedData.resize(256);
    // 填充模拟数据
    for (int i = 0; i < simulatedData.size(); ++i) {
        simulatedData[i] = static_cast<char>(i % 256);
    }
    
    setFingerprintData(simulatedData, m_currentFinger);
    
    QMessageBox::information(this, tr("录入成功"), tr("指纹录入成功！"));
}

void ConsumerFingerprintWidget::deleteFingerprint()
{
    if (hasFingerprint(m_currentFinger)) {
        if (QMessageBox::question(this, tr("确认删除"), tr("确定要删除指纹吗？"),
                                 QMessageBox::Yes | QMessageBox::No) == QMessageBox::Yes) {
            clearFingerprint(m_currentFinger);
            QMessageBox::information(this, tr("删除成功"), tr("指纹删除成功！"));
        }
    } else {
        QMessageBox::information(this, tr("提示"), tr("没有可删除的指纹！"));
    }
}

void ConsumerFingerprintWidget::updateUIState()
{
    bool hasData = !m_fingerprintData.isEmpty();
    
    if (hasData) {
        m_statusLabel->setText(tr("指纹状态：已录入"));
    } else {
        m_statusLabel->setText(tr("指纹状态：未录入"));
    }
    
    m_deleteButton->setEnabled(hasData);
}

} // namespace AccessControl 