-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(100) NOT NULL,
    salt VARCHAR(50) NOT NULL,
    role INTEGER NOT NULL DEFAULT 3, -- 0:超级管理员, 1:管理员, 2:操作员, 3:查看者
    status INTEGER NOT NULL DEFAULT 1, -- 0:正常, 1:停用, 2:锁定, 3:过期
    real_name VARCHAR(100),
    email VARCHAR(100),
    phone_number VARCHAR(20),
    id_number VARCHAR(18),
    access_enabled INTEGER NOT NULL DEFAULT 1,
    attendance_enabled INTEGER NOT NULL DEFAULT 1,
    valid_from DATE DEFAULT (date('now')),
    valid_until DATE DEFAULT '2099-12-31',
    department_id INTEGER DEFAULT -1,
    work_number VARCHAR(50),
    shift_work INTEGER DEFAULT 0,
    last_login_at TIMESTAMP,
    last_login_ip VARCHAR(50),
    login_attempts INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 插入默认管理员用户
-- 用户名: admin, 密码: admin
INSERT OR IGNORE INTO users (
    username, password_hash, salt, role, status, real_name, email,
    access_enabled, attendance_enabled, created_at, updated_at
) VALUES (
    'admin',
    '8c6976e5b5410415bde908bd4dee15dfb167a9c873fc4bb8a81f6f2ab448a918', -- admin的SHA-256哈希
    '00000000-0000-0000-0000-000000000000',
    0, -- 超级管理员
    0, -- 正常
    '系统管理员',
    '<EMAIL>',
    1, 1,
    CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
);

-- 创建部门表
CREATE TABLE IF NOT EXISTS departments (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(20) NOT NULL UNIQUE,
    parent_id INTEGER,
    description TEXT,
    status INTEGER NOT NULL DEFAULT 1,
    sort_order INTEGER NOT NULL DEFAULT 0,
    level INTEGER NOT NULL DEFAULT 0,
    full_path TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (parent_id) REFERENCES departments(id) ON DELETE SET NULL
);

-- 创建区域表
CREATE TABLE IF NOT EXISTS areas (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    area_code VARCHAR(20) UNIQUE NOT NULL,
    area_name VARCHAR(100) NOT NULL,
    parent_id INTEGER,
    level_depth INTEGER DEFAULT 1,
    sort_order INTEGER DEFAULT 0,
    description TEXT,
    enabled BOOLEAN DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES areas(id) ON DELETE SET NULL
);

-- 创建用户扩展信息表
CREATE TABLE IF NOT EXISTS user_profiles (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    gender INTEGER DEFAULT 0,
    ethnicity TEXT,
    religion TEXT,
    native_place TEXT,
    birth_date TEXT,
    marital_status INTEGER DEFAULT 0,
    political_status TEXT,
    education TEXT,
    work_phone TEXT,
    home_phone TEXT,
    english_name TEXT,
    organization TEXT,
    job_title TEXT,
    skill_level TEXT,
    certificate_name TEXT,
    certificate_number TEXT,
    social_security_number TEXT,
    hire_date TEXT,
    termination_date TEXT,
    email_address TEXT,
    mailing_address TEXT,
    postal_code TEXT,
    remarks TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 创建用户卡片表
CREATE TABLE IF NOT EXISTS user_cards (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    card_number VARCHAR(50) NOT NULL UNIQUE,
    card_type INTEGER NOT NULL DEFAULT 0, -- 0:普通卡, 1:IC卡, 2:CPU卡
    status INTEGER NOT NULL DEFAULT 0, -- 0:正常, 1:挂失, 2:注销
    valid_from DATE DEFAULT CURRENT_DATE,
    valid_until DATE DEFAULT '2099-12-31',
    is_primary BOOLEAN DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 创建用户生物识别表
CREATE TABLE IF NOT EXISTS user_biometric (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    biometric_type INTEGER NOT NULL DEFAULT 0, -- 0:指纹, 1:人脸, 2:虹膜
    biometric_data BLOB NOT NULL,
    template_version VARCHAR(20) NOT NULL,
    quality_score FLOAT,
    enrollment_device VARCHAR(50),
    status INTEGER NOT NULL DEFAULT 0, -- 0:正常, 1:挂失, 2:注销
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
); 