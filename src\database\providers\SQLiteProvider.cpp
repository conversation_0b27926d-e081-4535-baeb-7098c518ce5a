#include "SQLiteProvider.h"
#include <QSqlError>
#include <QSqlRecord>
#include <QFileInfo>
#include <QDir>
#include <QUuid>
#include <QDebug>
#include <QFile>
#include <QDateTime>

namespace AccessControl {

SQLiteProvider::SQLiteProvider()
    : m_connectionName(generateConnectionName())
    , m_errorCode(0)
{
    clearError();
}

SQLiteProvider::~SQLiteProvider() {
    disconnect();
}

bool SQLiteProvider::connect(const DatabaseConfig& config) {
    qDebug() << "开始连接SQLite数据库...";
    
    // 使用局部锁保护整个连接过程
    QMutexLocker locker(&m_mutex);
    
    // 保存配置
    m_config = config;
    qDebug() << "配置保存完成，数据库文件:" << config.filePath;
    
    try {
        // 确保数据库目录存在
        if (!ensureDatabaseDirectoryUnsafe(config.filePath)) {
            setErrorUnsafe(QString("无法创建数据库目录: %1").arg(QFileInfo(config.filePath).path()));
            return false;
        }
        qDebug() << "数据库目录检查完成";
        
        // 创建数据库连接
        qDebug() << "创建数据库连接，连接名:" << m_connectionName;
        m_database = QSqlDatabase::addDatabase("QSQLITE", m_connectionName);
        m_database.setDatabaseName(config.filePath);
        qDebug() << "数据库连接创建完成";
        
        // 打开数据库
        qDebug() << "打开数据库...";
        if (!m_database.open()) {
            setErrorUnsafe(QString("无法打开SQLite数据库: %1").arg(m_database.lastError().text()),
                    m_database.lastError().nativeErrorCode().toInt());
            return false;
        }
        qDebug() << "数据库打开成功";
        
        // 初始化SQLite设置
        qDebug() << "开始初始化设置...";
        if (!initializeSettingsUnsafe()) {
            disconnectUnsafe();
            return false;
        }
        qDebug() << "设置初始化完成";
        
        clearErrorUnsafe();
        qDebug() << "SQLite数据库连接成功:" << config.filePath;
        return true;
        
    } catch (const std::exception& e) {
        qDebug() << "连接异常:" << e.what();
        setErrorUnsafe(QString("连接SQLite数据库异常: %1").arg(e.what()));
        return false;
    }
}

void SQLiteProvider::disconnect() {
    QMutexLocker locker(&m_mutex);
    disconnectUnsafe();
}

bool SQLiteProvider::isConnected() const {
    QMutexLocker locker(&m_mutex);
    return m_database.isOpen() && m_database.isValid();
}

bool SQLiteProvider::reconnect() {
    // 使用无锁版本避免重复锁定
    QMutexLocker locker(&m_mutex);
    disconnectUnsafe();
    locker.unlock(); // 释放锁，让connect重新获取
    return connect(m_config);
}

QSqlDatabase SQLiteProvider::getDatabase() {
    QMutexLocker locker(&m_mutex);
    return m_database;
}

QSqlQuery SQLiteProvider::query(const QString& sql) {
    QMutexLocker locker(&m_mutex);
    
    if (!isConnectedUnsafe()) {
        setErrorUnsafe("数据库未连接");
        return QSqlQuery();
    }
    
    QSqlQuery q(m_database);
    if (!q.exec(sql)) {
        setErrorUnsafe(QString("查询执行失败: %1").arg(q.lastError().text()),
                q.lastError().nativeErrorCode().toInt());
    } else {
        clearErrorUnsafe();
    }
    
    return q;
}

bool SQLiteProvider::execute(const QString& sql) {
    qDebug() << "执行SQL:" << sql;
    QMutexLocker locker(&m_mutex);
    
    if (!isConnectedUnsafe()) {
        setErrorUnsafe("数据库未连接");
        qDebug() << "数据库未连接";
        return false;
    }
    
    QSqlQuery q(m_database);
    if (!q.exec(sql)) {
        setErrorUnsafe(QString("SQL执行失败: %1").arg(q.lastError().text()),
                q.lastError().nativeErrorCode().toInt());
        qDebug() << "SQL执行失败:" << q.lastError().text();
        return false;
    }
    
    qDebug() << "SQL执行成功";
    clearErrorUnsafe();
    return true;
}

QSqlQuery SQLiteProvider::prepareQuery(const QString& sql) {
    QMutexLocker locker(&m_mutex);
    
    QSqlQuery q(m_database);
    if (!q.prepare(sql)) {
        setErrorUnsafe(QString("SQL预编译失败: %1").arg(q.lastError().text()),
                q.lastError().nativeErrorCode().toInt());
    } else {
        clearErrorUnsafe();
    }
    
    return q;
}

QVariant SQLiteProvider::lastInsertId() {
    QMutexLocker locker(&m_mutex);
    
    if (!isConnectedUnsafe()) {
        return QVariant();
    }
    
    QSqlQuery q("SELECT last_insert_rowid()", m_database);
    if (q.next()) {
        return q.value(0);
    }
    
    return QVariant();
}

int SQLiteProvider::numRowsAffected() {
    QMutexLocker locker(&m_mutex);
    
    QSqlQuery q("SELECT changes()", m_database);
    if (q.next()) {
        return q.value(0).toInt();
    }
    
    return 0;
}

bool SQLiteProvider::transaction() {
    QMutexLocker locker(&m_mutex);
    
    if (!isConnectedUnsafe()) {
        setErrorUnsafe("数据库未连接");
        return false;
    }
    
    if (!m_database.transaction()) {
        setErrorUnsafe(QString("开始事务失败: %1").arg(m_database.lastError().text()));
        return false;
    }
    
    clearErrorUnsafe();
    return true;
}

bool SQLiteProvider::commit() {
    QMutexLocker locker(&m_mutex);
    
    if (!isConnectedUnsafe()) {
        setErrorUnsafe("数据库未连接");
        return false;
    }
    
    if (!m_database.commit()) {
        setErrorUnsafe(QString("提交事务失败: %1").arg(m_database.lastError().text()));
        return false;
    }
    
    clearErrorUnsafe();
    return true;
}

bool SQLiteProvider::rollback() {
    QMutexLocker locker(&m_mutex);
    
    if (!isConnectedUnsafe()) {
        setErrorUnsafe("数据库未连接");
        return false;
    }
    
    if (!m_database.rollback()) {
        setErrorUnsafe(QString("回滚事务失败: %1").arg(m_database.lastError().text()));
        return false;
    }
    
    clearErrorUnsafe();
    return true;
}

QString SQLiteProvider::lastError() const {
    QMutexLocker locker(&m_mutex);
    return m_lastError;
}

int SQLiteProvider::errorCode() const {
    QMutexLocker locker(&m_mutex);
    return m_errorCode;
}

bool SQLiteProvider::hasError() const {
    QMutexLocker locker(&m_mutex);
    return !m_lastError.isEmpty();
}

QString SQLiteProvider::databaseType() const {
    return "SQLite";
}

QString SQLiteProvider::databaseVersion() const {
    QMutexLocker locker(&m_mutex);
    
    if (!isConnectedUnsafe()) {
        return QString();
    }
    
    QSqlQuery q("SELECT sqlite_version()", m_database);
    if (q.next()) {
        return q.value(0).toString();
    }
    
    return QString();
}

QStringList SQLiteProvider::tableNames() const {
    QMutexLocker locker(&m_mutex);
    
    if (!isConnectedUnsafe()) {
        return QStringList();
    }
    
    QStringList tables;
    QSqlQuery q("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'", m_database);
    
    while (q.next()) {
        tables << q.value(0).toString();
    }
    
    return tables;
}

bool SQLiteProvider::tableExists(const QString& tableName) const {
    QMutexLocker locker(&m_mutex);
    
    if (!isConnectedUnsafe()) {
        return false;
    }
    
    QSqlQuery q(m_database);
    q.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name=?");
    q.addBindValue(tableName);
    
    if (q.exec() && q.next()) {
        return true;
    }
    
    return false;
}

QStringList SQLiteProvider::columnNames(const QString& tableName) const {
    QMutexLocker locker(&m_mutex);
    
    if (!isConnectedUnsafe()) {
        return QStringList();
    }
    
    QStringList columns;
    QSqlQuery q(QString("PRAGMA table_info(%1)").arg(tableName), m_database);
    
    while (q.next()) {
        columns << q.value("name").toString();
    }
    
    return columns;
}

bool SQLiteProvider::executeBatch(const QStringList& sqlList) {
    // 使用锁保护整个批量操作
    QMutexLocker locker(&m_mutex);
    
    if (!transactionUnsafe()) {
        return false;
    }
    
    try {
        for (const QString& sql : sqlList) {
            if (!executeUnsafe(sql)) {
                rollbackUnsafe();
                return false;
            }
        }
        
        return commitUnsafe();
        
    } catch (...) {
        rollbackUnsafe();
        return false;
    }
}

bool SQLiteProvider::batchInsert(const QString& tableName, 
                                const QStringList& columns, 
                                const QList<QVariantList>& values) {
    if (columns.isEmpty() || values.isEmpty()) {
        QMutexLocker locker(&m_mutex);
        setErrorUnsafe("批量插入参数无效");
        return false;
    }
    
    // 构建INSERT语句
    QString sql = QString("INSERT INTO %1 (%2) VALUES (%3)")
                  .arg(tableName)
                  .arg(columns.join(", "))
                  .arg(QString("?").repeated(columns.size()).replace("??", "?, ?"));
    
    QMutexLocker locker(&m_mutex);
    
    if (!transactionUnsafe()) {
        return false;
    }
    
    try {
        QSqlQuery q(m_database);
        if (!q.prepare(sql)) {
            setErrorUnsafe(QString("SQL预编译失败: %1").arg(q.lastError().text()));
            rollbackUnsafe();
            return false;
        }
        
        for (const QVariantList& row : values) {
            // 绑定参数
            for (int i = 0; i < row.size() && i < columns.size(); ++i) {
                q.addBindValue(row[i]);
            }
            
            if (!q.exec()) {
                setErrorUnsafe(QString("批量插入执行失败: %1").arg(q.lastError().text()));
                rollbackUnsafe();
                return false;
            }
        }
        
        return commitUnsafe();
        
    } catch (...) {
        rollbackUnsafe();
        return false;
    }
}

bool SQLiteProvider::optimize() {
    QMutexLocker locker(&m_mutex);
    return executeUnsafe("PRAGMA optimize");
}

bool SQLiteProvider::checkIntegrity() {
    QMutexLocker locker(&m_mutex);
    
    QSqlQuery q("PRAGMA integrity_check", m_database);
    if (q.next()) {
        QString result = q.value(0).toString();
        return result == "ok";
    }
    return false;
}

qint64 SQLiteProvider::databaseSize() const {
    QMutexLocker locker(&m_mutex);
    
    if (!isConnectedUnsafe()) {
        return 0;
    }
    
    QFileInfo fileInfo(m_config.filePath);
    return fileInfo.size();
}

bool SQLiteProvider::backup(const QString& backupPath) {
    QMutexLocker locker(&m_mutex);
    
    if (!isConnectedUnsafe()) {
        setErrorUnsafe("数据库未连接");
        return false;
    }
    
    // 确保备份目录存在
    QFileInfo backupInfo(backupPath);
    if (!backupInfo.dir().exists()) {
        if (!backupInfo.dir().mkpath(".")) {
            setErrorUnsafe(QString("无法创建备份目录: %1").arg(backupInfo.path()));
            return false;
        }
    }
    
    // 关闭数据库连接以进行文件复制
    QString dbPath = m_config.filePath;
    disconnectUnsafe();
    
    // 复制数据库文件
    bool success = QFile::copy(dbPath, backupPath);
    
    // 重新连接数据库 - 临时释放锁
    locker.unlock();
    connect(m_config);
    locker.relock();
    
    if (!success) {
        setErrorUnsafe(QString("备份文件复制失败: %1 -> %2").arg(dbPath, backupPath));
        return false;
    }
    
    clearErrorUnsafe();
    return true;
}

bool SQLiteProvider::restore(const QString& backupPath) {
    QMutexLocker locker(&m_mutex);
    
    if (!QFile::exists(backupPath)) {
        setErrorUnsafe(QString("备份文件不存在: %1").arg(backupPath));
        return false;
    }
    
    // 关闭当前连接
    QString dbPath = m_config.filePath;
    disconnectUnsafe();
    
    // 删除现有数据库文件
    if (QFile::exists(dbPath)) {
        if (!QFile::remove(dbPath)) {
            setErrorUnsafe(QString("无法删除现有数据库文件: %1").arg(dbPath));
            return false;
        }
    }
    
    // 复制备份文件
    bool success = QFile::copy(backupPath, dbPath);
    
    // 重新连接数据库 - 临时释放锁
    locker.unlock();
    connect(m_config);
    locker.relock();
    
    if (!success) {
        setErrorUnsafe(QString("恢复文件复制失败: %1 -> %2").arg(backupPath, dbPath));
        return false;
    }
    
    clearErrorUnsafe();
    return true;
}

bool SQLiteProvider::enableForeignKeys(bool enable) {
    QMutexLocker locker(&m_mutex);
    return executeUnsafe(QString("PRAGMA foreign_keys = %1").arg(enable ? "ON" : "OFF"));
}

bool SQLiteProvider::enableWALMode(bool enable) {
    QMutexLocker locker(&m_mutex);
    QString mode = enable ? "WAL" : "DELETE";
    return executeUnsafe(QString("PRAGMA journal_mode = %1").arg(mode));
}

bool SQLiteProvider::vacuum() {
    QMutexLocker locker(&m_mutex);
    return executeUnsafe("VACUUM");
}

bool SQLiteProvider::setPageSize(int pageSize) {
    QMutexLocker locker(&m_mutex);
    return executeUnsafe(QString("PRAGMA page_size = %1").arg(pageSize));
}

bool SQLiteProvider::setCacheSize(int cacheSize) {
    QMutexLocker locker(&m_mutex);
    return executeUnsafe(QString("PRAGMA cache_size = %1").arg(cacheSize));
}

QString SQLiteProvider::generateConnectionName() {
    return QString("SQLiteConn_%1_%2")
           .arg(QDateTime::currentMSecsSinceEpoch())
           .arg(QUuid::createUuid().toString(QUuid::WithoutBraces));
}

bool SQLiteProvider::ensureDatabaseDirectoryUnsafe(const QString& filePath) {
    if (filePath == ":memory:") {
        return true; // 内存数据库无需目录
    }
    
    QFileInfo fileInfo(filePath);
    QDir dir = fileInfo.dir();
    
    if (!dir.exists()) {
        return dir.mkpath(".");
    }
    
    return true;
}

bool SQLiteProvider::initializeSettingsUnsafe() {
    try {
        // 启用外键约束
        if (!executeUnsafe("PRAGMA foreign_keys = ON")) {
            return false;
        }
        
        // 设置同步模式为NORMAL（平衡性能和安全性）
        if (!executeUnsafe("PRAGMA synchronous = NORMAL")) {
            return false;
        }
        
        // 设置临时存储为内存
        if (!executeUnsafe("PRAGMA temp_store = MEMORY")) {
            return false;
        }
        
        // 设置页大小为4096（适合大多数现代文件系统）
        if (!executeUnsafe("PRAGMA page_size = 4096")) {
            return false;
        }
        
        // 设置缓存大小（8MB）
        if (!executeUnsafe("PRAGMA cache_size = 2000")) {
            return false;
        }
        
        return true;
        
    } catch (const std::exception& e) {
        setErrorUnsafe(QString("初始化设置异常: %1").arg(e.what()));
        return false;
    }
}

// ========== 线程安全版本的私有方法 ==========

bool SQLiteProvider::initializeSettings() {
    QMutexLocker locker(&m_mutex);
    return initializeSettingsUnsafe();
}

bool SQLiteProvider::ensureDatabaseDirectory(const QString& filePath) {
    QMutexLocker locker(&m_mutex);
    return ensureDatabaseDirectoryUnsafe(filePath);
}

void SQLiteProvider::setError(const QString& error, int code) {
    QMutexLocker locker(&m_mutex);
    setErrorUnsafe(error, code);
}

void SQLiteProvider::clearError() {
    QMutexLocker locker(&m_mutex);
    clearErrorUnsafe();
}

// ========== 无锁版本的错误处理方法 ==========

void SQLiteProvider::setErrorUnsafe(const QString& error, int code) {
    m_lastError = error;
    m_errorCode = code;
    qDebug() << "SQLiteProvider错误:" << error;
}

void SQLiteProvider::clearErrorUnsafe() {
    m_lastError.clear();
    m_errorCode = 0;
}

bool SQLiteProvider::isConnectedUnsafe() const {
    return m_database.isOpen() && m_database.isValid();
}

void SQLiteProvider::disconnectUnsafe() {
    if (m_database.isOpen()) {
        m_database.close();
    }
    
    if (QSqlDatabase::contains(m_connectionName)) {
        QSqlDatabase::removeDatabase(m_connectionName);
    }
}

bool SQLiteProvider::executeUnsafe(const QString& sql) {
    if (!isConnectedUnsafe()) {
        setErrorUnsafe("数据库未连接");
        return false;
    }
    
    QSqlQuery q(m_database);
    if (!q.exec(sql)) {
        setErrorUnsafe(QString("SQL执行失败: %1").arg(q.lastError().text()),
                q.lastError().nativeErrorCode().toInt());
        return false;
    }
    
    clearErrorUnsafe();
    return true;
}

bool SQLiteProvider::transactionUnsafe() {
    if (!isConnectedUnsafe()) {
        setErrorUnsafe("数据库未连接");
        return false;
    }
    
    if (!m_database.transaction()) {
        setErrorUnsafe(QString("开始事务失败: %1").arg(m_database.lastError().text()));
        return false;
    }
    
    clearErrorUnsafe();
    return true;
}

bool SQLiteProvider::commitUnsafe() {
    if (!isConnectedUnsafe()) {
        setErrorUnsafe("数据库未连接");
        return false;
    }
    
    if (!m_database.commit()) {
        setErrorUnsafe(QString("提交事务失败: %1").arg(m_database.lastError().text()));
        return false;
    }
    
    clearErrorUnsafe();
    return true;
}

bool SQLiteProvider::rollbackUnsafe() {
    if (!isConnectedUnsafe()) {
        setErrorUnsafe("数据库未连接");
        return false;
    }
    
    if (!m_database.rollback()) {
        setErrorUnsafe(QString("回滚事务失败: %1").arg(m_database.lastError().text()));
        return false;
    }
    
    clearErrorUnsafe();
    return true;
}

} // namespace AccessControl 