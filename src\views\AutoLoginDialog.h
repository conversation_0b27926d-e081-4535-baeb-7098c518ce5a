#ifndef AUTOLOGINDIALOG_H
#define AUTOLOGINDIALOG_H

#include <QDialog>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QFormLayout>
#include <QGroupBox>
#include <QCheckBox>
#include <QLineEdit>
#include <QPushButton>
#include <QLabel>
#include <QMessageBox>
#include <QSettings>

namespace AccessControl {

/**
 * @brief 自动登录设置对话框
 * 允许用户启用/禁用自动登录功能，并设置相关参数
 */
class AutoLoginDialog : public QDialog {
    Q_OBJECT

public:
    explicit AutoLoginDialog(QWidget *parent = nullptr);
    ~AutoLoginDialog();

private slots:
    /**
     * @brief 自动登录复选框状态改变
     */
    void onAutoLoginToggled(bool enabled);
    
    /**
     * @brief 记住密码复选框状态改变
     */
    void onRememberPasswordToggled(bool enabled);
    
    /**
     * @brief 应用设置
     */
    void applySettings();
    
    /**
     * @brief 重置为默认设置
     */
    void resetToDefault();

private:
    // UI组件
    QVBoxLayout* m_mainLayout;
    QGroupBox* m_autoLoginGroup;
    QFormLayout* m_formLayout;
    
    // 设置控件
    QCheckBox* m_enableAutoLoginCheck;
    QCheckBox* m_rememberPasswordCheck;
    QLineEdit* m_usernameEdit;
    QLineEdit* m_passwordEdit;
    QLabel* m_statusLabel;
    
    // 按钮
    QHBoxLayout* m_buttonLayout;
    QPushButton* m_applyBtn;
    QPushButton* m_resetBtn;
    QPushButton* m_closeBtn;
    
    // 设置管理
    QSettings* m_settings;
    
    /**
     * @brief 初始化界面
     */
    void initializeUI();
    
    /**
     * @brief 初始化样式
     */
    void initializeStyles();
    
    /**
     * @brief 初始化信号连接
     */
    void initializeConnections();
    
    /**
     * @brief 加载当前设置
     */
    void loadCurrentSettings();
    
    /**
     * @brief 保存设置
     */
    void saveSettings();
    
    /**
     * @brief 验证输入
     */
    bool validateInput();
    
    /**
     * @brief 加密密码
     */
    QString encryptPassword(const QString& password);
    
    /**
     * @brief 解密密码
     */
    QString decryptPassword(const QString& encryptedPassword);
    
    /**
     * @brief 更新状态显示
     */
    void updateStatus(const QString& message, bool isError = false);
};

} // namespace AccessControl

#endif // AUTOLOGINDIALOG_H 