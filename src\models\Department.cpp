#include "Department.h"
#include <QDebug>

namespace AccessControl {

// ========== Department 类实现 ==========

Department::Department()
    : m_id(0)
    , m_parentId(0)
    , m_status(Active)
    , m_sortOrder(0)
    , m_level(0)
{
    m_createdAt = QDateTime::currentDateTime();
    m_updatedAt = m_createdAt;
}

Department::Department(const QString& name, const QString& code, int parentId)
    : m_id(0)
    , m_name(name)
    , m_code(code)
    , m_parentId(parentId)
    , m_status(Active)
    , m_sortOrder(0)
    , m_level(0)
{
    m_createdAt = QDateTime::currentDateTime();
    m_updatedAt = m_createdAt;
}

Department::Department(const Department& other)
    : m_id(other.m_id)
    , m_name(other.m_name)
    , m_code(other.m_code)
    , m_parentId(other.m_parentId)
    , m_description(other.m_description)
    , m_status(other.m_status)
    , m_sortOrder(other.m_sortOrder)
    , m_createdAt(other.m_createdAt)
    , m_updatedAt(other.m_updatedAt)
    , m_level(other.m_level)
    , m_fullPath(other.m_fullPath)
{
}

Department& Department::operator=(const Department& other)
{
    if (this != &other) {
        m_id = other.m_id;
        m_name = other.m_name;
        m_code = other.m_code;
        m_parentId = other.m_parentId;
        m_description = other.m_description;
        m_status = other.m_status;
        m_sortOrder = other.m_sortOrder;
        m_createdAt = other.m_createdAt;
        m_updatedAt = other.m_updatedAt;
        m_level = other.m_level;
        m_fullPath = other.m_fullPath;
    }
    return *this;
}

Department::~Department()
{
}

QString Department::statusText() const
{
    switch (m_status) {
        case Active:
            return "启用";
        case Inactive:
            return "禁用";
        default:
            return "未知";
    }
}

bool Department::isValid() const
{
    // 基本验证：名称和代码不能为空
    if (m_name.trimmed().isEmpty()) {
        return false;
    }
    
    if (m_code.trimmed().isEmpty()) {
        return false;
    }
    
    // 代码长度限制（1-20字符）
    if (m_code.length() > 20) {
        return false;
    }
    
    // 名称长度限制（1-100字符）
    if (m_name.length() > 100) {
        return false;
    }
    
    // 父部门ID不能是自己（当ID不为0时）
    if (m_id != 0 && m_parentId == m_id) {
        return false;
    }
    
    return true;
}

QString Department::toString() const
{
    return QString("Department(id=%1, name='%2', code='%3', parentId=%4, status=%5)")
           .arg(m_id)
           .arg(m_name)
           .arg(m_code)
           .arg(m_parentId)
           .arg(m_status);
}

// ========== DepartmentTreeNode 类实现 ==========

DepartmentTreeNode::DepartmentTreeNode(const Department& dept)
    : department(dept)
    , parent(nullptr)
{
}

DepartmentTreeNode::~DepartmentTreeNode()
{
    // 智能指针会自动处理子节点的释放
}

void DepartmentTreeNode::addChild(std::shared_ptr<DepartmentTreeNode> child)
{
    if (child) {
        child->parent = this;
        children.append(child);
    }
}

bool DepartmentTreeNode::removeChild(int departmentId)
{
    for (int i = 0; i < children.size(); ++i) {
        if (children[i]->department.id() == departmentId) {
            children[i]->parent = nullptr;
            children.removeAt(i);
            return true;
        }
    }
    return false;
}

std::shared_ptr<DepartmentTreeNode> DepartmentTreeNode::findChild(int departmentId)
{
    for (auto& child : children) {
        if (child->department.id() == departmentId) {
            return child;
        }
    }
    return nullptr;
}

QList<std::shared_ptr<DepartmentTreeNode>> DepartmentTreeNode::getAllDescendants()
{
    QList<std::shared_ptr<DepartmentTreeNode>> descendants;
    
    for (auto& child : children) {
        descendants.append(child);
        
        // 递归获取子节点的后代
        auto childDescendants = child->getAllDescendants();
        descendants.append(childDescendants);
    }
    
    return descendants;
}

QList<int> DepartmentTreeNode::getPath()
{
    QList<int> path;
    
    // 从当前节点向上遍历到根节点
    DepartmentTreeNode* current = this;
    while (current) {
        path.prepend(current->department.id());
        current = current->parent;
    }
    
    return path;
}

} // namespace AccessControl 