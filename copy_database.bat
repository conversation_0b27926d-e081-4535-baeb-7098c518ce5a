@echo off
echo 复制数据库文件...

REM 设置路径
set TARGET_DIR=%APPDATA%\AccessControl Inc.\AccessControlSystem
set SOURCE_DB=test_access_control.db
set TARGET_DB=%TARGET_DIR%\access_control.db

REM 检查源数据库是否存在
if not exist "%SOURCE_DB%" (
    echo 源数据库文件不存在: %SOURCE_DB%
    pause
    exit /b 1
)

REM 确保目标目录存在
if not exist "%TARGET_DIR%" (
    echo 创建目标目录: %TARGET_DIR%
    mkdir "%TARGET_DIR%"
)

REM 删除现有数据库
if exist "%TARGET_DB%" (
    echo 删除现有数据库文件...
    del /f /q "%TARGET_DB%"
)

REM 复制数据库文件
echo 复制数据库文件到 %TARGET_DB%...
copy /y "%SOURCE_DB%" "%TARGET_DB%"

if %ERRORLEVEL% EQU 0 (
    echo 数据库文件复制成功！
    echo.
    echo 按任意键启动门禁系统...
    pause >nul
    start "" ".\build\simple\AccessControlSystem.exe"
) else (
    echo 数据库文件复制失败，错误代码: %ERRORLEVEL%
    pause
) 