#include "CardLineEdit.h"
#include <QKeyEvent>
#include <QRegularExpression>
#include <QDebug>
#include <QTimer>

CardLineEdit::CardLineEdit(QWidget *parent)
    : QLineEdit(parent)
    , m_clearTimer(new QTimer(this))
{
    // 设置定时器，用于检测连续输入
    m_clearTimer->setSingleShot(true);
    m_clearTimer->setInterval(50); // 50ms内的输入认为是连续的（读卡器输入）

    connect(m_clearTimer, &QTimer::timeout, this, &CardLineEdit::onInputFinished);

    // 记录初始状态
    m_previousText = text();
}

void CardLineEdit::keyPressEvent(QKeyEvent *event)
{
    // 如果是第一个字符输入，且之前有内容，可能是新的刷卡
    if (!m_clearTimer->isActive() && !text().isEmpty() && event->text().length() > 0) {
        // 检测到新的输入开始，清空现有内容
        qDebug() << "CardLineEdit: New input detected, clearing existing content:" << text();
        clear();
        m_previousText.clear();
    }

    QLineEdit::keyPressEvent(event);

    // 重启定时器
    m_clearTimer->start();
}

void CardLineEdit::onInputFinished()
{
    // 输入完成，处理可能的换行符和多行内容
    QString currentText = text();

    if (currentText != m_previousText) {
        // 处理换行符，只保留最后一行
        QStringList lines = currentText.split(QRegularExpression("[\r\n]"), Qt::SkipEmptyParts);
        if (!lines.isEmpty()) {
            QString cleanText = lines.last().trimmed();
            if (!cleanText.isEmpty() && cleanText != currentText) {
                qDebug() << "CardLineEdit: Cleaning text, keeping:" << cleanText;
                blockSignals(true);
                setText(cleanText);
                blockSignals(false);
            }
        }

        m_previousText = text();
    }
}