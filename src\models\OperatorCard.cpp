#include "OperatorCard.h"
#include <QDebug>

namespace AccessControl {

OperatorCard::OperatorCard()
    : m_id(0)
    , m_userId(0)
    , m_cardType(CardType::IC_ID)
    , m_cardNumber("")
    , m_isPrimary(false)
    , m_status(CardStatus::Active)
    , m_bindDate(QDateTime::currentDateTime())
    , m_lastUsed()
    , m_createdAt(QDateTime::currentDateTime())
    , m_remarks("")
{
}

OperatorCard::OperatorCard(int userId, CardType type, const QString& cardNumber)
    : m_id(0)
    , m_userId(userId)
    , m_cardType(type)
    , m_cardNumber(cardNumber)
    , m_isPrimary(false)
    , m_status(CardStatus::Active)
    , m_bindDate(QDateTime::currentDateTime())
    , m_lastUsed()
    , m_createdAt(QDateTime::currentDateTime())
    , m_remarks("")
{
}

// ========== 验证方法 ==========

bool OperatorCard::isValid() const {
    return m_userId > 0 && !m_cardNumber.isEmpty();
}

bool OperatorCard::isActive() const {
    return m_status == CardStatus::Active;
}

bool OperatorCard::isBiometric() const {
    return m_cardType == CardType::Fingerprint || m_cardType == CardType::Face;
}

bool OperatorCard::isPhysicalCard() const {
    return m_cardType == CardType::IC_ID || m_cardType == CardType::CPU || 
           m_cardType == CardType::Phone || m_cardType == CardType::IDCard ||
           m_cardType == CardType::Panic || m_cardType == CardType::Master;
}

bool OperatorCard::canBePrimary() const {
    // 生物识别卡不能作为主卡，需要绑定到物理卡
    return !isBiometric();
}

// ========== 序列化 ==========

QVariantMap OperatorCard::toVariantMap() const {
    QVariantMap map;
    
    map["id"] = m_id;
    map["userId"] = m_userId;
    map["cardType"] = static_cast<int>(m_cardType);
    map["cardNumber"] = m_cardNumber;
    map["isPrimary"] = m_isPrimary;
    map["status"] = static_cast<int>(m_status);
    map["bindDate"] = m_bindDate;
    map["lastUsed"] = m_lastUsed;
    map["createdAt"] = m_createdAt;
    map["remarks"] = m_remarks;
    
    return map;
}

void OperatorCard::fromVariantMap(const QVariantMap& map) {
    m_id = map.value("id", 0).toInt();
    m_userId = map.value("userId", 0).toInt();
    m_cardType = static_cast<CardType>(map.value("cardType", static_cast<int>(CardType::IC_ID)).toInt());
    m_cardNumber = map.value("cardNumber").toString();
    m_isPrimary = map.value("isPrimary", false).toBool();
    m_status = static_cast<CardStatus>(map.value("status", static_cast<int>(CardStatus::Active)).toInt());
    m_bindDate = map.value("bindDate").toDateTime();
    m_lastUsed = map.value("lastUsed").toDateTime();
    m_createdAt = map.value("createdAt").toDateTime();
    m_remarks = map.value("remarks").toString();
}

// ========== 静态方法 ==========

QString OperatorCard::cardTypeToString(CardType type) {
    switch (type) {
        case CardType::IC_ID: return "IC/ID卡";
        case CardType::CPU: return "CPU卡";
        case CardType::Fingerprint: return "指纹";
        case CardType::Face: return "人脸";
        case CardType::Phone: return "手机号";
        case CardType::IDCard: return "身份证";
        case CardType::Panic: return "胁迫卡";
        case CardType::Master: return "母卡";
        default: return "未知";
    }
}

OperatorCard::CardType OperatorCard::stringToCardType(const QString& typeStr) {
    if (typeStr == "IC/ID卡" || typeStr == "IC_ID") return CardType::IC_ID;
    if (typeStr == "CPU卡" || typeStr == "CPU") return CardType::CPU;
    if (typeStr == "指纹" || typeStr == "Fingerprint") return CardType::Fingerprint;
    if (typeStr == "人脸" || typeStr == "Face") return CardType::Face;
    if (typeStr == "手机号" || typeStr == "Phone") return CardType::Phone;
    if (typeStr == "身份证" || typeStr == "IDCard") return CardType::IDCard;
    if (typeStr == "胁迫卡" || typeStr == "Panic") return CardType::Panic;
    if (typeStr == "母卡" || typeStr == "Master") return CardType::Master;
    return CardType::IC_ID; // 默认
}

QString OperatorCard::cardStatusToString(CardStatus status) {
    switch (status) {
        case CardStatus::Active: return "启用";
        case CardStatus::Disabled: return "禁用";
        case CardStatus::Lost: return "挂失";
        case CardStatus::Expired: return "过期";
        default: return "未知";
    }
}

OperatorCard::CardStatus OperatorCard::stringToCardStatus(const QString& statusStr) {
    if (statusStr == "启用" || statusStr == "Active") return CardStatus::Active;
    if (statusStr == "禁用" || statusStr == "Disabled") return CardStatus::Disabled;
    if (statusStr == "挂失" || statusStr == "Lost") return CardStatus::Lost;
    if (statusStr == "过期" || statusStr == "Expired") return CardStatus::Expired;
    return CardStatus::Active; // 默认
}

QString OperatorCard::getCardTypeDescription(CardType type) {
    switch (type) {
        case CardType::IC_ID: return "射频识别卡，支持IC卡和ID卡格式";
        case CardType::CPU: return "智能CPU卡，支持加密和复杂应用";
        case CardType::Fingerprint: return "生物识别，指纹验证";
        case CardType::Face: return "生物识别，人脸识别";
        case CardType::Phone: return "手机号码作为门禁凭证";
        case CardType::IDCard: return "身份证号码作为门禁凭证";
        case CardType::Panic: return "紧急情况下使用的胁迫卡";
        case CardType::Master: return "管理员使用的母卡";
        default: return "未知卡片类型";
    }
}

} // namespace AccessControl 