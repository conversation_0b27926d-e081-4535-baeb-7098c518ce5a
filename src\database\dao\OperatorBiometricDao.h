#ifndef USERBIOMETRICDAO_H
#define USERBIOMETRICDAO_H

#include "../IDatabaseProvider.h"
#include "../../models/UserBiometric.h"
#include <QList>
#include <QVariant>
#include <memory>

namespace AccessControl {

/**
 * @brief 用户生物识别数据访问对象
 * 负责用户生物识别信息的数据库操作
 */
class OperatorBiometricDao {
public:
    explicit OperatorBiometricDao(std::shared_ptr<IDatabaseProvider> provider);
    ~OperatorBiometricDao() = default;

    // ========== 表管理 ==========
    
    /**
     * @brief 创建用户生物识别表
     * @return 创建是否成功
     */
    bool createTable();
    
    /**
     * @brief 检查用户生物识别表是否存在
     * @return 表是否存在
     */
    bool tableExists();
    
    /**
     * @brief 初始化表结构
     * @return 初始化是否成功
     */
    bool initializeTable();

    // ========== CRUD 操作 ==========
    
    /**
     * @brief 创建用户生物识别信息
     * @param biometric 用户生物识别对象
     * @return 创建是否成功，成功时会设置ID
     */
    bool createUserBiometric(UserBiometric& biometric);
    
    /**
     * @brief 根据ID获取用户生物识别信息
     * @param biometricId 生物识别ID
     * @return 用户生物识别对象
     */
    UserBiometric getUserBiometricById(int biometricId);
    
    /**
     * @brief 根据用户ID获取用户生物识别信息
     * @param userId 用户ID
     * @return 用户生物识别对象
     */
    UserBiometric getUserBiometricByUserId(int userId);
    
    /**
     * @brief 更新用户生物识别信息
     * @param biometric 用户生物识别对象
     * @return 更新是否成功
     */
    bool updateUserBiometric(const UserBiometric& biometric);
    
    /**
     * @brief 删除用户生物识别信息
     * @param biometricId 生物识别ID
     * @return 删除是否成功
     */
    bool deleteUserBiometric(int biometricId);
    
    /**
     * @brief 根据用户ID删除用户生物识别信息
     * @param userId 用户ID
     * @return 删除是否成功
     */
    bool deleteUserBiometricByUserId(int userId);
    
    /**
     * @brief 获取所有用户生物识别信息
     * @return 用户生物识别列表
     */
    QList<UserBiometric> getAllUserBiometrics();

    // ========== 照片管理 ==========
    
    /**
     * @brief 更新用户照片
     * @param userId 用户ID
     * @param photoPath 照片路径
     * @param photoData 照片数据
     * @return 更新是否成功
     */
    bool updateUserPhoto(int userId, const QString& photoPath, const QByteArray& photoData);
    
    /**
     * @brief 获取用户照片数据
     * @param userId 用户ID
     * @return 照片数据
     */
    QByteArray getUserPhotoData(int userId);
    
    /**
     * @brief 获取用户照片路径
     * @param userId 用户ID
     * @return 照片路径
     */
    QString getUserPhotoPath(int userId);
    
    /**
     * @brief 删除用户照片
     * @param userId 用户ID
     * @return 删除是否成功
     */
    bool deleteUserPhoto(int userId);

    // ========== 指纹管理 ==========
    
    /**
     * @brief 更新用户指纹模板
     * @param userId 用户ID
     * @param templateData 指纹模板数据
     * @return 更新是否成功
     */
    bool updateUserFingerprint(int userId, const QByteArray& templateData);
    
    /**
     * @brief 获取用户指纹模板
     * @param userId 用户ID
     * @return 指纹模板数据
     */
    QByteArray getUserFingerprintTemplate(int userId);
    
    /**
     * @brief 删除用户指纹模板
     * @param userId 用户ID
     * @return 删除是否成功
     */
    bool deleteUserFingerprint(int userId);

    // ========== 人脸管理 ==========
    
    /**
     * @brief 更新用户人脸模板
     * @param userId 用户ID
     * @param templateData 人脸模板数据
     * @return 更新是否成功
     */
    bool updateUserFace(int userId, const QByteArray& templateData);
    
    /**
     * @brief 获取用户人脸模板
     * @param userId 用户ID
     * @return 人脸模板数据
     */
    QByteArray getUserFaceTemplate(int userId);
    
    /**
     * @brief 删除用户人脸模板
     * @param userId 用户ID
     * @return 删除是否成功
     */
    bool deleteUserFace(int userId);

    // ========== 查询和搜索 ==========
    
    /**
     * @brief 根据生物识别类型获取用户列表
     * @param type 生物识别类型
     * @return 用户ID列表
     */
    QList<int> getUsersWithBiometricType(UserBiometric::BiometricType type);
    
    /**
     * @brief 搜索有生物识别数据的用户
     * @param hasPhoto 是否有照片
     * @param hasFingerprint 是否有指纹
     * @param hasFace 是否有人脸
     * @return 用户ID列表
     */
    QList<int> searchUsersWithBiometric(bool hasPhoto = false,
                                       bool hasFingerprint = false,
                                       bool hasFace = false);

    // ========== 统计信息 ==========
    
    /**
     * @brief 获取用户生物识别信息总数
     * @return 总数
     */
    int getUserBiometricCount();
    
    /**
     * @brief 获取有生物识别数据的用户数
     * @return 用户数
     */
    int getUsersWithBiometricCount();
    
    /**
     * @brief 获取生物识别类型分布
     * @return 类型分布映射
     */
    QMap<QString, int> getBiometricTypeDistribution();

    // ========== 批量操作 ==========
    
    /**
     * @brief 批量创建用户生物识别信息
     * @param biometrics 用户生物识别列表
     * @return 成功创建的数量
     */
    int batchCreateUserBiometrics(const QList<UserBiometric>& biometrics);
    
    /**
     * @brief 批量更新用户生物识别信息
     * @param biometrics 用户生物识别列表
     * @return 成功更新的数量
     */
    int batchUpdateUserBiometrics(const QList<UserBiometric>& biometrics);

private:
    std::shared_ptr<IDatabaseProvider> m_provider;

    /**
     * @brief 从查询结果构建用户生物识别对象
     * @param query 查询结果
     * @return 用户生物识别对象
     */
    UserBiometric buildUserBiometricFromQuery(const QSqlQuery& query);
    
    /**
     * @brief 获取用户生物识别表的SQL创建语句
     * @return SQL语句
     */
    QString getUserBiometricTableSQL();
    
    /**
     * @brief 获取插入用户生物识别信息的SQL语句
     * @return SQL语句
     */
    QString getInsertUserBiometricSQL();
    
    /**
     * @brief 获取更新用户生物识别信息的SQL语句
     * @return SQL语句
     */
    QString getUpdateUserBiometricSQL();
    
    /**
     * @brief 检查数据库提供者是否有效
     * @return 是否有效
     */
    bool isProviderValid();
};

} // namespace AccessControl

#endif // USERBIOMETRICDAO_H 