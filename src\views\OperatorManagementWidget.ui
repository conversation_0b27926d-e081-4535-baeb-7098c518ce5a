<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>OperatorManagementWidget</class>
 <widget class="QWidget" name="OperatorManagementWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>800</width>
    <height>600</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>操作员管理</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <widget class="QFrame" name="buttonFrame">
     <property name="frameShape">
      <enum>QFrame::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout_2">
      <item>
       <widget class="QPushButton" name="addButton">
        <property name="text">
         <string>添加</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="editButton">
        <property name="text">
         <string>编辑</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="deleteButton">
        <property name="text">
         <string>删除</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="resetPasswordButton">
        <property name="text">
         <string>重置密码</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="changeStatusButton">
        <property name="text">
         <string>更改状态</string>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QFrame" name="searchFrame">
     <property name="frameShape">
      <enum>QFrame::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout">
      <item>
       <widget class="QLabel" name="usernameLabel">
        <property name="text">
         <string>用户名/姓名:</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QLineEdit" name="usernameSearchEdit">
        <property name="placeholderText">
         <string>输入用户名或姓名</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QLabel" name="roleLabel">
        <property name="text">
         <string>角色:</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QComboBox" name="roleFilterCombo"/>
      </item>
      <item>
       <widget class="QPushButton" name="searchButton">
        <property name="text">
         <string>查询</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="filterButton">
        <property name="text">
         <string>筛选</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="clearFilterButton">
        <property name="text">
         <string>清除筛选</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QTableWidget" name="operatorTableWidget">
     <property name="alternatingRowColors">
      <bool>true</bool>
     </property>
     <property name="selectionBehavior">
      <enum>QAbstractItemView::SelectRows</enum>
     </property>
     <attribute name="horizontalHeaderStretchLastSection">
      <bool>true</bool>
     </attribute>
    </widget>
   </item>
   <item>
    <widget class="QFrame" name="statusFrame">
     <property name="frameShape">
      <enum>QFrame::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout_3">
      <item>
       <widget class="QLabel" name="totalLabel">
        <property name="text">
         <string>共 0 个操作员</string>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer_2">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QLabel" name="statusLabel">
        <property name="text">
         <string>就绪</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui> 