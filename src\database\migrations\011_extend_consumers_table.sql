-- 迁移文件：011_extend_consumers_table.sql
-- 描述：扩展consumers表，添加基本信息的缺失字段，创建consumer_cards表
-- 创建时间：2024-12-19
-- 作者：AI Assistant

-- ========== 扩展consumers表（基本信息） ==========
ALTER TABLE consumers ADD COLUMN gender INTEGER DEFAULT 0;                 -- 性别：0-未知, 1-男, 2-女
ALTER TABLE consumers ADD COLUMN employee_type INTEGER DEFAULT 0;           -- 员工类型：0-正式员工, 1-临时员工, 2-实习生, 3-外包人员
ALTER TABLE consumers ADD COLUMN hire_date DATE DEFAULT (date('now'));      -- 入职日期
ALTER TABLE consumers ADD COLUMN leave_date DATE;                           -- 离职日期
ALTER TABLE consumers ADD COLUMN emergency_contact VARCHAR(100);            -- 紧急联系人
ALTER TABLE consumers ADD COLUMN emergency_phone VARCHAR(20);               -- 紧急联系人电话
ALTER TABLE consumers ADD COLUMN notes TEXT;                                -- 备注信息

-- ========== 创建consumer_cards表 ==========

CREATE TABLE IF NOT EXISTS consumer_cards (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    consumer_id INTEGER NOT NULL,            -- 消费者ID
    card_number VARCHAR(32) NOT NULL,        -- 卡号（支持字母数字混合，最长32字符）
    card_type INTEGER NOT NULL DEFAULT 0,    -- 卡类型：0-IC/ID卡, 1-CPU卡, 2-手机APP, 3-手机NFC, 4-指纹, 5-人脸, 6-手机号, 7-身份证
    is_primary BOOLEAN DEFAULT 0,            -- 是否主卡
    is_biometric BOOLEAN DEFAULT 0,          -- 是否生物识别卡（指纹、人脸）
    status INTEGER NOT NULL DEFAULT 0,       -- 状态：0-正常, 1-挂失, 2-注销
    valid_from DATE DEFAULT (date('now')),   -- 有效期开始
    valid_until DATE DEFAULT '2099-12-31',   -- 有效期结束
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (consumer_id) REFERENCES consumers(id) ON DELETE CASCADE
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_consumer_cards_consumer_id ON consumer_cards(consumer_id);
CREATE INDEX IF NOT EXISTS idx_consumer_cards_card_number ON consumer_cards(card_number);
CREATE INDEX IF NOT EXISTS idx_consumer_cards_card_type ON consumer_cards(card_type);
CREATE INDEX IF NOT EXISTS idx_consumer_cards_status ON consumer_cards(status);
CREATE INDEX IF NOT EXISTS idx_consumer_cards_is_primary ON consumer_cards(is_primary);
CREATE INDEX IF NOT EXISTS idx_consumer_cards_is_biometric ON consumer_cards(is_biometric);

-- 重新创建触发器
CREATE TRIGGER IF NOT EXISTS trigger_consumer_cards_update 
AFTER UPDATE ON consumer_cards
FOR EACH ROW
BEGIN
    UPDATE consumer_cards SET updated_at = CURRENT_TIMESTAMP WHERE id = OLD.id;
END;

-- 创建唯一约束
CREATE UNIQUE INDEX IF NOT EXISTS idx_consumer_cards_unique_card_number ON consumer_cards(card_number);

-- ========== 创建consumer_biometrics表 ==========

CREATE TABLE IF NOT EXISTS consumer_biometrics (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    consumer_id INTEGER NOT NULL,
    biometric_type INTEGER NOT NULL,      -- 生物识别类型：4-指纹, 5-人脸
    template_data BLOB,                   -- 模板数据
    template_id VARCHAR(50),              -- 模板ID
    quality_score INTEGER DEFAULT 0,      -- 质量得分 (0-100)
    enrollment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status INTEGER DEFAULT 0,             -- 0-正常, 1-禁用
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (consumer_id) REFERENCES consumers(id) ON DELETE CASCADE
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_consumer_biometrics_consumer_id ON consumer_biometrics(consumer_id);
CREATE INDEX IF NOT EXISTS idx_consumer_biometrics_type ON consumer_biometrics(biometric_type);
CREATE INDEX IF NOT EXISTS idx_consumer_biometrics_template_id ON consumer_biometrics(template_id);

-- ========== 创建consumer_photos表 ==========

CREATE TABLE IF NOT EXISTS consumer_photos (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    consumer_id INTEGER NOT NULL,
    photo_path VARCHAR(255),               -- 照片文件路径
    photo_type INTEGER DEFAULT 0,         -- 照片类型：0-证件照, 1-现场照
    file_size INTEGER DEFAULT 0,          -- 文件大小(字节)
    image_width INTEGER DEFAULT 0,        -- 图片宽度
    image_height INTEGER DEFAULT 0,       -- 图片高度
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (consumer_id) REFERENCES consumers(id) ON DELETE CASCADE
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_consumer_photos_consumer_id ON consumer_photos(consumer_id);
CREATE INDEX IF NOT EXISTS idx_consumer_photos_type ON consumer_photos(photo_type); 