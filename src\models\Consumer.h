#ifndef CONSUMER_H
#define CONSUMER_H

#include <QString>
#include <QDateTime>
#include <QDate>

namespace AccessControl {

/**
 * @brief 门禁卡持有者模型类
 * 表示持有门禁卡的用户，与系统登录用户(User)区分开
 */
class Consumer {
public:
    /**
     * @brief 门禁卡持有者状态枚举
     */
    enum class Status {
        Active,     // 正常
        Inactive,   // 停用
        Expired     // 过期
    };

public:
    Consumer();
    ~Consumer() = default;

    // ========== 基本属性 Getters ==========
    int id() const { return m_id; }
    const QString& workNumber() const { return m_workNumber; }
    const QString& realName() const { return m_realName; }
    const QString& phoneNumber() const { return m_phoneNumber; }
    const QString& idNumber() const { return m_idNumber; }
    int departmentId() const { return m_departmentId; }
    Status status() const { return m_status; }
    bool accessEnabled() const { return m_accessEnabled; }
    bool attendanceEnabled() const { return m_attendanceEnabled; }
    bool shiftWork() const { return m_shiftWork; }
    const QDate& validFrom() const { return m_validFrom; }
    const QDate& validUntil() const { return m_validUntil; }
    const QDateTime& createdAt() const { return m_createdAt; }
    const QDateTime& updatedAt() const { return m_updatedAt; }
    
    // ========== 基本属性 Setters ==========
    void setId(int id) { m_id = id; }
    void setWorkNumber(const QString& workNumber) { m_workNumber = workNumber; }
    void setRealName(const QString& realName) { m_realName = realName; }
    void setPhoneNumber(const QString& phoneNumber) { m_phoneNumber = phoneNumber; }
    void setIdNumber(const QString& idNumber) { m_idNumber = idNumber; }
    void setDepartmentId(int departmentId) { m_departmentId = departmentId; }
    void setStatus(Status status) { m_status = status; }
    void setAccessEnabled(bool enabled) { m_accessEnabled = enabled; }
    void setAttendanceEnabled(bool enabled) { m_attendanceEnabled = enabled; }
    void setShiftWork(bool shiftWork) { m_shiftWork = shiftWork; }
    void setValidFrom(const QDate& validFrom) { m_validFrom = validFrom; }
    void setValidUntil(const QDate& validUntil) { m_validUntil = validUntil; }
    void setCreatedAt(const QDateTime& createdAt) { m_createdAt = createdAt; }
    void setUpdatedAt(const QDateTime& updatedAt) { m_updatedAt = updatedAt; }
    
    // ========== 辅助方法 ==========
    bool isValid() const;
    bool isExpired() const;
    bool isInValidPeriod() const;
    QString getDisplayName() const;
    
    // ========== 静态方法 ==========
    static QString statusToString(Status status);

private:
    // 基本信息
    int m_id;
    QString m_workNumber;      // 工号
    QString m_realName;        // 姓名
    QString m_phoneNumber;     // 手机号
    QString m_idNumber;        // 身份证号
    int m_departmentId;        // 部门ID
    Status m_status;           // 状态
    
    // 权限信息
    bool m_accessEnabled;      // 门禁权限启用
    bool m_attendanceEnabled;  // 考勤权限启用
    bool m_shiftWork;          // 是否倒班
    QDate m_validFrom;         // 有效期开始
    QDate m_validUntil;        // 有效期结束
    
    // 时间信息
    QDateTime m_createdAt;     // 创建时间
    QDateTime m_updatedAt;     // 更新时间
};

} // namespace AccessControl

#endif // CONSUMER_H 