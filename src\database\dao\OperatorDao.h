#ifndef OPERATORDAO_H
#define OPERATORDAO_H

#include "../../models/Operator.h"
#include "../IDatabaseProvider.h"
#include <memory>
#include <optional>
#include <vector>

namespace AccessControl {

class OperatorDao {
public:
    explicit OperatorDao(DatabaseProviderPtr provider);

    bool create(Operator& user);
    std::optional<Operator> getById(int id);
    std::optional<Operator> getByUsername(const QString& username);
    bool update(const Operator& user);
    bool remove(int id);

    bool authenticateUser(const QString& username, const QString& password, Operator& user);
    bool updateLastLogin(int userId, const QDateTime& lastLoginAt, const QString& lastLoginIp);
    std::vector<Operator> getAllUsers();
    std::vector<Operator> getUsersByDepartment(int departmentId);
    std::vector<Operator> searchOperators(const QString& searchTerm);
    std::vector<Operator> searchOperatorsByName(const QString& name);
    std::vector<Operator> searchOperatorsByCard(const QString& cardNumber);
    std::vector<Operator> getUsersWithAdvancedFilter(const QString& name, const QString& cardNumber, int departmentId);

    // 操作员管理相关方法
    std::vector<Operator> getOperators();
    std::vector<Operator> getOperatorsWithFilter(const QString& usernameOrName, int roleFilter = -1);

    bool isUsernameExists(const QString& username, int excludeId = -1);
    bool isPhoneNumberExists(const QString& phoneNumber, int excludeId = -1);
    bool isIdNumberExists(const QString& idNumber, int excludeId = -1);
    bool isWorkNumberExists(const QString& workNumber, int excludeId = -1);

    int getTotalUserCount();
    int getActiveUserCount();
    int getUserCountByDepartment(int departmentId);

    bool batchCreate(const std::vector<Operator>& users);
    bool batchUpdate(const std::vector<Operator>& users);
    bool batchDelete(const std::vector<int>& userIds);

private:
    DatabaseProviderPtr m_provider;

    Operator mapRowToOperator(const QSqlQuery& query);
    void bindOperatorToQuery(QSqlQuery& query, const Operator& user, bool includeId = false);
};

} // namespace AccessControl

#endif // OPERATORDAO_H
