-- 用户扩展信息表迁移脚本
-- 创建时间: 2024-12-19
-- 描述: 创建用户扩展信息表，存储用户的详细个人信息

-- SQLite版本
-- 创建用户扩展信息表
CREATE TABLE IF NOT EXISTS user_profiles (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    gender INTEGER DEFAULT 0,  -- 0:未知, 1:男, 2:女
    ethnicity TEXT,            -- 民族
    religion TEXT,             -- 宗教
    native_place TEXT,         -- 籍贯
    birth_date TEXT,           -- 出生日期 (YYYY-MM-DD)
    marital_status INTEGER DEFAULT 0,  -- 0:未知, 1:未婚, 2:已婚, 3:离异, 4:丧偶
    political_status TEXT,     -- 政治面貌
    education TEXT,            -- 学历
    work_phone TEXT,           -- 工作电话
    home_phone TEXT,           -- 家庭电话
    english_name TEXT,         -- 英文名
    organization TEXT,         -- 单位
    job_title TEXT,            -- 职称
    skill_level TEXT,          -- 技术等级
    certificate_name TEXT,     -- 证件名称
    certificate_number TEXT,   -- 证件号
    social_security_number TEXT, -- 社保号
    hire_date TEXT,            -- 入职时间 (YYYY-MM-DD)
    termination_date TEXT,     -- 离职时间 (YYYY-MM-DD)
    email_address TEXT,        -- 电子邮箱
    mailing_address TEXT,      -- 通讯地址
    postal_code TEXT,          -- 邮编
    remarks TEXT,              -- 备注
    created_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
    updated_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_user_profiles_user_id ON user_profiles(user_id);
CREATE INDEX IF NOT EXISTS idx_user_profiles_gender ON user_profiles(gender);
CREATE INDEX IF NOT EXISTS idx_user_profiles_education ON user_profiles(education);
CREATE INDEX IF NOT EXISTS idx_user_profiles_organization ON user_profiles(organization);
CREATE INDEX IF NOT EXISTS idx_user_profiles_birth_date ON user_profiles(birth_date);
CREATE INDEX IF NOT EXISTS idx_user_profiles_hire_date ON user_profiles(hire_date);

-- 创建唯一约束，确保每个用户只有一条扩展信息记录
CREATE UNIQUE INDEX IF NOT EXISTS idx_user_profiles_unique_user ON user_profiles(user_id);

-- 创建触发器，自动更新updated_at字段
CREATE TRIGGER IF NOT EXISTS trigger_user_profiles_updated_at
    AFTER UPDATE ON user_profiles
    FOR EACH ROW
BEGIN
    UPDATE user_profiles SET updated_at = datetime('now', 'localtime') WHERE id = NEW.id;
END;

-- PostgreSQL版本
-- 注意：PostgreSQL版本需要在PostgreSQLProvider中单独处理
/*
CREATE TABLE IF NOT EXISTS user_profiles (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    gender INTEGER DEFAULT 0,
    ethnicity VARCHAR(50),
    religion VARCHAR(50),
    native_place VARCHAR(100),
    birth_date DATE,
    marital_status INTEGER DEFAULT 0,
    political_status VARCHAR(50),
    education VARCHAR(50),
    work_phone VARCHAR(20),
    home_phone VARCHAR(20),
    english_name VARCHAR(100),
    organization VARCHAR(200),
    job_title VARCHAR(100),
    skill_level VARCHAR(50),
    certificate_name VARCHAR(100),
    certificate_number VARCHAR(50),
    social_security_number VARCHAR(50),
    hire_date DATE,
    termination_date DATE,
    email_address VARCHAR(200),
    mailing_address TEXT,
    postal_code VARCHAR(10),
    remarks TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_user_profiles_user_id ON user_profiles(user_id);
CREATE INDEX IF NOT EXISTS idx_user_profiles_gender ON user_profiles(gender);
CREATE INDEX IF NOT EXISTS idx_user_profiles_education ON user_profiles(education);
CREATE INDEX IF NOT EXISTS idx_user_profiles_organization ON user_profiles(organization);
CREATE INDEX IF NOT EXISTS idx_user_profiles_birth_date ON user_profiles(birth_date);
CREATE INDEX IF NOT EXISTS idx_user_profiles_hire_date ON user_profiles(hire_date);

-- 创建唯一约束
CREATE UNIQUE INDEX IF NOT EXISTS idx_user_profiles_unique_user ON user_profiles(user_id);

-- 创建触发器函数
CREATE OR REPLACE FUNCTION update_user_profiles_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 创建触发器
CREATE TRIGGER trigger_user_profiles_updated_at
    BEFORE UPDATE ON user_profiles
    FOR EACH ROW
    EXECUTE FUNCTION update_user_profiles_updated_at();
*/ 