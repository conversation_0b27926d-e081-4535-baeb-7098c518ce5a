{"BUILD_DIR": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/AccessControlSystem_autogen", "CMAKE_BINARY_DIR": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug", "CMAKE_CURRENT_BINARY_DIR": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug", "CMAKE_CURRENT_SOURCE_DIR": "C:/Users/<USER>/Documents/AccessControlSystem", "CMAKE_SOURCE_DIR": "C:/Users/<USER>/Documents/AccessControlSystem", "CROSS_CONFIG": false, "GENERATOR": "Ninja", "INCLUDE_DIR": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/AccessControlSystem_autogen/include", "INPUTS": ["C:/Users/<USER>/Documents/AccessControlSystem/src/database/migrations/2024_areas.sql", "C:/Users/<USER>/Documents/AccessControlSystem/src/database/migrations/009_migrate_user_data.sql", "C:/Users/<USER>/Documents/AccessControlSystem/src/database/migrations/005_add_missing_fields_to_users.sql", "C:/Users/<USER>/Documents/AccessControlSystem/src/database/migrations/011_extend_consumers_table.sql", "C:/Users/<USER>/Documents/AccessControlSystem/src/database/migrations/004_extend_users_table.sql", "C:/Users/<USER>/Documents/AccessControlSystem/src/database/migrations/008_create_consumers_table.sql", "C:/Users/<USER>/Documents/AccessControlSystem/src/database/migrations/004_create_user_profiles_table.sql", "C:/Users/<USER>/Documents/AccessControlSystem/resources/department_template.csv", "C:/Users/<USER>/Documents/AccessControlSystem/resources/area_template.csv", "C:/Users/<USER>/Documents/AccessControlSystem/resources/images/lock.png", "C:/Users/<USER>/Documents/AccessControlSystem/resources/sql/migrations/003_create_areas_table.sql", "C:/Users/<USER>/Documents/AccessControlSystem/resources/sql/migrations/010_create_operators_table.sql", "C:/Users/<USER>/Documents/AccessControlSystem/resources/sql/migrations/012_create_departments_table.sql", "C:/Users/<USER>/Documents/AccessControlSystem/resources/sql/migrations/009_migrate_user_data.sql", "C:/Users/<USER>/Documents/AccessControlSystem/resources/sql/migrations/005_create_controllers_table.sql", "C:/Users/<USER>/Documents/AccessControlSystem/resources/sql/migrations/002_create_departments_table.sql", "C:/Users/<USER>/Documents/AccessControlSystem/resources/sql/migrations/007_create_access_records_table.sql", "C:/Users/<USER>/Documents/AccessControlSystem/resources/sql/migrations/011_extend_consumers_table.sql", "C:/Users/<USER>/Documents/AccessControlSystem/resources/sql/migrations/004_extend_users_table.sql", "C:/Users/<USER>/Documents/AccessControlSystem/resources/sql/migrations/006_create_doors_table.sql", "C:/Users/<USER>/Documents/AccessControlSystem/resources/sql/migrations/005_1_add_missing_fields_to_users.sql", "C:/Users/<USER>/Documents/AccessControlSystem/resources/sql/migrations/008_create_consumers_table.sql", "C:/Users/<USER>/Documents/AccessControlSystem/resources/sql/migrations/001_create_users_table.sql", "C:/Users/<USER>/Documents/AccessControlSystem/resources/sql/migrations/004_create_user_profiles_table.sql"], "LOCK_FILE": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/CMakeFiles/AccessControlSystem_autogen.dir/AutoRcc_resources_EWIEGA46WW_Lock.lock", "MULTI_CONFIG": false, "OPTIONS": ["--no-zstd", "-name", "resources"], "OUTPUT_CHECKSUM": "EWIEGA46WW", "OUTPUT_NAME": "qrc_resources.cpp", "RCC_EXECUTABLE": "C:/Qt/6.6.3/mingw_64/./bin/rcc.exe", "RCC_LIST_OPTIONS": ["--list"], "SETTINGS_FILE": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/CMakeFiles/AccessControlSystem_autogen.dir/AutoRcc_resources_EWIEGA46WW_Used.txt", "SOURCE": "C:/Users/<USER>/Documents/AccessControlSystem/resources.qrc", "USE_BETTER_GRAPH": false, "VERBOSITY": 0}