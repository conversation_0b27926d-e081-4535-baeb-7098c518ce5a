#ifndef POSTGRESQLPROVIDER_H
#define POSTGRESQLPROVIDER_H

#include "../IDatabaseProvider.h"
#include <QSqlDatabase>
#include <QMutex>
#include <QTimer>
#include <QRegularExpression>

namespace AccessControl {

/**
 * @brief PostgreSQL数据库提供者
 * 实现PostgreSQL数据库的具体操作
 * 
 * 支持特性：
 * - 连接池管理
 * - 事务隔离级别控制
 * - 分区表支持
 * - JSON数据类型
 * - 高级索引策略
 * - 自动故障恢复
 */
class PostgreSQLProvider : public IDatabaseProvider {
    
public:
    /**
     * @brief 事务隔离级别
     */
    enum IsolationLevel {
        ReadUncommitted = 0,  // READ UNCOMMITTED
        ReadCommitted = 1,    // READ COMMITTED (默认)
        RepeatableRead = 2,   // REPEATABLE READ
        Serializable = 3      // SERIALIZABLE
    };
    
    PostgreSQLProvider();
    ~PostgreSQLProvider() override;
    
    // ========== 连接管理 ==========
    bool connect(const DatabaseConfig& config) override;
    void disconnect() override;
    bool isConnected() const override;
    bool reconnect() override;
    QSqlDatabase getDatabase() override;
    
    // ========== 查询操作 ==========
    QSqlQuery query(const QString& sql) override;
    bool execute(const QString& sql) override;
    QSqlQuery prepareQuery(const QString& sql) override;
    QVariant lastInsertId() override;
    int numRowsAffected() override;
    
    // ========== 事务管理 ==========
    bool transaction() override;
    bool commit() override;
    bool rollback() override;
    
    // ========== 错误处理 ==========
    QString lastError() const override;
    int errorCode() const override;
    bool hasError() const override;
    
    // ========== 数据库特性 ==========
    QString databaseType() const override;
    QString databaseVersion() const override;
    QStringList tableNames() const override;
    bool tableExists(const QString& tableName) const override;
    QStringList columnNames(const QString& tableName) const override;
    
    // ========== 批量操作 ==========
    bool executeBatch(const QStringList& sqlList) override;
    bool batchInsert(const QString& tableName, 
                    const QStringList& columns, 
                    const QList<QVariantList>& values) override;
    
    // ========== 数据库维护 ==========
    bool optimize() override;
    bool checkIntegrity() override;
    qint64 databaseSize() const override;
    
    // ========== 备份恢复 ==========
    bool backup(const QString& backupPath) override;
    bool restore(const QString& backupPath) override;
    
    // ========== PostgreSQL特有功能 ==========
    
    /**
     * @brief 设置事务隔离级别
     * @param level 隔离级别
     * @return 操作是否成功
     */
    bool setIsolationLevel(IsolationLevel level);
    
    /**
     * @brief 创建分区表
     * @param baseTableName 基础表名
     * @param partitionType 分区类型 (RANGE, LIST, HASH)
     * @param partitionColumn 分区列
     * @return 操作是否成功
     */
    bool createPartitionedTable(const QString& baseTableName, 
                               const QString& partitionType, 
                               const QString& partitionColumn);
    
    /**
     * @brief 创建分区
     * @param partitionName 分区名
     * @param baseTableName 基础表名
     * @param partitionCondition 分区条件
     * @return 操作是否成功
     */
    bool createPartition(const QString& partitionName, 
                        const QString& baseTableName, 
                        const QString& partitionCondition);
    
    /**
     * @brief 执行VACUUM命令
     * @param tableName 表名(空表示所有表)
     * @param full 是否执行VACUUM FULL
     * @return 操作是否成功
     */
    bool vacuum(const QString& tableName = QString(), bool full = false);
    
    /**
     * @brief 重建索引
     * @param indexName 索引名(空表示所有索引)
     * @return 操作是否成功
     */
    bool reindex(const QString& indexName = QString());
    
    /**
     * @brief 分析表统计信息
     * @param tableName 表名(空表示所有表)
     * @return 操作是否成功
     */
    bool analyze(const QString& tableName = QString());
    
    /**
     * @brief 获取当前活动连接数
     * @return 连接数
     */
    int activeConnections() const;
    
    /**
     * @brief 获取数据库统计信息
     * @return 统计信息JSON字符串
     */
    QString getDatabaseStats() const;
    
    /**
     * @brief 创建数据库扩展
     * @param extensionName 扩展名
     * @return 操作是否成功
     */
    bool createExtension(const QString& extensionName);
    
    /**
     * @brief 检查扩展是否存在
     * @param extensionName 扩展名
     * @return 是否存在
     */
    bool extensionExists(const QString& extensionName) const;

private:
    QString m_connectionName;      // 连接名称
    QSqlDatabase m_database;       // 数据库连接
    DatabaseConfig m_config;       // 数据库配置
    mutable QMutex m_mutex;        // 线程安全锁
    QString m_lastError;           // 最后错误信息
    int m_errorCode;               // 错误代码
    IsolationLevel m_isolationLevel; // 事务隔离级别
    QTimer* m_healthCheckTimer;    // 健康检查定时器
    int m_reconnectAttempts;       // 重连尝试次数
    static const int MAX_RECONNECT_ATTEMPTS = 3; // 最大重连次数
    
    /**
     * @brief 生成唯一的连接名称
     * @return 连接名称
     */
    QString generateConnectionName();
    
    /**
     * @brief 初始化PostgreSQL设置
     * @return 初始化是否成功
     */
    bool initializeSettings();
    
    /**
     * @brief 设置错误信息
     * @param error 错误信息
     * @param code 错误代码
     */
    void setError(const QString& error, int code = -1);
    
    /**
     * @brief 清除错误信息
     */
    void clearError();
    
    /**
     * @brief 测试数据库连接是否有效
     * @return 连接是否有效
     */
    bool testConnection();
    
    /**
     * @brief 启动健康检查定时器
     */
    void startHealthCheck();
    
    /**
     * @brief 停止健康检查定时器
     */
    void stopHealthCheck();
    
    /**
     * @brief 连接健康检查
     */
    void healthCheck();
    
    /**
     * @brief 获取隔离级别字符串
     * @param level 隔离级别
     * @return 隔离级别字符串
     */
    QString isolationLevelToString(IsolationLevel level) const;
    
    // ========== 无锁版本方法 (仅限内部调用) ==========
    
    bool initializeSettingsUnsafe();
    void setErrorUnsafe(const QString& error, int code = -1);
    void clearErrorUnsafe();
    bool isConnectedUnsafe() const;
    void disconnectUnsafe();
    bool executeUnsafe(const QString& sql);
    bool transactionUnsafe();
    bool commitUnsafe();
    bool rollbackUnsafe();
    bool testConnectionUnsafe();
};

} // namespace AccessControl

#endif // POSTGRESQLPROVIDER_H 