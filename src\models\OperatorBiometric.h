#ifndef USERBIOMETRIC_H
#define USERBIOMETRIC_H

#include <QString>
#include <QDateTime>
#include <QByteArray>
#include <QVariant>

namespace AccessControl {

/**
 * @brief 用户生物识别模型类
 * 管理用户的照片、指纹和人脸识别数据
 */
class OperatorBiometric {
public:
    /**
     * @brief 生物识别类型枚举
     */
    enum class BiometricType {
        Photo = 0,      // 照片
        Fingerprint = 1, // 指纹
        Face = 2        // 人脸
    };

public:
    OperatorBiometric();
    OperatorBiometric(int userId);
    ~OperatorBiometric() = default;

    // ========== 基本属性 ==========
    int id() const { return m_id; }
    void setId(int id) { m_id = id; }

    int userId() const { return m_userId; }
    void setUserId(int userId) { m_userId = userId; }

    // ========== 照片管理 ==========
    QString photoPath() const { return m_photoPath; }
    void setPhotoPath(const QString& path) { m_photoPath = path; }

    QByteArray photoData() const { return m_photoData; }
    void setPhotoData(const QByteArray& data) { m_photoData = data; }

    bool hasPhoto() const { return !m_photoPath.isEmpty() || !m_photoData.isEmpty(); }

    // ========== 指纹管理 ==========
    QByteArray fingerprintTemplate() const { return m_fingerprintTemplate; }
    void setFingerprintTemplate(const QByteArray& templateData) { m_fingerprintTemplate = templateData; }

    bool hasFingerprint() const { return !m_fingerprintTemplate.isEmpty(); }

    // ========== 人脸管理 ==========
    QByteArray faceTemplate() const { return m_faceTemplate; }
    void setFaceTemplate(const QByteArray& templateData) { m_faceTemplate = templateData; }

    bool hasFace() const { return !m_faceTemplate.isEmpty(); }

    // ========== 时间属性 ==========
    QDateTime createdAt() const { return m_createdAt; }
    void setCreatedAt(const QDateTime& dateTime) { m_createdAt = dateTime; }

    QDateTime updatedAt() const { return m_updatedAt; }
    void setUpdatedAt(const QDateTime& dateTime) { m_updatedAt = dateTime; }

    // ========== 验证方法 ==========
    bool isValid() const;
    bool hasAnyBiometric() const;
    bool hasBiometricType(BiometricType type) const;

    // ========== 数据管理 ==========
    bool savePhotoToFile(const QString& filePath);
    bool loadPhotoFromFile(const QString& filePath);
    bool saveFingerprintTemplate(const QByteArray& templateData);
    bool saveFaceTemplate(const QByteArray& templateData);
    void clearBiometricData(BiometricType type);

    // ========== 序列化 ==========
    QVariantMap toVariantMap() const;
    void fromVariantMap(const QVariantMap& map);

    // ========== 静态方法 ==========
    static QString biometricTypeToString(BiometricType type);
    static BiometricType stringToBiometricType(const QString& typeStr);
    static QString getDefaultPhotoPath(int userId);
    static bool isValidPhotoFormat(const QString& filePath);

private:
    // 基本信息
    int m_id;
    int m_userId;

    // 照片数据
    QString m_photoPath;
    QByteArray m_photoData;

    // 生物识别模板
    QByteArray m_fingerprintTemplate;
    QByteArray m_faceTemplate;

    // 时间信息
    QDateTime m_createdAt;
    QDateTime m_updatedAt;
};

} // namespace AccessControl

#endif // USERBIOMETRIC_H 