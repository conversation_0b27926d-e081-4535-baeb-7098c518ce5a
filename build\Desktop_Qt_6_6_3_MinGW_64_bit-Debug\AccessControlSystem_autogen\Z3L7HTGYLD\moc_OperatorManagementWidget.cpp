/****************************************************************************
** Meta object code from reading C++ file 'OperatorManagementWidget.h'
**
** Created by: The Qt Meta Object Compiler version 68 (Qt 6.6.3)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../src/views/OperatorManagementWidget.h"
#include <QtGui/qtextcursor.h>
#include <QtCore/qmetatype.h>

#if __has_include(<QtCore/qtmochelpers.h>)
#include <QtCore/qtmochelpers.h>
#else
QT_BEGIN_MOC_NAMESPACE
#endif


#include <memory>

#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'OperatorManagementWidget.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 68
#error "This file was generated using the moc from 6.6.3. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {

#ifdef QT_MOC_HAS_STRINGDATA
struct qt_meta_stringdata_CLASSAccessControlSCOPEOperatorManagementWidgetENDCLASS_t {};
constexpr auto qt_meta_stringdata_CLASSAccessControlSCOPEOperatorManagementWidgetENDCLASS = QtMocHelpers::stringData(
    "AccessControl::OperatorManagementWidget",
    "loadOperators",
    "",
    "refreshOperatorTable",
    "onAdd",
    "onEdit",
    "onDelete",
    "onResetPassword",
    "onChangeStatus",
    "onSearch",
    "onFilter",
    "onClearFilter",
    "onUsernameSearchChanged",
    "onRoleFilterChanged",
    "onTableDoubleClicked",
    "row",
    "column",
    "onTableSelectionChanged"
);
#else  // !QT_MOC_HAS_STRING_DATA
struct qt_meta_stringdata_CLASSAccessControlSCOPEOperatorManagementWidgetENDCLASS_t {
    uint offsetsAndSizes[36];
    char stringdata0[40];
    char stringdata1[14];
    char stringdata2[1];
    char stringdata3[21];
    char stringdata4[6];
    char stringdata5[7];
    char stringdata6[9];
    char stringdata7[16];
    char stringdata8[15];
    char stringdata9[9];
    char stringdata10[9];
    char stringdata11[14];
    char stringdata12[24];
    char stringdata13[20];
    char stringdata14[21];
    char stringdata15[4];
    char stringdata16[7];
    char stringdata17[24];
};
#define QT_MOC_LITERAL(ofs, len) \
    uint(sizeof(qt_meta_stringdata_CLASSAccessControlSCOPEOperatorManagementWidgetENDCLASS_t::offsetsAndSizes) + ofs), len 
Q_CONSTINIT static const qt_meta_stringdata_CLASSAccessControlSCOPEOperatorManagementWidgetENDCLASS_t qt_meta_stringdata_CLASSAccessControlSCOPEOperatorManagementWidgetENDCLASS = {
    {
        QT_MOC_LITERAL(0, 39),  // "AccessControl::OperatorManage..."
        QT_MOC_LITERAL(40, 13),  // "loadOperators"
        QT_MOC_LITERAL(54, 0),  // ""
        QT_MOC_LITERAL(55, 20),  // "refreshOperatorTable"
        QT_MOC_LITERAL(76, 5),  // "onAdd"
        QT_MOC_LITERAL(82, 6),  // "onEdit"
        QT_MOC_LITERAL(89, 8),  // "onDelete"
        QT_MOC_LITERAL(98, 15),  // "onResetPassword"
        QT_MOC_LITERAL(114, 14),  // "onChangeStatus"
        QT_MOC_LITERAL(129, 8),  // "onSearch"
        QT_MOC_LITERAL(138, 8),  // "onFilter"
        QT_MOC_LITERAL(147, 13),  // "onClearFilter"
        QT_MOC_LITERAL(161, 23),  // "onUsernameSearchChanged"
        QT_MOC_LITERAL(185, 19),  // "onRoleFilterChanged"
        QT_MOC_LITERAL(205, 20),  // "onTableDoubleClicked"
        QT_MOC_LITERAL(226, 3),  // "row"
        QT_MOC_LITERAL(230, 6),  // "column"
        QT_MOC_LITERAL(237, 23)   // "onTableSelectionChanged"
    },
    "AccessControl::OperatorManagementWidget",
    "loadOperators",
    "",
    "refreshOperatorTable",
    "onAdd",
    "onEdit",
    "onDelete",
    "onResetPassword",
    "onChangeStatus",
    "onSearch",
    "onFilter",
    "onClearFilter",
    "onUsernameSearchChanged",
    "onRoleFilterChanged",
    "onTableDoubleClicked",
    "row",
    "column",
    "onTableSelectionChanged"
};
#undef QT_MOC_LITERAL
#endif // !QT_MOC_HAS_STRING_DATA
} // unnamed namespace

Q_CONSTINIT static const uint qt_meta_data_CLASSAccessControlSCOPEOperatorManagementWidgetENDCLASS[] = {

 // content:
      12,       // revision
       0,       // classname
       0,    0, // classinfo
      14,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // slots: name, argc, parameters, tag, flags, initial metatype offsets
       1,    0,   98,    2, 0x08,    1 /* Private */,
       3,    0,   99,    2, 0x08,    2 /* Private */,
       4,    0,  100,    2, 0x08,    3 /* Private */,
       5,    0,  101,    2, 0x08,    4 /* Private */,
       6,    0,  102,    2, 0x08,    5 /* Private */,
       7,    0,  103,    2, 0x08,    6 /* Private */,
       8,    0,  104,    2, 0x08,    7 /* Private */,
       9,    0,  105,    2, 0x08,    8 /* Private */,
      10,    0,  106,    2, 0x08,    9 /* Private */,
      11,    0,  107,    2, 0x08,   10 /* Private */,
      12,    0,  108,    2, 0x08,   11 /* Private */,
      13,    0,  109,    2, 0x08,   12 /* Private */,
      14,    2,  110,    2, 0x08,   13 /* Private */,
      17,    0,  115,    2, 0x08,   16 /* Private */,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Int, QMetaType::Int,   15,   16,
    QMetaType::Void,

       0        // eod
};

Q_CONSTINIT const QMetaObject AccessControl::OperatorManagementWidget::staticMetaObject = { {
    QMetaObject::SuperData::link<QWidget::staticMetaObject>(),
    qt_meta_stringdata_CLASSAccessControlSCOPEOperatorManagementWidgetENDCLASS.offsetsAndSizes,
    qt_meta_data_CLASSAccessControlSCOPEOperatorManagementWidgetENDCLASS,
    qt_static_metacall,
    nullptr,
    qt_incomplete_metaTypeArray<qt_meta_stringdata_CLASSAccessControlSCOPEOperatorManagementWidgetENDCLASS_t,
        // Q_OBJECT / Q_GADGET
        QtPrivate::TypeAndForceComplete<OperatorManagementWidget, std::true_type>,
        // method 'loadOperators'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'refreshOperatorTable'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onAdd'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onEdit'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onDelete'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onResetPassword'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onChangeStatus'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onSearch'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onFilter'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onClearFilter'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onUsernameSearchChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onRoleFilterChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onTableDoubleClicked'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        // method 'onTableSelectionChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>
    >,
    nullptr
} };

void AccessControl::OperatorManagementWidget::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<OperatorManagementWidget *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->loadOperators(); break;
        case 1: _t->refreshOperatorTable(); break;
        case 2: _t->onAdd(); break;
        case 3: _t->onEdit(); break;
        case 4: _t->onDelete(); break;
        case 5: _t->onResetPassword(); break;
        case 6: _t->onChangeStatus(); break;
        case 7: _t->onSearch(); break;
        case 8: _t->onFilter(); break;
        case 9: _t->onClearFilter(); break;
        case 10: _t->onUsernameSearchChanged(); break;
        case 11: _t->onRoleFilterChanged(); break;
        case 12: _t->onTableDoubleClicked((*reinterpret_cast< std::add_pointer_t<int>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[2]))); break;
        case 13: _t->onTableSelectionChanged(); break;
        default: ;
        }
    }
}

const QMetaObject *AccessControl::OperatorManagementWidget::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *AccessControl::OperatorManagementWidget::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_CLASSAccessControlSCOPEOperatorManagementWidgetENDCLASS.stringdata0))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int AccessControl::OperatorManagementWidget::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 14)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 14;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 14)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 14;
    }
    return _id;
}
QT_WARNING_POP
