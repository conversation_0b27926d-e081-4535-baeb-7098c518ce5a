/****************************************************************************
** Meta object code from reading C++ file 'ConsumerPhotoWidget.h'
**
** Created by: The Qt Meta Object Compiler version 68 (Qt 6.6.3)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../src/views/ConsumerPhotoWidget.h"
#include <QtGui/qtextcursor.h>
#include <QtCore/qmetatype.h>

#if __has_include(<QtCore/qtmochelpers.h>)
#include <QtCore/qtmochelpers.h>
#else
QT_BEGIN_MOC_NAMESPACE
#endif


#include <memory>

#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'ConsumerPhotoWidget.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 68
#error "This file was generated using the moc from 6.6.3. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {

#ifdef QT_MOC_HAS_STRINGDATA
struct qt_meta_stringdata_CLASSAccessControlSCOPEConsumerPhotoWidgetENDCLASS_t {};
constexpr auto qt_meta_stringdata_CLASSAccessControlSCOPEConsumerPhotoWidgetENDCLASS = QtMocHelpers::stringData(
    "AccessControl::ConsumerPhotoWidget",
    "photoChanged",
    "",
    "uploadPhoto",
    "takePhoto",
    "deletePhoto"
);
#else  // !QT_MOC_HAS_STRING_DATA
struct qt_meta_stringdata_CLASSAccessControlSCOPEConsumerPhotoWidgetENDCLASS_t {
    uint offsetsAndSizes[12];
    char stringdata0[35];
    char stringdata1[13];
    char stringdata2[1];
    char stringdata3[12];
    char stringdata4[10];
    char stringdata5[12];
};
#define QT_MOC_LITERAL(ofs, len) \
    uint(sizeof(qt_meta_stringdata_CLASSAccessControlSCOPEConsumerPhotoWidgetENDCLASS_t::offsetsAndSizes) + ofs), len 
Q_CONSTINIT static const qt_meta_stringdata_CLASSAccessControlSCOPEConsumerPhotoWidgetENDCLASS_t qt_meta_stringdata_CLASSAccessControlSCOPEConsumerPhotoWidgetENDCLASS = {
    {
        QT_MOC_LITERAL(0, 34),  // "AccessControl::ConsumerPhotoW..."
        QT_MOC_LITERAL(35, 12),  // "photoChanged"
        QT_MOC_LITERAL(48, 0),  // ""
        QT_MOC_LITERAL(49, 11),  // "uploadPhoto"
        QT_MOC_LITERAL(61, 9),  // "takePhoto"
        QT_MOC_LITERAL(71, 11)   // "deletePhoto"
    },
    "AccessControl::ConsumerPhotoWidget",
    "photoChanged",
    "",
    "uploadPhoto",
    "takePhoto",
    "deletePhoto"
};
#undef QT_MOC_LITERAL
#endif // !QT_MOC_HAS_STRING_DATA
} // unnamed namespace

Q_CONSTINIT static const uint qt_meta_data_CLASSAccessControlSCOPEConsumerPhotoWidgetENDCLASS[] = {

 // content:
      12,       // revision
       0,       // classname
       0,    0, // classinfo
       4,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       1,       // signalCount

 // signals: name, argc, parameters, tag, flags, initial metatype offsets
       1,    0,   38,    2, 0x06,    1 /* Public */,

 // slots: name, argc, parameters, tag, flags, initial metatype offsets
       3,    0,   39,    2, 0x08,    2 /* Private */,
       4,    0,   40,    2, 0x08,    3 /* Private */,
       5,    0,   41,    2, 0x08,    4 /* Private */,

 // signals: parameters
    QMetaType::Void,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

Q_CONSTINIT const QMetaObject AccessControl::ConsumerPhotoWidget::staticMetaObject = { {
    QMetaObject::SuperData::link<QWidget::staticMetaObject>(),
    qt_meta_stringdata_CLASSAccessControlSCOPEConsumerPhotoWidgetENDCLASS.offsetsAndSizes,
    qt_meta_data_CLASSAccessControlSCOPEConsumerPhotoWidgetENDCLASS,
    qt_static_metacall,
    nullptr,
    qt_incomplete_metaTypeArray<qt_meta_stringdata_CLASSAccessControlSCOPEConsumerPhotoWidgetENDCLASS_t,
        // Q_OBJECT / Q_GADGET
        QtPrivate::TypeAndForceComplete<ConsumerPhotoWidget, std::true_type>,
        // method 'photoChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'uploadPhoto'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'takePhoto'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'deletePhoto'
        QtPrivate::TypeAndForceComplete<void, std::false_type>
    >,
    nullptr
} };

void AccessControl::ConsumerPhotoWidget::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<ConsumerPhotoWidget *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->photoChanged(); break;
        case 1: _t->uploadPhoto(); break;
        case 2: _t->takePhoto(); break;
        case 3: _t->deletePhoto(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (ConsumerPhotoWidget::*)();
            if (_t _q_method = &ConsumerPhotoWidget::photoChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 0;
                return;
            }
        }
    }
    (void)_a;
}

const QMetaObject *AccessControl::ConsumerPhotoWidget::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *AccessControl::ConsumerPhotoWidget::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_CLASSAccessControlSCOPEConsumerPhotoWidgetENDCLASS.stringdata0))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int AccessControl::ConsumerPhotoWidget::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 4)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 4;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 4)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 4;
    }
    return _id;
}

// SIGNAL 0
void AccessControl::ConsumerPhotoWidget::photoChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}
QT_WARNING_POP
