#ifndef CAMERADIALOG_H
#define CAMERADIALOG_H

#include <QDialog>
#include <QLabel>
#include <QPushButton>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QTimer>
#include <QCamera>
#include <QImageCapture>
#include <QMediaCaptureSession>
#include <QVideoWidget>
#include <QMediaDevices>
#include <QCameraDevice>
#include <QComboBox>
#include <QMessageBox>
#include <QCloseEvent>
#include <QByteArray>
#include <QImage>
#include <QPixmap>

namespace AccessControl {

/**
 * @brief 相机拍照对话框
 * 
 * 提供相机预览和拍照功能，支持相机设备选择
 */
class CameraDialog : public QDialog
{
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param parent 父窗口
     */
    explicit CameraDialog(QWidget *parent = nullptr);
    
    /**
     * @brief 析构函数
     */
    ~CameraDialog();

    /**
     * @brief 获取拍摄的照片数据
     * @return 照片二进制数据
     */
    QByteArray getCapturedImageData() const;

signals:
    /**
     * @brief 照片拍摄完成信号
     * @param imageData 照片二进制数据
     */
    void imageCaptured(const QByteArray &imageData);

protected:
    /**
     * @brief 对话框关闭事件
     */
    void closeEvent(QCloseEvent *event) override;

private slots:
    /**
     * @brief 拍照
     */
    void captureImage();
    
    /**
     * @brief 确认并保存照片
     */
    void acceptPhoto();
    
    /**
     * @brief 重新拍照
     */
    void retakePhoto();
    
    /**
     * @brief 取消拍照
     */
    void cancelPhoto();
    
    /**
     * @brief 切换相机设备
     */
    void switchCamera();
    
    /**
     * @brief 处理拍摄的照片
     * @param id 拍摄ID
     * @param image 照片
     */
    void onImageCaptured(int id, const QImage &image);
    
    /**
     * @brief 处理相机错误
     */
    void onCameraError();
    
    /**
     * @brief 处理拍照错误
     * @param id 拍摄ID
     * @param error 错误类型
     * @param errorString 错误描述
     */
    void onCaptureError(int id, QImageCapture::Error error, const QString &errorString);

private:
    /**
     * @brief 初始化UI
     */
    void initUI();
    
    /**
     * @brief 初始化相机
     */
    void initCamera();
    
    /**
     * @brief 启动相机
     */
    void startCamera();
    
    /**
     * @brief 停止相机
     */
    void stopCamera();
    
    /**
     * @brief 设置相机设备
     * @param cameraDevice 相机设备
     */
    void setCamera(const QCameraDevice &cameraDevice);
    
    /**
     * @brief 显示预览
     */
    void showPreview();
    
    /**
     * @brief 显示拍摄的照片
     */
    void showCapturedImage();

private:
    // UI组件
    QVBoxLayout* m_mainLayout;
    QHBoxLayout* m_topLayout;
    QHBoxLayout* m_buttonLayout;
    
    QComboBox* m_cameraCombo;
    QVideoWidget* m_viewfinder;
    QLabel* m_imageLabel;
    
    QPushButton* m_captureButton;
    QPushButton* m_acceptButton;
    QPushButton* m_retakeButton;
    QPushButton* m_cancelButton;
    
    // 相机组件
    QCamera* m_camera;
    QImageCapture* m_imageCapture;
    QMediaCaptureSession* m_captureSession;
    
    // 数据
    QByteArray m_capturedImageData;
    QImage m_capturedImage;
    QList<QCameraDevice> m_cameras;
    
    // 状态
    bool m_isImageCaptured;
};

} // namespace AccessControl

#endif // CAMERADIALOG_H 