-- 迁移文件：006_create_doors_table.sql
-- 描述：创建门表
-- 创建时间：2024-07-10
-- 作者：AI Assistant

-- ========== 创建doors表 ==========

CREATE TABLE IF NOT EXISTS doors (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL,                 -- 门名称
    controller_id INTEGER NOT NULL,             -- 所属控制器ID
    door_number INTEGER NOT NULL,               -- 门编号（在控制器上的编号）
    area_id INTEGER,                            -- 所在区域ID
    description TEXT,                           -- 说明
    status INTEGER DEFAULT 0,                   -- 门状态：0-正常关闭，1-正常打开，2-报警，3-故障
    open_timeout INTEGER DEFAULT 30,            -- 开门超时时间（秒）
    reader_in_id INTEGER,                       -- 进门读卡器ID
    reader_out_id INTEGER,                      -- 出门读卡器ID
    anti_passback_enabled INTEGER DEFAULT 0,    -- 是否启用反潜回
    interlock_group_id INTEGER DEFAULT 0,       -- 互锁组ID
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (controller_id) REFERENCES controllers(id) ON DELETE CASCADE,
    FOREIGN KEY (area_id) REFERENCES areas(id) ON DELETE SET NULL
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_doors_name ON doors(name);
CREATE INDEX IF NOT EXISTS idx_doors_controller_id ON doors(controller_id);
CREATE INDEX IF NOT EXISTS idx_doors_area_id ON doors(area_id);
CREATE INDEX IF NOT EXISTS idx_doors_status ON doors(status);

-- 创建触发器，在更新doors时自动更新updated_at字段
CREATE TRIGGER IF NOT EXISTS trigger_doors_update 
    AFTER UPDATE ON doors
    FOR EACH ROW
BEGIN
    UPDATE doors SET updated_at = CURRENT_TIMESTAMP WHERE id = OLD.id;
END; 