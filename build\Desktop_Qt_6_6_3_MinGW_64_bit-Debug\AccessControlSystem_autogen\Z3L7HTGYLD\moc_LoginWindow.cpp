/****************************************************************************
** Meta object code from reading C++ file 'LoginWindow.h'
**
** Created by: The Qt Meta Object Compiler version 68 (Qt 6.6.3)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../src/views/LoginWindow.h"
#include <QtGui/qtextcursor.h>
#include <QtCore/qmetatype.h>

#if __has_include(<QtCore/qtmochelpers.h>)
#include <QtCore/qtmochelpers.h>
#else
QT_BEGIN_MOC_NAMESPACE
#endif


#include <memory>

#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'LoginWindow.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 68
#error "This file was generated using the moc from 6.6.3. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {

#ifdef QT_MOC_HAS_STRINGDATA
struct qt_meta_stringdata_CLASSAccessControlSCOPELoginWindowENDCLASS_t {};
constexpr auto qt_meta_stringdata_CLASSAccessControlSCOPELoginWindowENDCLASS = QtMocHelpers::stringData(
    "AccessControl::LoginWindow",
    "showLogin",
    "",
    "handleLogin",
    "handleDatabaseConfig",
    "handleRememberPassword",
    "checked",
    "handleAutoLogin",
    "onUsernameChanged",
    "onPasswordChanged",
    "onLoginTimeout",
    "onFadeInFinished"
);
#else  // !QT_MOC_HAS_STRING_DATA
struct qt_meta_stringdata_CLASSAccessControlSCOPELoginWindowENDCLASS_t {
    uint offsetsAndSizes[24];
    char stringdata0[27];
    char stringdata1[10];
    char stringdata2[1];
    char stringdata3[12];
    char stringdata4[21];
    char stringdata5[23];
    char stringdata6[8];
    char stringdata7[16];
    char stringdata8[18];
    char stringdata9[18];
    char stringdata10[15];
    char stringdata11[17];
};
#define QT_MOC_LITERAL(ofs, len) \
    uint(sizeof(qt_meta_stringdata_CLASSAccessControlSCOPELoginWindowENDCLASS_t::offsetsAndSizes) + ofs), len 
Q_CONSTINIT static const qt_meta_stringdata_CLASSAccessControlSCOPELoginWindowENDCLASS_t qt_meta_stringdata_CLASSAccessControlSCOPELoginWindowENDCLASS = {
    {
        QT_MOC_LITERAL(0, 26),  // "AccessControl::LoginWindow"
        QT_MOC_LITERAL(27, 9),  // "showLogin"
        QT_MOC_LITERAL(37, 0),  // ""
        QT_MOC_LITERAL(38, 11),  // "handleLogin"
        QT_MOC_LITERAL(50, 20),  // "handleDatabaseConfig"
        QT_MOC_LITERAL(71, 22),  // "handleRememberPassword"
        QT_MOC_LITERAL(94, 7),  // "checked"
        QT_MOC_LITERAL(102, 15),  // "handleAutoLogin"
        QT_MOC_LITERAL(118, 17),  // "onUsernameChanged"
        QT_MOC_LITERAL(136, 17),  // "onPasswordChanged"
        QT_MOC_LITERAL(154, 14),  // "onLoginTimeout"
        QT_MOC_LITERAL(169, 16)   // "onFadeInFinished"
    },
    "AccessControl::LoginWindow",
    "showLogin",
    "",
    "handleLogin",
    "handleDatabaseConfig",
    "handleRememberPassword",
    "checked",
    "handleAutoLogin",
    "onUsernameChanged",
    "onPasswordChanged",
    "onLoginTimeout",
    "onFadeInFinished"
};
#undef QT_MOC_LITERAL
#endif // !QT_MOC_HAS_STRING_DATA
} // unnamed namespace

Q_CONSTINIT static const uint qt_meta_data_CLASSAccessControlSCOPELoginWindowENDCLASS[] = {

 // content:
      12,       // revision
       0,       // classname
       0,    0, // classinfo
       9,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // slots: name, argc, parameters, tag, flags, initial metatype offsets
       1,    0,   68,    2, 0x0a,    1 /* Public */,
       3,    0,   69,    2, 0x08,    2 /* Private */,
       4,    0,   70,    2, 0x08,    3 /* Private */,
       5,    1,   71,    2, 0x08,    4 /* Private */,
       7,    1,   74,    2, 0x08,    6 /* Private */,
       8,    0,   77,    2, 0x08,    8 /* Private */,
       9,    0,   78,    2, 0x08,    9 /* Private */,
      10,    0,   79,    2, 0x08,   10 /* Private */,
      11,    0,   80,    2, 0x08,   11 /* Private */,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Bool,    6,
    QMetaType::Void, QMetaType::Bool,    6,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

Q_CONSTINIT const QMetaObject AccessControl::LoginWindow::staticMetaObject = { {
    QMetaObject::SuperData::link<QDialog::staticMetaObject>(),
    qt_meta_stringdata_CLASSAccessControlSCOPELoginWindowENDCLASS.offsetsAndSizes,
    qt_meta_data_CLASSAccessControlSCOPELoginWindowENDCLASS,
    qt_static_metacall,
    nullptr,
    qt_incomplete_metaTypeArray<qt_meta_stringdata_CLASSAccessControlSCOPELoginWindowENDCLASS_t,
        // Q_OBJECT / Q_GADGET
        QtPrivate::TypeAndForceComplete<LoginWindow, std::true_type>,
        // method 'showLogin'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'handleLogin'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'handleDatabaseConfig'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'handleRememberPassword'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<bool, std::false_type>,
        // method 'handleAutoLogin'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<bool, std::false_type>,
        // method 'onUsernameChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onPasswordChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onLoginTimeout'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onFadeInFinished'
        QtPrivate::TypeAndForceComplete<void, std::false_type>
    >,
    nullptr
} };

void AccessControl::LoginWindow::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<LoginWindow *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->showLogin(); break;
        case 1: _t->handleLogin(); break;
        case 2: _t->handleDatabaseConfig(); break;
        case 3: _t->handleRememberPassword((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1]))); break;
        case 4: _t->handleAutoLogin((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1]))); break;
        case 5: _t->onUsernameChanged(); break;
        case 6: _t->onPasswordChanged(); break;
        case 7: _t->onLoginTimeout(); break;
        case 8: _t->onFadeInFinished(); break;
        default: ;
        }
    }
}

const QMetaObject *AccessControl::LoginWindow::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *AccessControl::LoginWindow::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_CLASSAccessControlSCOPELoginWindowENDCLASS.stringdata0))
        return static_cast<void*>(this);
    return QDialog::qt_metacast(_clname);
}

int AccessControl::LoginWindow::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QDialog::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 9)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 9;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 9)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 9;
    }
    return _id;
}
QT_WARNING_POP
