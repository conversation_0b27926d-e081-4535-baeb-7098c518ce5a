# 用户手册更新 - 2025年7月30日

## 概述
本文档介绍了2025年7月30日更新的新功能使用方法，帮助用户快速掌握系统的最新特性。

## 1. 表单验证功能

### 1.1 手机号输入
**使用说明**:
1. 在添加或编辑用户时，手机号字段支持格式验证
2. 输入11位中国大陆手机号（如：13812345678）
3. 系统会实时验证格式，错误时显示红色边框
4. 手机号可以为空，但如果填写必须符合格式

**支持的号段**:
- 移动：134-139, 147, 150-152, 157-159, 178, 182-184, 187-188, 198
- 联通：130-132, 145, 155-156, 166, 175-176, 185-186
- 电信：133, 149, 153, 173-174, 177, 180-181, 189, 199

### 1.2 身份证号输入
**使用说明**:
1. 输入18位身份证号码
2. 系统会验证格式和校验位的正确性
3. 支持X作为校验位（大小写均可）
4. 验证失败时会显示具体错误信息

**注意事项**:
- 必须是18位标准身份证号
- 系统会自动验证校验位算法
- 输入错误时会有明确的提示信息

## 2. 查询功能使用

### 2.1 工号/卡号查询
**使用方法**:
1. 在用户管理界面顶部找到"工号/卡号"输入框
2. 输入要查询的工号或卡号（支持部分匹配）
3. 系统会自动筛选显示匹配的用户
4. 支持USB读卡器直接刷卡查询

**查询技巧**:
- 支持模糊查询，输入部分内容即可
- 可以输入工号或任意卡号进行查询
- 清空输入框可显示所有用户

### 2.2 全局搜索功能
**启动方式**:
- 点击用户管理界面的"查找"按钮
- 使用快捷键 Ctrl+F

**使用方法**:
1. 在搜索框中输入关键词
2. 系统会在所有字段中搜索匹配内容
3. 双击搜索结果可自动定位到主界面对应用户
4. 搜索窗口可以保持打开，不影响其他操作

**搜索范围**:
- 工号、姓名、手机号
- 身份证号、卡号
- 支持中文和数字搜索

## 3. USB读卡器使用

### 3.1 刷卡操作
**使用说明**:
1. 将USB读卡器连接到电脑
2. 在任意卡号输入框中刷卡
3. 系统会自动识别并显示卡号
4. 多次刷卡时，只保留最后一次的卡号

**适用场景**:
- 添加用户时的卡号输入
- 用户管理界面的卡号查询
- 卡片管理对话框的卡号输入

**注意事项**:
- 支持各种USB读卡器设备
- 自动处理换行符和特殊字符
- 多次刷卡会自动清空前一次内容

### 3.2 故障排除
**常见问题**:
1. **刷卡无反应**: 检查读卡器连接，确保输入框有焦点
2. **卡号显示不完整**: 等待刷卡完成，系统会自动处理
3. **多次刷卡累加**: 新版本已修复，会自动清空前一次内容

## 4. 隐私保护功能

### 4.1 身份证号显示
**显示规则**:
- 界面显示：`123456********1234`（前6位+8个星号+后4位）
- 导出文件：显示完整身份证号
- 数据库存储：保存完整信息

**查看完整信息**:
- 编辑用户时可查看完整身份证号
- 导出数据时包含完整信息
- 系统管理员可配置显示权限

### 4.2 数据安全
**保护措施**:
- 敏感信息脱敏显示
- 完整数据安全存储
- 操作权限控制

## 5. 数据导入导出

### 5.1 导出用户数据
**操作步骤**:
1. 在用户管理界面点击"导出"按钮
2. 选择保存位置和文件名
3. 系统会导出当前筛选的用户数据
4. 文件格式为CSV，可用Excel打开

**导出内容**:
- 用户基本信息（工号、姓名、卡号等）
- 权限设置（考勤、倒班、门禁）
- 时间信息（起始日期、截止日期）
- 联系信息（部门、手机号、身份证号）

**文件命名**:
- 自动生成：`用户数据_20250730_143022.xlsx`
- 包含导出日期和时间

### 5.2 导入用户数据
**支持格式**:
- CSV文件（推荐）
- Excel文件（.xlsx）

**文件格式要求**:
```
工号,姓名,卡号,考勤,倒班,门禁,起始日期,截止日期,部门,手机号,身份证号
001,张三,12345678,是,否,是,2025-01-01,2025-12-31,技术部,13812345678,123456789012345678
```

**操作步骤**:
1. 准备符合格式的数据文件
2. 点击"导入"按钮选择文件
3. 系统会逐行处理数据
4. 显示导入结果和错误信息

**注意事项**:
- 第一行必须是表头
- 至少包含工号、姓名、卡号三列
- 手机号和身份证号会进行格式验证
- 重复工号会导致导入失败

### 5.3 导入结果处理
**结果显示**:
```
导入完成！
成功：15 条
失败：2 条

错误详情：
第3行：身份证号格式不正确
第7行：工号已存在
```

**处理建议**:
- 根据错误信息修正数据文件
- 重新导入失败的记录
- 检查数据格式和完整性

## 6. 界面操作优化

### 6.1 查询区域布局
**新的布局**:
- 输入框宽度优化，更适合内容长度
- 控件对齐方式改进，视觉效果更好
- 查询按钮固定宽度，操作更便捷

### 6.2 部门显示
**层级显示**:
- 格式：`总公司\技术部\开发组\前端组`
- 使用反斜杠分隔各级部门
- 完整显示部门层级关系

### 6.3 搜索对话框
**窗口特点**:
- 尺寸：800×300像素
- 非模态设计，不阻塞主界面
- 支持拖拽移动和调整位置

## 7. 快捷键说明

### 7.1 全局快捷键
- **Ctrl+F**: 打开全局搜索对话框
- **Ctrl+N**: 添加新用户（如果支持）
- **Ctrl+S**: 保存当前编辑（在对话框中）

### 7.2 搜索快捷键
- **Enter**: 执行搜索
- **Esc**: 关闭搜索对话框
- **双击结果**: 定位到主界面对应用户

## 8. 常见问题解答

### 8.1 验证相关
**Q: 为什么手机号输入后显示红色边框？**
A: 手机号格式不正确，请输入11位中国大陆手机号。

**Q: 身份证号验证失败怎么办？**
A: 检查是否为18位标准身份证号，确保校验位正确。

### 8.2 查询相关
**Q: 为什么查询不到用户？**
A: 检查输入的工号或卡号是否正确，支持部分匹配查询。

**Q: 全局搜索找不到结果？**
A: 确认关键词拼写正确，搜索支持模糊匹配。

### 8.3 读卡器相关
**Q: USB读卡器刷卡无反应？**
A: 检查设备连接，确保输入框有焦点，尝试重新刷卡。

**Q: 刷卡后显示乱码？**
A: 系统会自动处理，等待刷卡完成后会显示正确卡号。

### 8.4 导入导出相关
**Q: 导出的文件用Excel打开乱码？**
A: 文件使用UTF-8编码，用Excel打开时选择UTF-8编码。

**Q: 导入时提示格式错误？**
A: 检查文件格式，确保第一行是表头，数据格式符合要求。

## 9. 最佳实践建议

### 9.1 数据录入
- 使用USB读卡器可提高录入效率
- 定期验证数据完整性
- 及时备份重要数据

### 9.2 查询操作
- 使用全局搜索可快速定位用户
- 合理使用筛选条件提高查询效率
- 善用快捷键提升操作速度

### 9.3 数据管理
- 定期导出数据进行备份
- 批量导入前先验证数据格式
- 注意保护用户隐私信息

---

**文档版本**: 1.0  
**更新日期**: 2025年7月30日  
**适用系统**: 门禁控制系统 v2.0+
