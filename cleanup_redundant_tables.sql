-- 数据库表清理脚本
-- 目的：清理冗余的用户相关表，避免数据混乱
-- 创建时间：2025-01-26
-- 警告：执行前请备份数据库！

-- ========== 备份重要数据 ==========

-- 首先确认consumers表中有正确的数据
SELECT COUNT(*) as consumer_count FROM consumers;

-- 确认consumer_cards表中有正确的数据  
SELECT COUNT(*) as consumer_cards_count FROM consumer_cards;

-- 确认operators表中有正确的数据
SELECT COUNT(*) as operators_count FROM operators;

-- ========== 检查是否存在重复数据 ==========

-- 检查consumers表和users表是否有重复数据
SELECT 
    'consumers vs users duplicate check' as check_type,
    COUNT(*) as duplicate_count 
FROM consumers c 
INNER JOIN users u ON c.work_number = u.work_number;

-- ========== 删除冗余表 ==========

-- 1. 删除旧的用户扩展表（如果存在）
DROP TABLE IF EXISTS user_profiles;

-- 2. 删除旧的用户卡片表（如果consumer_cards表存在且有数据）
-- 注意：只有当consumer_cards表存在且有数据时才删除user_cards
-- 检查consumer_cards表是否存在且有数据
SELECT CASE 
    WHEN EXISTS (SELECT 1 FROM sqlite_master WHERE type='table' AND name='consumer_cards') 
         AND EXISTS (SELECT 1 FROM consumer_cards LIMIT 1)
    THEN '可以安全删除user_cards表'
    ELSE '不建议删除user_cards表，consumer_cards表可能为空'
END as safety_check;

-- 如果上述检查结果是"可以安全删除"，则执行下面的命令：
-- DROP TABLE IF EXISTS user_cards_old;
-- DROP TABLE IF EXISTS user_cards;

-- 3. 清理users表中已迁移到consumers的数据
-- 注意：只删除role > 1的用户（门禁用户），保留管理员账户
-- DELETE FROM users WHERE role > 1 AND work_number IN (SELECT work_number FROM consumers);

-- ========== 创建视图用于向后兼容（可选） ==========

-- 如果某些代码仍然引用users表，可以创建一个视图
-- CREATE VIEW users_view AS 
-- SELECT 
--     id, 
--     work_number as username,
--     'migrated' as password_hash,
--     'salt' as salt,
--     2 as role,  -- 操作员角色
--     status,
--     real_name,
--     '' as email,
--     phone_number,
--     id_number,
--     access_enabled,
--     attendance_enabled,
--     valid_from,
--     valid_until,
--     department_id,
--     work_number,
--     shift_work,
--     null as last_login_at,
--     '' as last_login_ip,
--     0 as login_attempts,
--     created_at,
--     updated_at
-- FROM consumers;

-- ========== 验证清理结果 ==========

-- 验证核心表的数据完整性
SELECT 'consumers' as table_name, COUNT(*) as record_count FROM consumers
UNION ALL
SELECT 'consumer_cards', COUNT(*) FROM consumer_cards
UNION ALL  
SELECT 'operators', COUNT(*) FROM operators
UNION ALL
SELECT 'departments', COUNT(*) FROM departments
UNION ALL
SELECT 'areas', COUNT(*) FROM areas;

-- 检查是否还有引用不存在表的外键
-- SELECT name FROM sqlite_master WHERE type='table' AND sql LIKE '%user_cards%';

PRAGMA foreign_key_check; 