-- 迁移文件：010_create_operators_table.sql
-- 描述：创建操作员表（简化版本）
-- 创建时间：2024-12-25
-- 作者：AI Assistant

-- ========== 创建operators表 ==========
CREATE TABLE IF NOT EXISTS operators (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    salt VARCHAR(50) NOT NULL,
    role INTEGER NOT NULL DEFAULT 2,
    status INTEGER NOT NULL DEFAULT 0,
    real_name VARCHAR(100),
    email VARCHAR(100),
    phone_number VARCHAR(20),
    department_id INTEGER DEFAULT -1,
    last_login_at TIMESTAMP,
    last_login_ip VARCHAR(45),
    login_attempts INTEGER DEFAULT 0,
    work_number VARCHAR(50),
    id_number VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_operators_username ON operators(username);
CREATE INDEX IF NOT EXISTS idx_operators_status ON operators(status);
CREATE INDEX IF NOT EXISTS idx_operators_role ON operators(role);

-- 插入默认管理员账户
-- 密码是 'admin' + 'salt' 的SHA256哈希：f9a81477552594c79f2abc3fc099daa896a6e3a3590a55ffa392b6000412e80b
-- status=0 表示Active状态
INSERT OR IGNORE INTO operators (id, username, password_hash, salt, role, status, real_name)
VALUES (1, 'admin', 'f9a81477552594c79f2abc3fc099daa896a6e3a3590a55ffa392b6000412e80b', 'salt', 0, 0, '系统管理员');

-- 插入测试操作员账户  
-- 密码是 'test' + 'salt' 的SHA256哈希
-- status=0 表示Active状态
INSERT OR IGNORE INTO operators (id, username, password_hash, salt, role, status, real_name)
VALUES (2, 'test', '5a99c38c5d16658b7c9f9de4c3f34f0ab3f73b3ba4e62bb4dd6b1e6d0b7c9b8f', 'salt', 2, 0, '测试操作员'); 