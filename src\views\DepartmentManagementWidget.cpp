#include "DepartmentManagementWidget.h"
#include <QDebug>
#include <QApplication>
#include <QStyle>
#include <QMessageBox>
#include <QHBoxLayout>
#include <QVBoxLayout>
#include <QGridLayout>
#include <QSplitter>
#include <QTreeWidget>
#include <QTreeWidgetItem>
#include <QLineEdit>
#include <QPushButton>
#include <QComboBox>
#include <QTextEdit>
#include <QSpinBox>
#include <QLabel>
#include <QGroupBox>
#include <QMenu>
#include <QAction>
#include <QHeaderView>
#include <QAbstractItemView>
#include <QFileDialog>
#include <QStandardPaths>
#include <QDir>
#include <QProcess>
#include <QProgressDialog>
#include <QTextStream>
#include <QFile>
#include <QFileInfo>
#include <QStringConverter>

namespace AccessControl {

DepartmentManagementWidget::DepartmentManagementWidget(std::shared_ptr<IDatabaseProvider> dbProvider, 
                                                     QWidget *parent)
    : QWidget(parent)
    , m_dbProvider(dbProvider)
    , m_isEditMode(false)
{
    // 初始化数据访问对象
    m_departmentDao = std::make_unique<DepartmentDao>(m_dbProvider);
    
    // 初始化UI
    initializeUI();
    
    // 连接信号槽
    connectSignals();
    
    // 加载数据
    refreshDepartments();
    
    // 设置窗口属性
    setWindowTitle("部门管理");
    resize(1000, 700);
}

DepartmentManagementWidget::~DepartmentManagementWidget()
{
}

void DepartmentManagementWidget::refreshDepartments()
{
    loadDepartmentTree();
    
    // 更新父部门下拉框
    m_comboParent->clear();
    m_comboParent->addItem("无（根部门）", 0);
    
    QList<Department> departments = m_departmentDao->findActive();
    for (const Department& dept : departments) {
        m_comboParent->addItem(dept.fullPath().isEmpty() ? dept.name() : dept.fullPath(), dept.id());
    }
}

// ========== 私有方法：UI初始化 ==========

void DepartmentManagementWidget::initializeUI()
{
    // 主布局
    m_mainLayout = new QHBoxLayout(this);
    m_mainLayout->setContentsMargins(10, 10, 10, 10);
    m_mainLayout->setSpacing(10);
    
    // 创建分割器
    m_splitter = new QSplitter(Qt::Horizontal);
    m_mainLayout->addWidget(m_splitter);
    
    // 初始化左右面板
    initializeLeftPanel();
    initializeRightPanel();
    
    // 设置分割器比例
    m_splitter->setSizes({400, 600});
    m_splitter->setStretchFactor(0, 0);
    m_splitter->setStretchFactor(1, 1);
}

void DepartmentManagementWidget::initializeLeftPanel()
{
    m_leftPanel = new QWidget();
    m_leftLayout = new QVBoxLayout(m_leftPanel);
    m_leftLayout->setContentsMargins(0, 0, 0, 0);
    m_leftLayout->setSpacing(5);
    
    // 初始化工具栏
    initializeToolbar();
    
    // 搜索区域
    m_searchWidget = new QWidget();
    m_searchLayout = new QHBoxLayout(m_searchWidget);
    m_searchLayout->setContentsMargins(0, 0, 0, 0);
    
    m_searchEdit = new QLineEdit();
    m_searchEdit->setPlaceholderText("搜索部门名称或代码...");
    m_btnSearch = new QPushButton("搜索");
    m_btnClearSearch = new QPushButton("清空");
    m_btnRefresh = new QPushButton("刷新");
    
    // 设置按钮图标
    m_btnSearch->setIcon(style()->standardIcon(QStyle::SP_FileDialogDetailedView));
    m_btnClearSearch->setIcon(style()->standardIcon(QStyle::SP_DialogResetButton));
    m_btnRefresh->setIcon(style()->standardIcon(QStyle::SP_BrowserReload));
    
    m_searchLayout->addWidget(m_searchEdit);
    m_searchLayout->addWidget(m_btnSearch);
    m_searchLayout->addWidget(m_btnClearSearch);
    m_searchLayout->addWidget(m_btnRefresh);
    
    // 树形视图
    m_treeWidget = new QTreeWidget();
    m_treeWidget->setHeaderLabels({"部门", "代码", "状态"});
    m_treeWidget->setRootIsDecorated(true);
    m_treeWidget->setAlternatingRowColors(true);
    m_treeWidget->setSelectionMode(QAbstractItemView::SingleSelection);
    m_treeWidget->setContextMenuPolicy(Qt::CustomContextMenu);
    
    // 设置列宽
    m_treeWidget->header()->setSectionResizeMode(QHeaderView::Interactive);
    m_treeWidget->header()->resizeSection(0, 180); // 部门
    m_treeWidget->header()->resizeSection(1, 120); // 代码
    m_treeWidget->header()->resizeSection(2, 80);  // 状态
    m_treeWidget->header()->setStretchLastSection(true);
    
    // 添加到布局
    m_leftLayout->addWidget(m_toolbar);
    m_leftLayout->addWidget(m_searchWidget);
    m_leftLayout->addWidget(m_treeWidget);
    
    m_splitter->addWidget(m_leftPanel);
}

void DepartmentManagementWidget::initializeToolbar()
{
    m_toolbar = new QWidget();
    m_toolbarLayout = new QHBoxLayout(m_toolbar);
    m_toolbarLayout->setContentsMargins(0, 0, 0, 0);
    
    // 创建工具按钮
    m_btnAdd = new QPushButton("添加部门");
    m_btnAddSub = new QPushButton("添加子部门");
    m_btnEdit = new QPushButton("编辑");
    m_btnDelete = new QPushButton("删除");
    m_btnExpandAll = new QPushButton("展开全部");
    m_btnCollapseAll = new QPushButton("收起全部");
    m_btnImport = new QPushButton("导入");
    m_btnExport = new QPushButton("导出");
    
    // 设置图标（使用系统标准图标）
    m_btnAdd->setIcon(style()->standardIcon(QStyle::SP_FileIcon));
    m_btnAddSub->setIcon(style()->standardIcon(QStyle::SP_DirIcon));
    m_btnEdit->setIcon(style()->standardIcon(QStyle::SP_FileDialogDetailedView));
    m_btnDelete->setIcon(style()->standardIcon(QStyle::SP_TrashIcon));
    
    // 设置按钮状态
    m_btnAddSub->setEnabled(false);
    m_btnEdit->setEnabled(false);
    m_btnDelete->setEnabled(false);
    
    // 添加到布局
    m_toolbarLayout->addWidget(m_btnAdd);
    m_toolbarLayout->addWidget(m_btnAddSub);
    m_toolbarLayout->addWidget(m_btnEdit);
    m_toolbarLayout->addWidget(m_btnDelete);
    m_toolbarLayout->addStretch();
    m_toolbarLayout->addWidget(m_btnExpandAll);
    m_toolbarLayout->addWidget(m_btnCollapseAll);
    m_toolbarLayout->addStretch();
    m_toolbarLayout->addWidget(m_btnImport);
    m_toolbarLayout->addWidget(m_btnExport);
}

void DepartmentManagementWidget::initializeRightPanel()
{
    m_rightPanel = new QWidget();
    m_rightLayout = new QVBoxLayout(m_rightPanel);
    m_rightLayout->setContentsMargins(0, 0, 0, 0);
    m_rightLayout->setSpacing(10);
    
    // 详细信息表单
    m_detailsGroup = new QGroupBox("部门信息");
    m_detailsLayout = new QGridLayout(m_detailsGroup);
    
    // 创建表单控件
    m_lblName = new QLabel("部门名称 *:");
    m_editName = new QLineEdit();
    m_editName->setMaxLength(100);
    m_editName->setPlaceholderText("请输入部门名称（1-100个字符，支持中英文）");
    
    m_lblCode = new QLabel("部门代码 *:");
    m_editCode = new QLineEdit();
    m_editCode->setMaxLength(20);
    m_editCode->setPlaceholderText("请输入部门代码（1-20个字符，建议英文大写）");
    
    m_lblParent = new QLabel("上级部门:");
    m_comboParent = new QComboBox();
    
    m_lblDescription = new QLabel("部门描述:");
    m_editDescription = new QTextEdit();
    m_editDescription->setMaximumHeight(80);
    m_editDescription->setPlaceholderText("请输入部门描述（可选，不超过500个字符）");
    
    m_lblStatus = new QLabel("状态:");
    m_comboStatus = new QComboBox();
    m_comboStatus->addItem("启用", static_cast<int>(Department::Active));
    m_comboStatus->addItem("禁用", static_cast<int>(Department::Inactive));
    
    m_lblSortOrder = new QLabel("排序:");
    m_spinSortOrder = new QSpinBox();
    m_spinSortOrder->setRange(0, 9999);
    m_spinSortOrder->setSuffix(" (数字越小排序越靠前)");
    
    // 添加到表单布局
    m_detailsLayout->addWidget(m_lblName, 0, 0);
    m_detailsLayout->addWidget(m_editName, 0, 1);
    m_detailsLayout->addWidget(m_lblCode, 0, 2);
    m_detailsLayout->addWidget(m_editCode, 0, 3);
    
    m_detailsLayout->addWidget(m_lblParent, 1, 0);
    m_detailsLayout->addWidget(m_comboParent, 1, 1, 1, 3);
    
    m_detailsLayout->addWidget(m_lblStatus, 2, 0);
    m_detailsLayout->addWidget(m_comboStatus, 2, 1);
    m_detailsLayout->addWidget(m_lblSortOrder, 2, 2);
    m_detailsLayout->addWidget(m_spinSortOrder, 2, 3);
    
    m_detailsLayout->addWidget(m_lblDescription, 3, 0);
    m_detailsLayout->addWidget(m_editDescription, 3, 1, 1, 3);
    
    // 操作按钮
    m_buttonWidget = new QWidget();
    m_buttonLayout = new QHBoxLayout(m_buttonWidget);
    m_buttonLayout->setContentsMargins(0, 0, 0, 0);
    
    m_btnSave = new QPushButton("保存");
    m_btnCancel = new QPushButton("取消");
    m_btnSave->setIcon(style()->standardIcon(QStyle::SP_DialogApplyButton));
    m_btnCancel->setIcon(style()->standardIcon(QStyle::SP_DialogCancelButton));
    
    m_buttonLayout->addStretch();
    m_buttonLayout->addWidget(m_btnSave);
    m_buttonLayout->addWidget(m_btnCancel);
    
    // 统计信息
    m_statsGroup = new QGroupBox("统计信息");
    m_statsLayout = new QGridLayout(m_statsGroup);
    
    m_lblChildCount = new QLabel("子部门数量: 0");
    m_lblDirectUserCount = new QLabel("直接人员: 0");
    m_lblTotalUserCount = new QLabel("总人员: 0");
    m_lblCreatedAt = new QLabel("创建时间: -");
    m_lblUpdatedAt = new QLabel("更新时间: -");
    
    m_statsLayout->addWidget(m_lblChildCount, 0, 0);
    m_statsLayout->addWidget(m_lblDirectUserCount, 0, 1);
    m_statsLayout->addWidget(m_lblTotalUserCount, 0, 2);
    m_statsLayout->addWidget(m_lblCreatedAt, 1, 0, 1, 2);
    m_statsLayout->addWidget(m_lblUpdatedAt, 1, 2);
    
    // 添加使用说明
    QLabel* helpLabel = new QLabel("📌 填写说明：\n"
                                   "• 部门名称：必填，支持中英文，最多100个字符\n"
                                   "• 部门代码：必填，建议使用英文大写字母，最多20个字符\n"
                                   "• 上级部门：选择该部门的上级部门，根部门请选择\"无\"\n"
                                   "• 排序：数字越小排序越靠前，相同排序按名称排序");
    helpLabel->setStyleSheet("QLabel { color: #666; font-size: 12px; padding: 10px; background-color: #f5f5f5; border-radius: 4px; }");
    helpLabel->setWordWrap(true);
    
    // 添加到右侧布局
    m_rightLayout->addWidget(m_detailsGroup);
    m_rightLayout->addWidget(m_buttonWidget);
    m_rightLayout->addWidget(m_statsGroup);
    m_rightLayout->addWidget(helpLabel);
    m_rightLayout->addStretch();
    
    m_splitter->addWidget(m_rightPanel);
    
    // 初始状态设置为只读
    setFormEditMode(false);
}

void DepartmentManagementWidget::initializeContextMenu()
{
    m_contextMenu = new QMenu(this);
    
    m_actionAdd = new QAction("添加部门", this);
    m_actionAddSub = new QAction("添加子部门", this);
    m_actionEdit = new QAction("编辑部门", this);
    m_actionDelete = new QAction("删除部门", this);
    m_actionRefresh = new QAction("刷新", this);
    
    m_actionAdd->setIcon(style()->standardIcon(QStyle::SP_FileIcon));
    m_actionAddSub->setIcon(style()->standardIcon(QStyle::SP_DirIcon));
    m_actionEdit->setIcon(style()->standardIcon(QStyle::SP_FileDialogDetailedView));
    m_actionDelete->setIcon(style()->standardIcon(QStyle::SP_TrashIcon));
    m_actionRefresh->setIcon(style()->standardIcon(QStyle::SP_BrowserReload));
    
    m_contextMenu->addAction(m_actionAdd);
    m_contextMenu->addAction(m_actionAddSub);
    m_contextMenu->addSeparator();
    m_contextMenu->addAction(m_actionEdit);
    m_contextMenu->addAction(m_actionDelete);
    m_contextMenu->addSeparator();
    m_contextMenu->addAction(m_actionRefresh);
}

void DepartmentManagementWidget::connectSignals()
{
    // 树形视图信号
    connect(m_treeWidget, &QTreeWidget::itemSelectionChanged,
            this, &DepartmentManagementWidget::onTreeSelectionChanged);
    connect(m_treeWidget, &QTreeWidget::customContextMenuRequested,
            this, &DepartmentManagementWidget::onShowContextMenu);
    
    // 工具栏按钮信号
    connect(m_btnAdd, &QPushButton::clicked, this, &DepartmentManagementWidget::onAddDepartment);
    connect(m_btnAddSub, &QPushButton::clicked, this, &DepartmentManagementWidget::onAddSubDepartment);
    connect(m_btnEdit, &QPushButton::clicked, this, &DepartmentManagementWidget::onEditDepartment);
    connect(m_btnDelete, &QPushButton::clicked, this, &DepartmentManagementWidget::onDeleteDepartment);
    connect(m_btnExpandAll, &QPushButton::clicked, m_treeWidget, &QTreeWidget::expandAll);
    connect(m_btnCollapseAll, &QPushButton::clicked, m_treeWidget, &QTreeWidget::collapseAll);
    connect(m_btnImport, &QPushButton::clicked, this, &DepartmentManagementWidget::onImportDepartments);
    connect(m_btnExport, &QPushButton::clicked, this, &DepartmentManagementWidget::onExportDepartments);
    
    // 搜索信号
    connect(m_btnSearch, &QPushButton::clicked, this, &DepartmentManagementWidget::onSearchDepartments);
    connect(m_btnClearSearch, &QPushButton::clicked, this, &DepartmentManagementWidget::onClearSearch);
    connect(m_btnRefresh, &QPushButton::clicked, this, &DepartmentManagementWidget::refreshDepartments);
    connect(m_searchEdit, &QLineEdit::returnPressed, this, &DepartmentManagementWidget::onSearchDepartments);
    
    // 表单按钮信号
    connect(m_btnSave, &QPushButton::clicked, this, &DepartmentManagementWidget::onSaveDepartment);
    connect(m_btnCancel, &QPushButton::clicked, this, &DepartmentManagementWidget::onCancelEdit);
    
    // 右键菜单信号
    initializeContextMenu();
    connect(m_actionAdd, &QAction::triggered, this, &DepartmentManagementWidget::onAddDepartment);
    connect(m_actionAddSub, &QAction::triggered, this, &DepartmentManagementWidget::onAddSubDepartment);
    connect(m_actionEdit, &QAction::triggered, this, &DepartmentManagementWidget::onEditDepartment);
    connect(m_actionDelete, &QAction::triggered, this, &DepartmentManagementWidget::onDeleteDepartment);
    connect(m_actionRefresh, &QAction::triggered, this, &DepartmentManagementWidget::refreshDepartments);
}

// ========== 槽函数实现 ==========

void DepartmentManagementWidget::onTreeSelectionChanged()
{
    QTreeWidgetItem* item = m_treeWidget->currentItem();
    bool hasSelection = (item != nullptr);
    
    // 更新按钮状态
    m_btnAddSub->setEnabled(hasSelection);
    m_btnEdit->setEnabled(hasSelection);
    m_btnDelete->setEnabled(hasSelection);
    
    if (hasSelection) {
        int departmentId = item->data(0, Qt::UserRole).toInt();
        Department department = m_departmentDao->findById(departmentId);
        
        if (department.id() > 0) {
            m_currentDepartment = department;
            showDepartmentDetails(department);
            updateDepartmentStats(departmentId);
        }
    } else {
        m_currentDepartment = Department();
        clearDetailsForm();
    }
}

void DepartmentManagementWidget::onAddDepartment()
{
    m_currentDepartment = Department();
    // 不强制设置parentId，让用户自由选择
    m_isEditMode = true;
    
    // 清空表单但保持ComboBox的当前选择
    clearDetailsFormForAdd();
    setFormEditMode(true);
    
    m_editName->setFocus();
}

void DepartmentManagementWidget::onAddSubDepartment()
{
    QTreeWidgetItem* item = m_treeWidget->currentItem();
    if (!item) return;
    
    int parentId = item->data(0, Qt::UserRole).toInt();
    
    m_currentDepartment = Department();
    m_currentDepartment.setParentId(parentId);
    m_isEditMode = true;
    
    clearDetailsForm();
    setFormEditMode(true);
    
    // 设置父部门
    for (int i = 0; i < m_comboParent->count(); ++i) {
        if (m_comboParent->itemData(i).toInt() == parentId) {
            m_comboParent->setCurrentIndex(i);
            break;
        }
    }
    
    m_editName->setFocus();
}

void DepartmentManagementWidget::onEditDepartment()
{
    if (m_currentDepartment.id() <= 0) return;
    
    m_isEditMode = true;
    setFormEditMode(true);
    m_editName->setFocus();
}

void DepartmentManagementWidget::onDeleteDepartment()
{
    if (m_currentDepartment.id() <= 0) return;
    
    // 检查是否可以删除
    if (!m_departmentDao->canDelete(m_currentDepartment.id())) {
        QMessageBox::warning(this, "删除失败", 
                           "该部门包含子部门或关联用户，无法删除！\n请先处理子部门和关联用户。");
        return;
    }
    
    // 确认删除
    int ret = QMessageBox::question(this, "确认删除", 
                                   QString("确定要删除部门 \"%1\" 吗？\n此操作不可撤销！")
                                   .arg(m_currentDepartment.name()),
                                   QMessageBox::Yes | QMessageBox::No,
                                   QMessageBox::No);
    
    if (ret == QMessageBox::Yes) {
        if (m_departmentDao->deleteDepartment(m_currentDepartment.id())) {
            QMessageBox::information(this, "删除成功", "部门删除成功！");
            refreshDepartments();
            clearDetailsForm();
        } else {
            QMessageBox::critical(this, "删除失败", "删除部门时发生错误！");
        }
    }
}

void DepartmentManagementWidget::onSaveDepartment()
{
    // 验证表单数据
    auto validation = validateForm();
    if (!validation.first) {
        QMessageBox::warning(this, "输入错误", validation.second);
        return;
    }
    
    // 从表单获取部门对象
    Department department = getDepartmentFromForm();
    
    // 添加调试信息
    qDebug() << "DepartmentManagementWidget: onSaveDepartment()";
    qDebug() << "  - Department Name:" << department.name();
    qDebug() << "  - Department Code:" << department.code();
    qDebug() << "  - Department Parent ID:" << department.parentId();
    qDebug() << "  - Department Sort Order:" << department.sortOrder();
    qDebug() << "  - Department Status:" << static_cast<int>(department.status());
    qDebug() << "  - Is Edit Mode:" << (m_currentDepartment.id() > 0);
    
    bool success = false;
    if (m_currentDepartment.id() <= 0) {
        // 新增部门
        qDebug() << "  - Creating new department...";
        qDebug() << "  - Before createDepartment: Parent ID =" << department.parentId() << ", Sort Order =" << department.sortOrder();
        
        int departmentId = m_departmentDao->createDepartment(department);
        success = (departmentId > 0);
        if (success) {
            m_currentDepartment = department;
            m_currentDepartment.setId(departmentId);
            qDebug() << "  - New department created with ID:" << departmentId;
            qDebug() << "  - After creation: m_currentDepartment Parent ID =" << m_currentDepartment.parentId() << ", Sort Order =" << m_currentDepartment.sortOrder();
        } else {
            qDebug() << "  - Failed to create department";
        }
    } else {
        // 更新部门
        qDebug() << "  - Updating existing department with ID:" << m_currentDepartment.id();
        qDebug() << "  - Before updateDepartment: Parent ID =" << department.parentId() << ", Sort Order =" << department.sortOrder();
        
        department.setId(m_currentDepartment.id());
        department.setCreatedAt(m_currentDepartment.createdAt());
        success = m_departmentDao->updateDepartment(department);
        if (success) {
            m_currentDepartment = department;
            qDebug() << "  - Department updated successfully";
            qDebug() << "  - After update: m_currentDepartment Parent ID =" << m_currentDepartment.parentId() << ", Sort Order =" << m_currentDepartment.sortOrder();
        } else {
            qDebug() << "  - Failed to update department";
        }
    }
    
    if (success) {
        QMessageBox::information(this, "保存成功", "部门信息保存成功！");
        setFormEditMode(false);
        m_isEditMode = false;
        
        // 保存当前部门ID，用于重新选择
        int savedDepartmentId = m_currentDepartment.id();
        
        refreshDepartments();
        
        // 重新选中刚保存的部门并刷新显示
        QTreeWidgetItem* item = findTreeItem(savedDepartmentId);
        if (item) {
            // 临时阻止信号，避免递归调用
            bool blocked = m_treeWidget->blockSignals(true);
            m_treeWidget->setCurrentItem(item);
            m_treeWidget->blockSignals(blocked);
            
            // 从数据库重新加载最新的部门信息
            qDebug() << "  - Reloading department from database with ID:" << savedDepartmentId;
            Department updatedDepartment = m_departmentDao->findById(savedDepartmentId);
            if (updatedDepartment.id() > 0) {
                qDebug() << "  - Reloaded from DB: Parent ID =" << updatedDepartment.parentId() << ", Sort Order =" << updatedDepartment.sortOrder();
                m_currentDepartment = updatedDepartment;
                showDepartmentDetails(updatedDepartment);
                updateDepartmentStats(savedDepartmentId);
            } else {
                qDebug() << "  - Failed to reload department from database";
            }
        } else {
            qDebug() << "  - Could not find tree item for department ID:" << savedDepartmentId;
        }
    } else {
        QMessageBox::critical(this, "保存失败", "保存部门信息时发生错误！");
    }
}

void DepartmentManagementWidget::onCancelEdit()
{
    setFormEditMode(false);
    m_isEditMode = false;
    
    if (m_currentDepartment.id() > 0) {
        showDepartmentDetails(m_currentDepartment);
    } else {
        clearDetailsForm();
    }
}

void DepartmentManagementWidget::onSearchDepartments()
{
    QString keyword = m_searchEdit->text().trimmed();
    applySearchFilter(keyword);
}

void DepartmentManagementWidget::onClearSearch()
{
    m_searchEdit->clear();
    // 重新加载数据而不是仅仅显示所有缓存的项目
    refreshDepartments();
}

void DepartmentManagementWidget::onExpandAll()
{
    m_treeWidget->expandAll();
}

void DepartmentManagementWidget::onCollapseAll()
{
    m_treeWidget->collapseAll();
}

void DepartmentManagementWidget::onImportDepartments()
{
    // 显示文件选择对话框
    QString fileName = QFileDialog::getOpenFileName(
        this,
        "导入部门数据",
        QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation),
        "CSV 文件 (*.csv);;所有文件 (*.*)"
    );
    
    if (fileName.isEmpty()) {
        return;
    }
    
    // 创建进度对话框
    QProgressDialog progressDialog("正在导入部门数据...", "取消", 0, 0, this);
    progressDialog.setWindowModality(Qt::WindowModal);
    progressDialog.setAutoClose(true);
    progressDialog.setAutoReset(true);
    progressDialog.show();
    QApplication::processEvents();
    
    // 执行导入
    auto result = importDepartmentsFromCSV(fileName);
    
    progressDialog.close();
    
    if (result.first) {
        QMessageBox::information(this, "导入成功", result.second);
        refreshDepartments();
    } else {
        QMessageBox::critical(this, "导入失败", result.second);
    }
}

void DepartmentManagementWidget::onExportDepartments()
{
    // 显示保存文件对话框
    QString fileName = QFileDialog::getSaveFileName(
        this,
        "导出部门数据",
        QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation) + "/departments.csv",
        "CSV 文件 (*.csv);;所有文件 (*.*)"
    );
    
    if (fileName.isEmpty()) {
        return;
    }
    
    // 创建进度对话框
    QProgressDialog progressDialog("正在导出部门数据...", "取消", 0, 0, this);
    progressDialog.setWindowModality(Qt::WindowModal);
    progressDialog.setAutoClose(true);
    progressDialog.setAutoReset(true);
    progressDialog.show();
    QApplication::processEvents();
    
    // 执行导出
    bool success = exportDepartmentsToCSV(fileName);
    
    progressDialog.close();
    
    if (success) {
        int ret = QMessageBox::question(this, "导出成功", 
                                       QString("部门数据已成功导出到：\n%1\n\n是否打开文件所在目录？").arg(fileName),
                                       QMessageBox::Yes | QMessageBox::No,
                                       QMessageBox::Yes);
        
        if (ret == QMessageBox::Yes) {
            // 打开文件所在目录
            QDir dir(QFileInfo(fileName).absolutePath());
            QString path = QDir::toNativeSeparators(dir.absolutePath());
#ifdef Q_OS_WIN
            QProcess::startDetached("explorer", {"/select,", QDir::toNativeSeparators(fileName)});
#elif defined(Q_OS_MAC)
            QProcess::startDetached("open", {"-R", fileName});
#else
            QProcess::startDetached("xdg-open", {path});
#endif
        }
    } else {
        QMessageBox::critical(this, "导出失败", "导出部门数据时发生错误！");
    }
}

void DepartmentManagementWidget::onShowContextMenu(const QPoint& pos)
{
    QTreeWidgetItem* item = m_treeWidget->itemAt(pos);
    
    // 根据是否有选中项更新菜单状态
    m_actionAddSub->setEnabled(item != nullptr);
    m_actionEdit->setEnabled(item != nullptr);
    m_actionDelete->setEnabled(item != nullptr);
    
    m_contextMenu->exec(m_treeWidget->mapToGlobal(pos));
}

// ========== 私有辅助方法 ==========

void DepartmentManagementWidget::loadDepartmentTree()
{
    m_treeWidget->clear();
    
    QList<std::shared_ptr<DepartmentTreeNode>> rootNodes = m_departmentDao->getDepartmentTree();
    buildTreeItems(rootNodes);
}

void DepartmentManagementWidget::buildTreeItems(const QList<std::shared_ptr<DepartmentTreeNode>>& nodes, 
                                               QTreeWidgetItem* parentItem)
{
    for (auto& node : nodes) {
        QTreeWidgetItem* item = nullptr;
        
        if (parentItem) {
            item = new QTreeWidgetItem(parentItem);
        } else {
            item = new QTreeWidgetItem(m_treeWidget);
        }
        
        // 设置显示文本
        item->setText(0, node->department.name());
        item->setText(1, node->department.code());
        item->setText(2, node->department.statusText());
        
        // 存储部门ID
        item->setData(0, Qt::UserRole, node->department.id());
        
        // 设置图标
        if (node->children.isEmpty()) {
            item->setIcon(0, style()->standardIcon(QStyle::SP_FileIcon));
        } else {
            item->setIcon(0, style()->standardIcon(QStyle::SP_DirIcon));
        }
        
        // 递归构建子项
        buildTreeItems(node->children, item);
    }
}

void DepartmentManagementWidget::showDepartmentDetails(const Department& department)
{
    qDebug() << "DepartmentManagementWidget: showDepartmentDetails()";
    qDebug() << "  - Department ID:" << department.id();
    qDebug() << "  - Department Name:" << department.name();
    qDebug() << "  - Department Parent ID:" << department.parentId();
    qDebug() << "  - Department Sort Order:" << department.sortOrder();
    
    m_editName->setText(department.name());
    m_editCode->setText(department.code());
    m_editDescription->setPlainText(department.description());
    
    // 设置状态
    for (int i = 0; i < m_comboStatus->count(); ++i) {
        if (m_comboStatus->itemData(i).toInt() == static_cast<int>(department.status())) {
            m_comboStatus->setCurrentIndex(i);
            break;
        }
    }
    
    // 设置父部门 - 添加详细调试
    qDebug() << "  - Setting parent department, target parent ID:" << department.parentId();
    qDebug() << "  - Parent ComboBox item count:" << m_comboParent->count();
    
    bool parentFound = false;
    for (int i = 0; i < m_comboParent->count(); ++i) {
        int comboData = m_comboParent->itemData(i).toInt();
        QString comboText = m_comboParent->itemText(i);
        qDebug() << "    - ComboBox item" << i << ": data =" << comboData << ", text =" << comboText;
        
        if (comboData == department.parentId()) {
            m_comboParent->setCurrentIndex(i);
            parentFound = true;
            qDebug() << "  - Parent found and set to index:" << i << ", text:" << comboText;
            break;
        }
    }
    
    if (!parentFound) {
        qDebug() << "  - WARNING: Parent ID" << department.parentId() << "not found in ComboBox, using default index 0";
        m_comboParent->setCurrentIndex(0);
    }
    
    // 设置排序值
    qDebug() << "  - Setting sort order to:" << department.sortOrder();
    m_spinSortOrder->setValue(department.sortOrder());
    
    // 验证设置结果
    qDebug() << "  - After setting: Parent ComboBox index =" << m_comboParent->currentIndex() << ", data =" << m_comboParent->currentData().toInt();
    qDebug() << "  - After setting: Sort Order SpinBox value =" << m_spinSortOrder->value();
    
    // 更新时间显示
    m_lblCreatedAt->setText(QString("创建时间: %1").arg(department.createdAt().toString("yyyy-MM-dd hh:mm:ss")));
    m_lblUpdatedAt->setText(QString("更新时间: %1").arg(department.updatedAt().toString("yyyy-MM-dd hh:mm:ss")));
}

void DepartmentManagementWidget::clearDetailsForm()
{
    m_editName->clear();
    m_editCode->clear();
    m_editDescription->clear();
    m_comboStatus->setCurrentIndex(0);
    m_comboParent->setCurrentIndex(0);
    m_spinSortOrder->setValue(0);
    
    m_lblChildCount->setText("子部门数量: 0");
    m_lblDirectUserCount->setText("直接人员: 0");
    m_lblTotalUserCount->setText("总人员: 0");
    m_lblCreatedAt->setText("创建时间: -");
    m_lblUpdatedAt->setText("更新时间: -");
}

void DepartmentManagementWidget::clearDetailsFormForAdd()
{
    m_editName->clear();
    m_editCode->clear();
    m_editDescription->clear();
    m_comboStatus->setCurrentIndex(0);
    // 注意：不重置父部门ComboBox和排序值，保持用户的选择
    // m_spinSortOrder->setValue(0); // 注释掉这行，保持用户设置的排序值
    
    m_lblChildCount->setText("子部门数量: 0");
    m_lblDirectUserCount->setText("直接人员: 0");
    m_lblTotalUserCount->setText("总人员: 0");
    m_lblCreatedAt->setText("创建时间: -");
    m_lblUpdatedAt->setText("更新时间: -");
}

void DepartmentManagementWidget::setFormEditMode(bool editMode)
{
    m_editName->setReadOnly(!editMode);
    m_editCode->setReadOnly(!editMode);
    m_editDescription->setReadOnly(!editMode);
    m_comboStatus->setEnabled(editMode);
    m_comboParent->setEnabled(editMode);
    m_spinSortOrder->setReadOnly(!editMode);
    
    m_btnSave->setVisible(editMode);
    m_btnCancel->setVisible(editMode);
}

Department DepartmentManagementWidget::getDepartmentFromForm()
{
    Department department;
    
    department.setName(m_editName->text().trimmed());
    department.setCode(m_editCode->text().trimmed());
    department.setDescription(m_editDescription->toPlainText().trimmed());
    department.setStatus(static_cast<Department::Status>(m_comboStatus->currentData().toInt()));
    
    // 添加调试信息
    int parentId = m_comboParent->currentData().toInt();
    int sortOrder = m_spinSortOrder->value();
    
    qDebug() << "DepartmentManagementWidget: getDepartmentFromForm()";
    qDebug() << "  - Name:" << department.name();
    qDebug() << "  - Code:" << department.code();
    qDebug() << "  - Status:" << static_cast<int>(department.status());
    qDebug() << "  - Parent ComboBox current index:" << m_comboParent->currentIndex();
    qDebug() << "  - Parent ComboBox current data:" << parentId;
    qDebug() << "  - Sort Order SpinBox value:" << sortOrder;
    
    department.setParentId(parentId);
    department.setSortOrder(sortOrder);
    
    return department;
}

QPair<bool, QString> DepartmentManagementWidget::validateForm()
{
    QString name = m_editName->text().trimmed();
    QString code = m_editCode->text().trimmed();
    
    if (name.isEmpty()) {
        return qMakePair(false, "部门名称不能为空！");
    }
    
    if (code.isEmpty()) {
        return qMakePair(false, "部门代码不能为空！");
    }
    
    if (name.length() > 100) {
        return qMakePair(false, "部门名称长度不能超过100个字符！");
    }
    
    if (code.length() > 20) {
        return qMakePair(false, "部门代码长度不能超过20个字符！");
    }
    
    // 检查代码是否已存在
    if (m_departmentDao->isCodeExists(code, m_currentDepartment.id())) {
        return qMakePair(false, QString("部门代码 \"%1\" 已存在！").arg(code));
    }
    
    return qMakePair(true, "");
}

QTreeWidgetItem* DepartmentManagementWidget::findTreeItem(int departmentId, QTreeWidgetItem* parentItem)
{
    if (!parentItem) {
        // 从根开始搜索
        for (int i = 0; i < m_treeWidget->topLevelItemCount(); ++i) {
            QTreeWidgetItem* item = findTreeItem(departmentId, m_treeWidget->topLevelItem(i));
            if (item) return item;
        }
    } else {
        // 检查当前项
        if (parentItem->data(0, Qt::UserRole).toInt() == departmentId) {
            return parentItem;
        }
        
        // 递归搜索子项
        for (int i = 0; i < parentItem->childCount(); ++i) {
            QTreeWidgetItem* item = findTreeItem(departmentId, parentItem->child(i));
            if (item) return item;
        }
    }
    
    return nullptr;
}

void DepartmentManagementWidget::updateDepartmentStats(int departmentId)
{
    if (departmentId <= 0) return;
    
    // 获取统计信息
    auto stats = m_departmentDao->getDepartmentStats(departmentId);
    
    m_lblChildCount->setText(QString("子部门数量: %1").arg(stats.childCount));
    m_lblDirectUserCount->setText(QString("直接人员: %1").arg(stats.directUserCount));
    m_lblTotalUserCount->setText(QString("总人员: %1").arg(stats.totalUserCount));
}

void DepartmentManagementWidget::applySearchFilter(const QString& keyword)
{
    if (keyword.isEmpty()) {
        // 显示所有项目
        for (int i = 0; i < m_treeWidget->topLevelItemCount(); ++i) {
            filterTreeItem(m_treeWidget->topLevelItem(i), "");
        }
    } else {
        // 应用过滤
        for (int i = 0; i < m_treeWidget->topLevelItemCount(); ++i) {
            filterTreeItem(m_treeWidget->topLevelItem(i), keyword);
        }
    }
}

bool DepartmentManagementWidget::filterTreeItem(QTreeWidgetItem* item, const QString& keyword)
{
    if (!item) return false;
    
    bool visible = false;
    
    if (keyword.isEmpty()) {
        visible = true;
    } else {
        // 检查当前项是否匹配
        QString name = item->text(0);
        QString code = item->text(1);
        
        if (name.contains(keyword, Qt::CaseInsensitive) || 
            code.contains(keyword, Qt::CaseInsensitive)) {
            visible = true;
        }
        
        // 检查子项
        for (int i = 0; i < item->childCount(); ++i) {
            if (filterTreeItem(item->child(i), keyword)) {
                visible = true;
            }
        }
    }
    
    item->setHidden(!visible);
    return visible;
}

// ========== 导入导出功能实现 ==========

bool DepartmentManagementWidget::exportDepartmentsToCSV(const QString& fileName)
{
    QFile file(fileName);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        qDebug() << "Failed to open file for writing:" << fileName;
        return false;
    }
    
    // 写入UTF-8 BOM以确保Excel正确识别编码
    QByteArray bom;
    bom.append(char(0xEF));
    bom.append(char(0xBB));
    bom.append(char(0xBF));
    file.write(bom);
    
    QTextStream out(&file);
    out.setEncoding(QStringConverter::Utf8);
    
    // 写入CSV头部
    out << "部门名称,部门代码,上级部门代码,描述,状态,排序,创建时间,更新时间\n";
    
    // 获取所有部门数据
    QList<Department> departments = m_departmentDao->findAll();
    
    // 创建代码到名称的映射，便于查找上级部门名称
    QMap<int, QString> idToCodeMap;
    for (const Department& dept : departments) {
        idToCodeMap[dept.id()] = dept.code();
    }
    
    // 写入数据行
    for (const Department& dept : departments) {
        QString parentCode = "";
        if (dept.parentId() > 0 && idToCodeMap.contains(dept.parentId())) {
            parentCode = idToCodeMap[dept.parentId()];
        }
        
        QString statusText = (dept.status() == Department::Active) ? "启用" : "禁用";
        
        // 处理可能包含逗号或引号的字段，使用标准CSV转义规则
        auto escapeCSVField = [](const QString& field) -> QString {
            if (field.contains(',') || field.contains('"') || field.contains('\n') || field.contains('\r')) {
                return "\"" + field.trimmed().replace("\"", "\"\"") + "\"";
            }
            return field.trimmed();
        };
        
        out << escapeCSVField(dept.name()) << ","
            << escapeCSVField(dept.code()) << ","
            << escapeCSVField(parentCode) << ","
            << escapeCSVField(dept.description()) << ","
            << escapeCSVField(statusText) << ","
            << dept.sortOrder() << ","
            << dept.createdAt().toString("yyyy-MM-dd hh:mm:ss") << ","
            << dept.updatedAt().toString("yyyy-MM-dd hh:mm:ss") << "\n";
    }
    
    file.close();
    return true;
}

QPair<bool, QString> DepartmentManagementWidget::importDepartmentsFromCSV(const QString& fileName)
{
    QFile file(fileName);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        return qMakePair(false, QString("无法打开文件：%1").arg(fileName));
    }
    
    // 读取所有内容并检测BOM
    QByteArray fileContent = file.readAll();
    file.close();
    
    // 检查并移除UTF-8 BOM
    if (fileContent.startsWith("\xEF\xBB\xBF")) {
        fileContent.remove(0, 3);
    }
    
    // 使用UTF-8解码
    QString content = QString::fromUtf8(fileContent);
    QStringList lines = content.split('\n', Qt::SkipEmptyParts);
    
    QList<Department> importDepartments;
    QStringList errors;
    int lineNumber = 0;
    bool hasHeader = false;
    
    for (const QString& line : lines) {
        QString trimmedLine = line.trimmed();
        lineNumber++;
        
        if (trimmedLine.isEmpty()) {
            continue;
        }
        
        // 解析CSV行
        QStringList fields = parseCSVLine(trimmedLine);
        
        // 跳过头部行
        if (!hasHeader) {
            hasHeader = true;
            if (fields.size() >= 2 && 
                (fields[0].contains("部门名称") || fields[0].contains("name", Qt::CaseInsensitive))) {
                continue;
            }
        }
        
        // 验证字段数量
        if (fields.size() < 2) {
            errors.append(QString("第%1行：字段数量不足（至少需要部门名称和代码）").arg(lineNumber));
            continue;
        }
        
        // 解析部门数据
        auto parseResult = parseCSVDepartment(fields, lineNumber);
        if (parseResult.second) {
            importDepartments.append(parseResult.first);
        } else {
            errors.append(QString("第%1行：数据解析失败").arg(lineNumber));
        }
    }
    
    if (importDepartments.isEmpty()) {
        return qMakePair(false, "没有找到有效的部门数据。\n错误信息：\n" + errors.join("\n"));
    }
    
    // 验证导入数据
    auto validationResult = validateImportData(importDepartments);
    if (!validationResult.first) {
        return qMakePair(false, validationResult.second);
    }
    
    // 开始导入数据
    int successCount = 0;
    int updateCount = 0;
    int skipCount = 0;
    
    // 创建代码到ID的映射
    QMap<QString, int> codeToIdMap;
    QList<Department> existingDepartments = m_departmentDao->findAll();
    for (const Department& dept : existingDepartments) {
        codeToIdMap[dept.code()] = dept.id();
    }
    
    // 按层级顺序处理部门（先处理根部门，再处理子部门）
    QList<Department> sortedDepartments = sortDepartmentsByLevel(importDepartments);
    
    for (const Department& dept : sortedDepartments) {
        try {
            if (codeToIdMap.contains(dept.code())) {
                // 更新现有部门
                Department updateDept = dept;
                updateDept.setId(codeToIdMap[dept.code()]);
                
                // 解析父部门ID - fullPath中存储的是上级部门代码
                QString parentCode = dept.fullPath().trimmed();
                if (!parentCode.isEmpty() && codeToIdMap.contains(parentCode)) {
                    updateDept.setParentId(codeToIdMap[parentCode]);
                    qDebug() << "导入更新：设置部门" << dept.code() << "的上级为" << parentCode << "(ID:" << codeToIdMap[parentCode] << ")";
                } else {
                    updateDept.setParentId(0); // 根部门
                    qDebug() << "导入更新：设置部门" << dept.code() << "为根部门";
                }
                
                if (m_departmentDao->updateDepartment(updateDept)) {
                    updateCount++;
                } else {
                    skipCount++;
                }
            } else {
                // 创建新部门
                Department newDept = dept;
                
                // 解析父部门ID - fullPath中存储的是上级部门代码
                QString parentCode = dept.fullPath().trimmed();
                if (!parentCode.isEmpty() && codeToIdMap.contains(parentCode)) {
                    newDept.setParentId(codeToIdMap[parentCode]);
                    qDebug() << "导入新增：设置部门" << dept.code() << "的上级为" << parentCode << "(ID:" << codeToIdMap[parentCode] << ")";
                } else {
                    newDept.setParentId(0); // 根部门
                    qDebug() << "导入新增：设置部门" << dept.code() << "为根部门";
                }
                
                int newId = m_departmentDao->createDepartment(newDept);
                if (newId > 0) {
                    codeToIdMap[dept.code()] = newId;
                    successCount++;
                } else {
                    skipCount++;
                }
            }
        } catch (const std::exception& e) {
            skipCount++;
            errors.append(QString("处理部门 %1 时发生错误：%2").arg(dept.code()).arg(e.what()));
        }
    }
    
    QString resultMessage = QString("导入完成！\n新增部门：%1个\n更新部门：%2个\n跳过部门：%3个")
                           .arg(successCount).arg(updateCount).arg(skipCount);
    
    if (!errors.isEmpty()) {
        resultMessage += "\n\n错误信息：\n" + errors.join("\n");
    }
    
    return qMakePair(true, resultMessage);
}

bool DepartmentManagementWidget::generateCSVTemplate(const QString& fileName)
{
    QFile file(fileName);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        return false;
    }
    
    // 写入UTF-8 BOM以确保Excel正确识别编码
    QByteArray bom;
    bom.append(char(0xEF));
    bom.append(char(0xBB));
    bom.append(char(0xBF));
    file.write(bom);
    
    QTextStream out(&file);
    out.setEncoding(QStringConverter::Utf8);
    
    // 写入模板头部
    out << "部门名称,部门代码,上级部门代码,描述,状态,排序,创建时间,更新时间\n";
    
    // 写入示例数据
    out << "总公司,COMPANY,,公司总部,启用,0,2024-01-01 00:00:00,2024-01-01 00:00:00\n";
    out << "技术部,TECH,COMPANY,技术开发部门,启用,1,2024-01-01 00:00:00,2024-01-01 00:00:00\n";
    out << "开发组,DEV,TECH,软件开发组,启用,1,2024-01-01 00:00:00,2024-01-01 00:00:00\n";
    out << "测试组,TEST,TECH,软件测试组,启用,2,2024-01-01 00:00:00,2024-01-01 00:00:00\n";
    out << "市场部,MARKET,COMPANY,市场营销部门,启用,2,2024-01-01 00:00:00,2024-01-01 00:00:00\n";
    
    file.close();
    return true;
}

QPair<bool, QString> DepartmentManagementWidget::validateImportData(const QList<Department>& departments)
{
    QStringList errors;
    QSet<QString> codes;
    
    for (const Department& dept : departments) {
        // 检查必填字段
        if (dept.name().trimmed().isEmpty()) {
            errors.append(QString("部门名称不能为空（代码：%1）").arg(dept.code()));
        }
        
        if (dept.code().trimmed().isEmpty()) {
            errors.append(QString("部门代码不能为空（名称：%1）").arg(dept.name()));
        }
        
        // 检查代码重复
        if (codes.contains(dept.code())) {
            errors.append(QString("部门代码重复：%1").arg(dept.code()));
        } else {
            codes.insert(dept.code());
        }
        
        // 检查字段长度
        if (dept.name().length() > 100) {
            errors.append(QString("部门名称过长（%1），最多100个字符").arg(dept.code()));
        }
        
        if (dept.code().length() > 20) {
            errors.append(QString("部门代码过长（%1），最多20个字符").arg(dept.code()));
        }
    }
    
    if (!errors.isEmpty()) {
        return qMakePair(false, "数据验证失败：\n" + errors.join("\n"));
    }
    
    return qMakePair(true, "数据验证通过");
}

QPair<Department, bool> DepartmentManagementWidget::parseCSVDepartment(const QStringList& fields, int lineNumber)
{
    Department dept;
    
    if (fields.size() < 2) {
        return qMakePair(dept, false);
    }
    
    try {
        // 部门名称（必填）
        dept.setName(fields[0].trimmed());
        
        // 部门代码（必填）
        dept.setCode(fields[1].trimmed());
        
        // 上级部门代码（可选）- 临时存储在fullPath中
        if (fields.size() > 2 && !fields[2].trimmed().isEmpty()) {
            dept.setFullPath(fields[2].trimmed());
            qDebug() << "解析CSV：部门" << dept.code() << "的上级部门代码为：" << fields[2].trimmed();
        } else {
            qDebug() << "解析CSV：部门" << dept.code() << "为根部门（无上级）";
        }
        
        // 描述（可选）
        if (fields.size() > 3) {
            dept.setDescription(fields[3].trimmed());
        }
        
        // 状态（可选，默认启用）
        if (fields.size() > 4) {
            QString status = fields[4].trimmed();
            if (status == "禁用" || status == "0" || status.toLower() == "disabled") {
                dept.setStatus(Department::Inactive);
            } else {
                dept.setStatus(Department::Active);
            }
        } else {
            dept.setStatus(Department::Active);
        }
        
        // 排序（可选，默认0）
        if (fields.size() > 5) {
            bool ok;
            int sortOrder = fields[5].trimmed().toInt(&ok);
            if (ok) {
                dept.setSortOrder(sortOrder);
            }
        }
        
        // 设置默认时间
        QDateTime now = QDateTime::currentDateTime();
        dept.setCreatedAt(now);
        dept.setUpdatedAt(now);
        
        return qMakePair(dept, true);
    } catch (const std::exception& e) {
        qDebug() << "Error parsing CSV line" << lineNumber << ":" << e.what();
        return qMakePair(dept, false);
    }
}

QStringList DepartmentManagementWidget::parseCSVLine(const QString& line)
{
    QStringList fields;
    QString field;
    bool inQuotes = false;
    
    for (int i = 0; i < line.length(); ++i) {
        QChar c = line[i];
        
        if (c == '"') {
            if (inQuotes && i + 1 < line.length() && line[i + 1] == '"') {
                // 双引号转义
                field += '"';
                ++i;
            } else {
                inQuotes = !inQuotes;
            }
        } else if (c == ',' && !inQuotes) {
            fields.append(field);
            field.clear();
        } else {
            field += c;
        }
    }
    
    fields.append(field);
    return fields;
}

QList<Department> DepartmentManagementWidget::sortDepartmentsByLevel(const QList<Department>& departments)
{
    QList<Department> sorted;
    QMap<QString, Department> codeMap;
    
    // 创建代码映射
    for (const Department& dept : departments) {
        codeMap[dept.code()] = dept;
    }
    
    // 先添加根部门（没有上级部门的）
    for (const Department& dept : departments) {
        if (dept.fullPath().trimmed().isEmpty()) {
            sorted.append(dept);
            qDebug() << "排序：添加根部门" << dept.code();
        }
    }
    
    // 然后按层级添加子部门
    int processedCount = 0;
    int maxIterations = departments.size() * 2; // 防止无限循环
    
    while (sorted.size() < departments.size() && processedCount < maxIterations) {
        processedCount++;
        
        for (const Department& dept : departments) {
            // 跳过根部门（已经处理）
            if (dept.fullPath().trimmed().isEmpty()) {
                continue;
            }
            
            // 检查是否已经添加
            bool alreadyAdded = false;
            for (const Department& added : sorted) {
                if (added.code() == dept.code()) {
                    alreadyAdded = true;
                    break;
                }
            }
            
            if (alreadyAdded) {
                continue;
            }
            
            // 检查父部门是否已经添加
            QString parentCode = dept.fullPath().trimmed();
            bool parentExists = false;
            for (const Department& added : sorted) {
                if (added.code() == parentCode) {
                    parentExists = true;
                    break;
                }
            }
            
            if (parentExists) {
                sorted.append(dept);
                qDebug() << "排序：添加子部门" << dept.code() << "（上级：" << parentCode << "）";
            }
        }
    }
    
    qDebug() << "排序完成：共处理" << sorted.size() << "个部门，原始" << departments.size() << "个";
    
    return sorted;
}

} // namespace AccessControl 