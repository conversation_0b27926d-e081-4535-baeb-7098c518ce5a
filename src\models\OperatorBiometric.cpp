#include "OperatorBiometric.h"
#include <QFile>
#include <QFileInfo>
#include <QDir>
#include <QDebug>
#include <QImage>
#include <QImageReader>

namespace AccessControl {

OperatorBiometric::OperatorBiometric()
    : m_id(0)
    , m_userId(0)
    , m_photoPath("")
    , m_photoData()
    , m_fingerprintTemplate()
    , m_faceTemplate()
    , m_createdAt(QDateTime::currentDateTime())
    , m_updatedAt(QDateTime::currentDateTime())
{
}

OperatorBiometric::OperatorBiometric(int userId)
    : m_id(0)
    , m_userId(userId)
    , m_photoPath("")
    , m_photoData()
    , m_fingerprintTemplate()
    , m_faceTemplate()
    , m_createdAt(QDateTime::currentDateTime())
    , m_updatedAt(QDateTime::currentDateTime())
{
}

// ========== 验证方法 ==========

bool OperatorBiometric::isValid() const {
    return m_userId > 0;
}

bool OperatorBiometric::hasAnyBiometric() const {
    return hasPhoto() || hasFingerprint() || hasFace();
}

bool OperatorBiometric::hasBiometricType(BiometricType type) const {
    switch (type) {
        case BiometricType::Photo: return hasPhoto();
        case BiometricType::Fingerprint: return hasFingerprint();
        case BiometricType::Face: return hasFace();
        default: return false;
    }
}

// ========== 数据管理 ==========

bool OperatorBiometric::savePhotoToFile(const QString& filePath) {
    if (m_photoData.isEmpty()) {
        qWarning() << "没有照片数据可保存";
        return false;
    }

    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly)) {
        qWarning() << "无法打开文件进行写入:" << filePath;
        return false;
    }

    qint64 bytesWritten = file.write(m_photoData);
    file.close();

    if (bytesWritten != m_photoData.size()) {
        qWarning() << "照片数据写入不完整";
        return false;
    }

    m_photoPath = filePath;
    m_updatedAt = QDateTime::currentDateTime();
    return true;
}

bool OperatorBiometric::loadPhotoFromFile(const QString& filePath) {
    if (!isValidPhotoFormat(filePath)) {
        qWarning() << "不支持的照片格式:" << filePath;
        return false;
    }

    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly)) {
        qWarning() << "无法打开照片文件:" << filePath;
        return false;
    }

    m_photoData = file.readAll();
    file.close();

    if (m_photoData.isEmpty()) {
        qWarning() << "照片文件为空:" << filePath;
        return false;
    }

    m_photoPath = filePath;
    m_updatedAt = QDateTime::currentDateTime();
    return true;
}

bool OperatorBiometric::saveFingerprintTemplate(const QByteArray& templateData) {
    if (templateData.isEmpty()) {
        qWarning() << "指纹模板数据为空";
        return false;
    }

    m_fingerprintTemplate = templateData;
    m_updatedAt = QDateTime::currentDateTime();
    return true;
}

bool OperatorBiometric::saveFaceTemplate(const QByteArray& templateData) {
    if (templateData.isEmpty()) {
        qWarning() << "人脸模板数据为空";
        return false;
    }

    m_faceTemplate = templateData;
    m_updatedAt = QDateTime::currentDateTime();
    return true;
}

void OperatorBiometric::clearBiometricData(BiometricType type) {
    switch (type) {
        case BiometricType::Photo:
            m_photoData.clear();
            m_photoPath.clear();
            break;
        case BiometricType::Fingerprint:
            m_fingerprintTemplate.clear();
            break;
        case BiometricType::Face:
            m_faceTemplate.clear();
            break;
    }
    m_updatedAt = QDateTime::currentDateTime();
}

// ========== 序列化 ==========

QVariantMap OperatorBiometric::toVariantMap() const {
    QVariantMap map;
    
    map["id"] = m_id;
    map["userId"] = m_userId;
    map["photoPath"] = m_photoPath;
    map["photoData"] = m_photoData;
    map["fingerprintTemplate"] = m_fingerprintTemplate;
    map["faceTemplate"] = m_faceTemplate;
    map["createdAt"] = m_createdAt;
    map["updatedAt"] = m_updatedAt;
    
    return map;
}

void OperatorBiometric::fromVariantMap(const QVariantMap& map) {
    m_id = map.value("id", 0).toInt();
    m_userId = map.value("userId", 0).toInt();
    m_photoPath = map.value("photoPath").toString();
    m_photoData = map.value("photoData").toByteArray();
    m_fingerprintTemplate = map.value("fingerprintTemplate").toByteArray();
    m_faceTemplate = map.value("faceTemplate").toByteArray();
    m_createdAt = map.value("createdAt").toDateTime();
    m_updatedAt = map.value("updatedAt").toDateTime();
}

// ========== 静态方法 ==========

QString OperatorBiometric::biometricTypeToString(BiometricType type) {
    switch (type) {
        case BiometricType::Photo: return "照片";
        case BiometricType::Fingerprint: return "指纹";
        case BiometricType::Face: return "人脸";
        default: return "未知";
    }
}

OperatorBiometric::BiometricType OperatorBiometric::stringToBiometricType(const QString& typeStr) {
    if (typeStr == "照片" || typeStr == "Photo") return BiometricType::Photo;
    if (typeStr == "指纹" || typeStr == "Fingerprint") return BiometricType::Fingerprint;
    if (typeStr == "人脸" || typeStr == "Face") return BiometricType::Face;
    return BiometricType::Photo; // 默认
}

QString OperatorBiometric::getDefaultPhotoPath(int userId) {
    // 创建用户照片目录
    QString photoDir = QString("photos/user_%1").arg(userId);
    QDir().mkpath(photoDir);
    
    // 返回默认照片路径
    return QString("%1/photo.jpg").arg(photoDir);
}

bool OperatorBiometric::isValidPhotoFormat(const QString& filePath) {
    QFileInfo fileInfo(filePath);
    QString suffix = fileInfo.suffix().toLower();
    
    // 支持的图片格式
    QStringList supportedFormats = {"jpg", "jpeg", "png", "bmp", "gif"};
    return supportedFormats.contains(suffix);
}

} // namespace AccessControl 