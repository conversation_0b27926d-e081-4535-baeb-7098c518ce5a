#include "ConsumerPhotoWidget.h"
#include "CameraDialog.h"

#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QFileDialog>
#include <QMessageBox>
#include <QPixmap>
#include <QMouseEvent>
#include <QPainter>
#include <QFont>
#include <QFontMetrics>
#include <QBuffer>
#include <QMediaDevices>
#include <QCameraDevice>
#include <QImage>
#include <QDir>

namespace AccessControl {

// 证件照裁剪函数（建议与CameraDialog保持一致，可提取到utils）
static QImage cropToIDPhoto(const QImage &src, int targetW, int targetH) {
    double targetRatio = double(targetW) / targetH;
    int w = src.width();
    int h = src.height();
    double srcRatio = double(w) / h;
    int cropW, cropH, x, y;
    if (srcRatio > targetRatio) {
        cropH = h;
        cropW = int(h * targetRatio);
        x = (w - cropW) / 2;
        y = 0;
    } else {
        cropW = w;
        cropH = int(w / targetRatio);
        x = 0;
        y = (h - cropH) / 2;
    }
    return src.copy(x, y, cropW, cropH).scaled(targetW, targetH, Qt::KeepAspectRatio, Qt::SmoothTransformation);
}

ConsumerPhotoWidget::ConsumerPhotoWidget(QWidget *parent)
    : QWidget(parent)
    , m_photoLabel(nullptr)
    , m_contextMenu(nullptr)
    , m_uploadAction(nullptr)
    , m_takePhotoAction(nullptr)
    , m_deleteAction(nullptr)
{
    initUI();
    createContextMenu();
}

ConsumerPhotoWidget::~ConsumerPhotoWidget()
{
    // 析构函数内容
}

void ConsumerPhotoWidget::initUI()
{
    // 设置固定大小
    setFixedSize(200, 250);
    
    // 创建主布局
    QVBoxLayout *layout = new QVBoxLayout(this);
    layout->setContentsMargins(0, 0, 0, 0);
    
    // 创建照片显示标签
    m_photoLabel = new QLabel(this);
    m_photoLabel->setFixedSize(198, 248); // 稍微小一点，留出边框空间
    m_photoLabel->setAlignment(Qt::AlignCenter);
    m_photoLabel->setStyleSheet("QLabel { border: 2px dashed #cccccc; background-color: #f9f9f9; }");
    m_photoLabel->setScaledContents(true);
    
    layout->addWidget(m_photoLabel);
    
    // 初始化时没有照片数据
    m_photoData.clear();
    
    updateDisplay();
}

void ConsumerPhotoWidget::createContextMenu()
{
    m_contextMenu = new QMenu(this);
    
    m_uploadAction = new QAction(tr("上传照片"), this);
    connect(m_uploadAction, &QAction::triggered, this, &ConsumerPhotoWidget::uploadPhoto);
    m_contextMenu->addAction(m_uploadAction);
    
    m_takePhotoAction = new QAction(tr("拍照"), this);
    connect(m_takePhotoAction, &QAction::triggered, this, &ConsumerPhotoWidget::takePhoto);
    m_contextMenu->addAction(m_takePhotoAction);
    
    m_deleteAction = new QAction(tr("删除"), this);
    connect(m_deleteAction, &QAction::triggered, this, &ConsumerPhotoWidget::deletePhoto);
    m_contextMenu->addAction(m_deleteAction);
}

void ConsumerPhotoWidget::mousePressEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton || event->button() == Qt::RightButton) {
        // 更新删除按钮状态
        m_deleteAction->setEnabled(hasPhoto());
        
        // 显示右键菜单
        m_contextMenu->exec(mapToGlobal(event->pos()));
    }
    
    QWidget::mousePressEvent(event);
}

void ConsumerPhotoWidget::paintEvent(QPaintEvent *event)
{
    QWidget::paintEvent(event);
    // +图标和文字现在直接绘制在QLabel的pixmap上，不需要在这里处理
}

void ConsumerPhotoWidget::setPhotoData(const QByteArray& photoData)
{
    m_photoData = photoData;
    
    if (!photoData.isEmpty()) {
        m_photoPixmap.loadFromData(photoData);
    } else {
        m_photoPixmap = QPixmap();
    }
    
    updateDisplay();
    emit photoChanged();
}

QByteArray ConsumerPhotoWidget::photoData() const
{
    return m_photoData;
}

void ConsumerPhotoWidget::clearPhoto()
{
    m_photoData.clear();
    m_photoPixmap = QPixmap();
    updateDisplay();
    emit photoChanged();
}

bool ConsumerPhotoWidget::hasPhoto() const
{
    return !m_photoData.isEmpty();
}

bool ConsumerPhotoWidget::savePhotoToFile(const QString& fileName, const QString& directory) const
{
    if (!hasPhoto()) {
        return false;
    }
    
    // 创建完整的文件路径
    QString fullPath = QDir(directory).absoluteFilePath(fileName + ".jpg");
    
    // 从照片数据创建QImage
    QImage image;
    if (!image.loadFromData(m_photoData)) {
        qDebug() << "Failed to load image from photo data";
        return false;
    }
    
    // 保存图片
    if (!image.save(fullPath, "JPEG", 90)) {
        qDebug() << "Failed to save photo to:" << fullPath;
        return false;
    }
    
    qDebug() << "Photo saved to:" << fullPath;
    return true;
}

void ConsumerPhotoWidget::uploadPhoto() {
    QString fileName = QFileDialog::getOpenFileName(this, tr("选择照片"), "", tr("图片文件 (*.jpg *.jpeg *.png)"));
    if (fileName.isEmpty()) return;
    QImage img(fileName);
    if (img.isNull()) {
        QMessageBox::warning(this, tr("错误"), tr("无法打开图片文件"));
        return;
    }
    // 裁剪为证件照比例
    const int idPhotoW = 300, idPhotoH = 400;
    QImage cropped = cropToIDPhoto(img, idPhotoW, idPhotoH);
    QByteArray photoData;
    QBuffer buffer(&photoData);
    buffer.open(QIODevice::WriteOnly);
    cropped.save(&buffer, "JPEG", 90);
    setPhotoData(photoData);
}

void ConsumerPhotoWidget::takePhoto()
{
    // 创建相机对话框
    CameraDialog cameraDialog(this);
    
    // 显示对话框
    if (cameraDialog.exec() == QDialog::Accepted) {
        QByteArray photoData = cameraDialog.getCapturedImageData();
        if (!photoData.isEmpty()) {
            setPhotoData(photoData);
        }
    }
}

void ConsumerPhotoWidget::deletePhoto()
{
    if (hasPhoto()) {
        if (QMessageBox::question(this, tr("确认删除"), tr("确定要删除照片吗？"),
                                 QMessageBox::Yes | QMessageBox::No) == QMessageBox::Yes) {
            clearPhoto();
        }
    }
}

void ConsumerPhotoWidget::updateDisplay()
{
    if (hasPhoto() && !m_photoPixmap.isNull()) {
        // 缩放照片以适应标签大小
        QPixmap scaledPixmap = m_photoPixmap.scaled(m_photoLabel->size(), 
                                                   Qt::KeepAspectRatio, 
                                                   Qt::SmoothTransformation);
        m_photoLabel->setPixmap(scaledPixmap);
        m_photoLabel->setStyleSheet("QLabel { border: 2px solid #cccccc; background-color: white; }");
        m_photoLabel->setText(""); // 清除文字
    } else {
        // 没有照片时，创建一个带+图标和文字的图片
        QPixmap placeholderPixmap(m_photoLabel->size());
        placeholderPixmap.fill(QColor(249, 249, 249)); // 背景色
        
        QPainter painter(&placeholderPixmap);
        painter.setRenderHint(QPainter::Antialiasing);
        
        // 绘制+号
        painter.setPen(QPen(QColor(150, 150, 150), 4));
        int centerX = placeholderPixmap.width() / 2;
        int centerY = placeholderPixmap.height() / 2 - 15;
        int size = 40;
        
        // 水平线
        painter.drawLine(centerX - size/2, centerY, centerX + size/2, centerY);
        // 垂直线
        painter.drawLine(centerX, centerY - size/2, centerX, centerY + size/2);
        
        // 绘制文字
        painter.setPen(QColor(100, 100, 100));
        QFont font = painter.font();
        font.setPointSize(14);
        font.setBold(true);
        painter.setFont(font);
        
        QFontMetrics fm(font);
        QString text = tr("添加照片");
        int textWidth = fm.horizontalAdvance(text);
        int textX = centerX - textWidth / 2;
        int textY = centerY + size/2 + 35;
        
        painter.drawText(textX, textY, text);
        
        m_photoLabel->setPixmap(placeholderPixmap);
        m_photoLabel->setStyleSheet("QLabel { border: 2px dashed #cccccc; background-color: #f9f9f9; }");
    }
}

} // namespace AccessControl 