#ifndef OPERATOR_H
#define OPERATOR_H

#include <QString>
#include <QDateTime>
#include <QDate>
#include <QUuid>

namespace AccessControl {

class Operator {
public:
    enum class Role {
        SuperAdmin,
        Admin,
        Operator,
        Viewer
    };

    enum class Status {
        Active,
        Inactive,
        Locked,
        Expired
    };

    Operator();

    // Getters
    int id() const;
    const QString& Operatorname() const;
    const QString& passwordHash() const;
    const QString& salt() const;
    Role role() const;
    Status status() const;
    const QString& realName() const;
    const QString& email() const;
    const QDateTime& createdAt() const;
    const QDateTime& updatedAt() const;
    const QDateTime& lastLoginAt() const;
    const QString& lastLoginIp() const;
    int loginAttempts() const;

    // Getters - Extended
    const QString& phoneNumber() const;
    const QString& idNumber() const;
    bool accessEnabled() const;
    bool attendanceEnabled() const;
    const QDate& validFrom() const;
    const QDate& validUntil() const;
    int departmentId() const;
    const QString& workNumber() const;
    bool shiftWork() const;

    // Setters
    void setId(int id);
    void setOperatorname(const QString& Operatorname);
    void setPassword(const QString& password);
    void setPasswordHash(const QString& passwordHash);
    void setSalt(const QString& salt);
    void setRole(Role role);
    void setStatus(Status status);
    void setRealName(const QString& realName);
    void setEmail(const QString& email);
    void setCreatedAt(const QDateTime& createdAt);
    void setUpdatedAt(const QDateTime& updatedAt);
    void setLastLoginAt(const QDateTime& lastLoginAt);
    void setLastLoginIp(const QString& lastLoginIp);
    void setLoginAttempts(int loginAttempts);

    // Setters - Extended
    void setPhoneNumber(const QString& phoneNumber);
    void setIdNumber(const QString& idNumber);
    void setAccessEnabled(bool enabled);
    void setAttendanceEnabled(bool enabled);
    void setValidFrom(const QDate& validFrom);
    void setValidUntil(const QDate& validUntil);
    void setDepartmentId(int departmentId);
    void setWorkNumber(const QString& workNumber);
    void setShiftWork(bool shiftWork);

    bool checkPassword(const QString& password) const;
    static QString hashPassword(const QString& password, const QString& salt);
    static QString roleToString(Role role);
    static QString statusToString(Status status);

    // Validation methods
    bool isValid() const;
    bool isExpired() const;
    bool isInValidPeriod() const;
    QString getDisplayName() const;

private:
    int m_id;
    QString m_Operatorname;
    QString m_passwordHash;
    QString m_salt;
    Role m_role;
    Status m_status;
    QString m_realName;
    QString m_email;
    QDateTime m_createdAt;
    QDateTime m_updatedAt;
    QDateTime m_lastLoginAt;
    QString m_lastLoginIp;
    int m_loginAttempts;

    QString m_phoneNumber;
    QString m_idNumber;
    bool m_accessEnabled;
    bool m_attendanceEnabled;
    QDate m_validFrom;
    QDate m_validUntil;
    int m_departmentId;
    QString m_workNumber;
    bool m_shiftWork;
};

} // namespace AccessControl

#endif // OPERATOR_H
