#include "Area.h"
#include <QDebug>

namespace AccessControl {

// ========== Area 类实现 ==========

Area::Area()
    : m_id(0)
    , m_parentId(0)
    , m_status(Active)
    , m_sortOrder(0)
    , m_level(0)
{
    m_createdAt = QDateTime::currentDateTime();
    m_updatedAt = m_createdAt;
}

Area::Area(const QString& name, const QString& code, int parentId)
    : m_id(0)
    , m_name(name)
    , m_code(code)
    , m_parentId(parentId)
    , m_status(Active)
    , m_sortOrder(0)
    , m_level(0)
{
    m_createdAt = QDateTime::currentDateTime();
    m_updatedAt = m_createdAt;
}

Area::Area(const Area& other)
    : m_id(other.m_id)
    , m_name(other.m_name)
    , m_code(other.m_code)
    , m_parentId(other.m_parentId)
    , m_description(other.m_description)
    , m_status(other.m_status)
    , m_sortOrder(other.m_sortOrder)
    , m_createdAt(other.m_createdAt)
    , m_updatedAt(other.m_updatedAt)
    , m_level(other.m_level)
    , m_fullPath(other.m_fullPath)
{
}

Area& Area::operator=(const Area& other)
{
    if (this != &other) {
        m_id = other.m_id;
        m_name = other.m_name;
        m_code = other.m_code;
        m_parentId = other.m_parentId;
        m_description = other.m_description;
        m_status = other.m_status;
        m_sortOrder = other.m_sortOrder;
        m_createdAt = other.m_createdAt;
        m_updatedAt = other.m_updatedAt;
        m_level = other.m_level;
        m_fullPath = other.m_fullPath;
    }
    return *this;
}

Area::~Area()
{
}

QString Area::statusText() const
{
    switch (m_status) {
        case Active:
            return "启用";
        case Inactive:
            return "禁用";
        default:
            return "未知";
    }
}

bool Area::isValid() const
{
    // 基本验证：名称和代码不能为空
    if (m_name.trimmed().isEmpty()) {
        return false;
    }
    
    if (m_code.trimmed().isEmpty()) {
        return false;
    }
    
    // 代码长度限制（1-20字符）
    if (m_code.length() > 20) {
        return false;
    }
    
    // 名称长度限制（1-100字符）
    if (m_name.length() > 100) {
        return false;
    }
    
    // 父区域ID不能是自己（当ID不为0时）
    if (m_id != 0 && m_parentId == m_id) {
        return false;
    }
    
    return true;
}

QString Area::toString() const
{
    return QString("Area(id=%1, name='%2', code='%3', parentId=%4, status=%5)")
           .arg(m_id)
           .arg(m_name)
           .arg(m_code)
           .arg(m_parentId)
           .arg(m_status);
}

// ========== AreaTreeNode 类实现 ==========

AreaTreeNode::AreaTreeNode(const Area& area)
    : area(area)
    , parent(nullptr)
{
}

AreaTreeNode::~AreaTreeNode()
{
    // 智能指针会自动处理子节点的释放
}

void AreaTreeNode::addChild(std::shared_ptr<AreaTreeNode> child)
{
    if (child) {
        child->parent = this;
        children.append(child);
    }
}

bool AreaTreeNode::removeChild(int areaId)
{
    for (int i = 0; i < children.size(); ++i) {
        if (children[i]->area.id() == areaId) {
            children[i]->parent = nullptr;
            children.removeAt(i);
            return true;
        }
    }
    return false;
}

std::shared_ptr<AreaTreeNode> AreaTreeNode::findChild(int areaId)
{
    for (auto& child : children) {
        if (child->area.id() == areaId) {
            return child;
        }
    }
    return nullptr;
}

QList<std::shared_ptr<AreaTreeNode>> AreaTreeNode::getAllDescendants()
{
    QList<std::shared_ptr<AreaTreeNode>> descendants;
    
    for (auto& child : children) {
        descendants.append(child);
        
        // 递归获取子节点的后代
        auto childDescendants = child->getAllDescendants();
        descendants.append(childDescendants);
    }
    
    return descendants;
}

QList<int> AreaTreeNode::getPath()
{
    QList<int> path;
    
    // 从当前节点向上遍历到根节点
    AreaTreeNode* current = this;
    while (current) {
        path.prepend(current->area.id());
        current = current->parent;
    }
    
    return path;
}

} // namespace AccessControl 