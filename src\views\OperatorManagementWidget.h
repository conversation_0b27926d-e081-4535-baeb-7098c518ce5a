#ifndef OPERATORMANAGEMENTWIDGET_H
#define OPERATORMANAGEMENTWIDGET_H

#include <QWidget>
#include <QTableWidget>
#include <QLineEdit>
#include <QComboBox>
#include <QPushButton>
#include <QLabel>
#include <QFrame>
#include <QMessageBox>
#include <QFileDialog>
#include <memory>
#include <vector>
#include "../models/Operator.h"
#include "../database/dao/OperatorDao.h"

namespace Ui {
class OperatorManagementWidget;
}

namespace AccessControl {

class IDatabaseProvider;
class DepartmentDao;

/**
 * @brief 操作员管理界面
 * 用于管理系统登录账户，与门禁卡持有者管理(ConsumerManagementWidget)区分开
 */
class OperatorManagementWidget : public QWidget
{
    Q_OBJECT

public:
    explicit OperatorManagementWidget(std::shared_ptr<IDatabaseProvider> dbProvider, QWidget *parent = nullptr);
    ~OperatorManagementWidget();

private slots:
    // 数据操作
    void loadOperators();
    void refreshOperatorTable();
    
    // 功能按钮
    void onAdd();
    void onEdit();
    void onDelete();
    void onResetPassword();
    void onChangeStatus();
    void onSearch();
    
    // 搜索筛选
    void onFilter();
    void onClearFilter();
    void onUsernameSearchChanged();
    void onRoleFilterChanged();
    
    // 表格操作
    void onTableDoubleClicked(int row, int column);
    void onTableSelectionChanged();

private:
    void setupUi();
    void setupConnections();
    void setupTableHeaders();
    void updateStatusBar();
    void updateButtonStates();
    
    // 数据显示
    void populateOperatorTable(const std::vector<Operator>& operators);
    void fillOperatorRow(int row, const Operator& user);
    QString getRoleText(Operator::Role role);
    QString getStatusText(Operator::Status status);
    QString formatDate(const QDateTime& dateTime);
    
    // 搜索过滤
    std::vector<Operator> filterOperators(const std::vector<Operator>& operators);
    bool matchesSearchCriteria(const Operator& user);
    
    // 获取选中操作员
    std::vector<Operator> getSelectedOperators();
    Operator* getCurrentOperator();

    Ui::OperatorManagementWidget *ui;
    std::shared_ptr<IDatabaseProvider> m_databaseProvider;
    std::unique_ptr<OperatorDao> m_operatorDao;
    
    // 数据缓存
    std::vector<Operator> m_allOperators;
    std::vector<Operator> m_filteredOperators;
    
    // 界面元素
    QTableWidget* m_operatorTable;
    QLineEdit* m_usernameSearchEdit;
    QComboBox* m_roleFilterCombo;
    QLabel* m_totalLabel;
    QLabel* m_statusLabel;
    
    // 按钮组
    QPushButton* m_addButton;
    QPushButton* m_editButton;
    QPushButton* m_deleteButton;
    QPushButton* m_resetPasswordButton;
    QPushButton* m_changeStatusButton;
    QPushButton* m_searchButton;
    QPushButton* m_filterButton;
    QPushButton* m_clearFilterButton;
};

} // namespace AccessControl

#endif // OPERATORMANAGEMENTWIDGET_H 