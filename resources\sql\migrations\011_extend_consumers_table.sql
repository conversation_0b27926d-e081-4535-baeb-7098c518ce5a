-- 迁移文件：011_extend_consumers_table.sql
-- 描述：扩展consumers表，添加基本信息的缺失字段
-- 创建时间：2024-12-19
-- 作者：AI Assistant

-- ========== 扩展consumers表（基本信息） ==========
ALTER TABLE consumers ADD COLUMN gender INTEGER DEFAULT 0;           -- 性别：0-未知, 1-男, 2-女
ALTER TABLE consumers ADD COLUMN work_phone VARCHAR(20);             -- 工作电话
ALTER TABLE consumers ADD COLUMN home_phone VARCHAR(20);             -- 家庭电话

-- ========== 创建consumers_extended表（扩展信息） ==========
CREATE TABLE IF NOT EXISTS consumers_extended (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    consumer_id INTEGER UNIQUE NOT NULL,     -- 关联consumers表的ID
    
    -- 个人信息
    nation VARCHAR(50),                       -- 民族
    religion VARCHAR(50),                     -- 宗教
    birthplace VARCHAR(100),                  -- 籍贯
    birth_date DATE,                          -- 出生年月
    marital_status INTEGER DEFAULT 0,        -- 婚姻状态：0-未知, 1-未婚, 2-已婚, 3-离异, 4-丧偶
    political_status VARCHAR(50),            -- 政治面貌
    education_level INTEGER DEFAULT 0,       -- 学历：0-未知, 1-小学, 2-初中, 3-高中, 4-中专, 5-大专, 6-本科, 7-硕士, 8-博士
    english_name VARCHAR(100),               -- 英文名
    
    -- 工作信息
    company VARCHAR(200),                     -- 单位
    job_title VARCHAR(100),                   -- 职称
    technical_level VARCHAR(100),            -- 技术等级
    entry_date DATE,                          -- 入职时间
    leave_date DATE,                          -- 离职时间
    
    -- 证件信息
    certificate_name VARCHAR(100),           -- 证件名称
    certificate_number VARCHAR(50),          -- 证件号
    social_security_number VARCHAR(50),      -- 社保号
    
    -- 联系信息
    email VARCHAR(100),                       -- 电子邮箱
    address VARCHAR(200),                     -- 通讯地址
    postal_code VARCHAR(10),                  -- 邮编
    
    -- 其他信息
    remarks TEXT,                             -- 备注
    
    -- 时间信息
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (consumer_id) REFERENCES consumers(id) ON DELETE CASCADE
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_consumers_extended_consumer_id ON consumers_extended(consumer_id);
CREATE INDEX IF NOT EXISTS idx_consumers_extended_nation ON consumers_extended(nation);
CREATE INDEX IF NOT EXISTS idx_consumers_extended_education_level ON consumers_extended(education_level);
CREATE INDEX IF NOT EXISTS idx_consumers_extended_marital_status ON consumers_extended(marital_status);

-- 创建触发器，在更新consumers_extended时自动更新updated_at字段
CREATE TRIGGER IF NOT EXISTS trigger_consumers_extended_update 
    AFTER UPDATE ON consumers_extended
    FOR EACH ROW
BEGIN
    UPDATE consumers_extended SET updated_at = CURRENT_TIMESTAMP WHERE id = OLD.id;
END;

-- ========== 创建consumer_cards表（卡片管理） ==========
CREATE TABLE IF NOT EXISTS consumer_cards (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    consumer_id INTEGER NOT NULL,            -- 消费者ID
    card_number BIGINT NOT NULL,             -- 卡号（数字，范围1-1152921504606846975）
    card_type INTEGER NOT NULL DEFAULT 0,    -- 卡类型：0-IC/ID卡, 1-CPU卡, 2-手机APP, 3-手机NFC, 4-指纹, 5-人脸, 6-手机号, 7-身份证
    is_primary BOOLEAN DEFAULT 0,            -- 是否主卡
    is_biometric BOOLEAN DEFAULT 0,          -- 是否生物识别卡（指纹、人脸）
    status INTEGER NOT NULL DEFAULT 0,       -- 状态：0-正常, 1-挂失, 2-注销
    valid_from DATE DEFAULT (date('now')),   -- 有效期开始
    valid_until DATE DEFAULT '2099-12-31',   -- 有效期结束
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (consumer_id) REFERENCES consumers(id) ON DELETE CASCADE,
    UNIQUE(card_number)  -- 卡号全局唯一
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_consumer_cards_consumer_id ON consumer_cards(consumer_id);
CREATE INDEX IF NOT EXISTS idx_consumer_cards_card_number ON consumer_cards(card_number);
CREATE INDEX IF NOT EXISTS idx_consumer_cards_card_type ON consumer_cards(card_type);
CREATE INDEX IF NOT EXISTS idx_consumer_cards_status ON consumer_cards(status);
CREATE INDEX IF NOT EXISTS idx_consumer_cards_is_primary ON consumer_cards(is_primary);
CREATE INDEX IF NOT EXISTS idx_consumer_cards_is_biometric ON consumer_cards(is_biometric);

-- 创建触发器，在更新consumer_cards时自动更新updated_at字段
CREATE TRIGGER IF NOT EXISTS trigger_consumer_cards_update 
    AFTER UPDATE ON consumer_cards
    FOR EACH ROW
BEGIN
    UPDATE consumer_cards SET updated_at = CURRENT_TIMESTAMP WHERE id = OLD.id;
END;

-- ========== 创建consumer_biometrics表（生物识别数据） ==========
CREATE TABLE IF NOT EXISTS consumer_biometrics (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    consumer_id INTEGER NOT NULL,            -- 消费者ID
    card_id INTEGER NOT NULL,                -- 关联的卡片ID
    biometric_type INTEGER NOT NULL,         -- 生物识别类型：1-指纹, 2-人脸
    finger_type INTEGER,                     -- 指纹类型（仅当biometric_type=1时）：1-右手拇指, 2-右手食指, 3-右手中指, 6-左手拇指, 7-左手食指, 8-左手中指
    biometric_data BLOB,                     -- 生物识别数据
    template_data BLOB,                      -- 模板数据
    quality_score INTEGER DEFAULT 0,         -- 质量评分（0-100）
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (consumer_id) REFERENCES consumers(id) ON DELETE CASCADE,
    FOREIGN KEY (card_id) REFERENCES consumer_cards(id) ON DELETE CASCADE
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_consumer_biometrics_consumer_id ON consumer_biometrics(consumer_id);
CREATE INDEX IF NOT EXISTS idx_consumer_biometrics_card_id ON consumer_biometrics(card_id);
CREATE INDEX IF NOT EXISTS idx_consumer_biometrics_type ON consumer_biometrics(biometric_type);

-- 创建触发器，在更新consumer_biometrics时自动更新updated_at字段
CREATE TRIGGER IF NOT EXISTS trigger_consumer_biometrics_update 
    AFTER UPDATE ON consumer_biometrics
    FOR EACH ROW
BEGIN
    UPDATE consumer_biometrics SET updated_at = CURRENT_TIMESTAMP WHERE id = OLD.id;
END;

-- ========== 创建consumer_photos表（照片数据） ==========
CREATE TABLE IF NOT EXISTS consumer_photos (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    consumer_id INTEGER UNIQUE NOT NULL,     -- 消费者ID（每人只能有一张照片）
    photo_data BLOB NOT NULL,                -- 照片数据
    photo_format VARCHAR(10) DEFAULT 'JPEG', -- 照片格式
    photo_size INTEGER DEFAULT 0,            -- 照片大小（字节）
    width INTEGER DEFAULT 0,                 -- 宽度
    height INTEGER DEFAULT 0,                -- 高度
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (consumer_id) REFERENCES consumers(id) ON DELETE CASCADE
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_consumer_photos_consumer_id ON consumer_photos(consumer_id);

-- 创建触发器，在更新consumer_photos时自动更新updated_at字段
CREATE TRIGGER IF NOT EXISTS trigger_consumer_photos_update 
    AFTER UPDATE ON consumer_photos
    FOR EACH ROW
BEGIN
    UPDATE consumer_photos SET updated_at = CURRENT_TIMESTAMP WHERE id = OLD.id;
END; 