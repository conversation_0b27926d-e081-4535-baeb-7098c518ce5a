#ifndef LOGINWINDOW_H
#define LOGINWINDOW_H

#include <QDialog>
#include <QLabel>
#include <QLineEdit>
#include <QPushButton>
#include <QCheckBox>
#include <QComboBox>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QGroupBox>
#include <QProgressBar>
#include <QTimer>
#include <QPropertyAnimation>
#include <QGraphicsOpacityEffect>
#include <QSettings>
#include <memory>

#include "../database/IDatabaseProvider.h"
#include "../models/Operator.h"
#include "../database/dao/OperatorDao.h"

namespace AccessControl {

/**
 * @brief 登录窗口类
 * 提供用户登录、数据库连接、记住密码等功能
 */
class LoginWindow : public QDialog {
    Q_OBJECT

public:
    explicit LoginWindow(QWidget *parent = nullptr);
    ~LoginWindow();

    /**
     * @brief 获取数据库提供者
     * @return 数据库提供者
     */
    std::shared_ptr<IDatabaseProvider> getDatabaseProvider() const { return m_databaseProvider; }
    Operator getCurrentOperator() const { return m_currentOperator; }

    /**
     * @brief 设置数据库配置
     * @param config 数据库配置
     */
    void setDatabaseConfig(const DatabaseConfig& config);

public slots:
    /**
     * @brief 显示登录窗口
     */
    void showLogin();

private slots:
    /**
     * @brief 处理登录按钮点击
     */
    void handleLogin();
    
    /**
     * @brief 处理数据库配置按钮点击
     */
    void handleDatabaseConfig();
    
    /**
     * @brief 处理记住密码复选框变化
     */
    void handleRememberPassword(bool checked);
    
    /**
     * @brief 处理自动登录复选框变化
     */
    void handleAutoLogin(bool checked);
    
    /**
     * @brief 用户名输入框内容变化
     */
    void onUsernameChanged();
    
    /**
     * @brief 密码输入框内容变化
     */
    void onPasswordChanged();
    
    /**
     * @brief 登录超时处理
     */
    void onLoginTimeout();
    
    /**
     * @brief 淡入动画完成
     */
    void onFadeInFinished();

private:
    // UI组件
    QHBoxLayout* m_mainLayout;  // 改为水平布局
    QWidget* m_centerWidget;     // 保留但不再使用
    QVBoxLayout* m_centerLayout; // 现在用于右侧面板
    
    // 标题区域
    QLabel* m_titleLabel;
    QLabel* m_subtitleLabel;
    
    // 数据库连接区域
    QGroupBox* m_dbGroup;
    QHBoxLayout* m_dbLayout;
    QLabel* m_dbStatusLabel;
    QPushButton* m_dbConfigBtn;
    
    // 登录表单区域
    QGroupBox* m_loginGroup;
    QGridLayout* m_formLayout;
    QLabel* m_usernameLabel;
    QLineEdit* m_usernameEdit;
    QLabel* m_passwordLabel;
    QLineEdit* m_passwordEdit;
    QCheckBox* m_rememberCheck;
    QCheckBox* m_autoLoginCheck;
    
    // 按钮区域
    QHBoxLayout* m_buttonLayout;
    QPushButton* m_loginBtn;
    QPushButton* m_exitBtn;
    
    // 状态区域
    QLabel* m_statusLabel;
    QProgressBar* m_progressBar;
    
    // 数据和逻辑
    std::shared_ptr<IDatabaseProvider> m_databaseProvider;
    std::unique_ptr<OperatorDao> m_operatorDao;
    Operator m_currentOperator;
    DatabaseConfig m_dbConfig;
    QSettings* m_settings;
    QTimer* m_loginTimer;
    
    // 动画效果
    QPropertyAnimation* m_fadeAnimation;
    QGraphicsOpacityEffect* m_opacityEffect;
    
    // 状态
    bool m_isConnecting;
    bool m_isLoggedIn;
    int m_loginAttempts;
    
    /**
     * @brief 初始化UI界面
     */
    void initializeUI();
    
    /**
     * @brief 初始化样式
     */
    void initializeStyles();
    
    /**
     * @brief 初始化连接
     */
    void initializeConnections();
    
    /**
     * @brief 初始化数据库
     */
    void initializeDatabase();
    
    /**
     * @brief 加载保存的设置
     */
    void loadSettings();
    
    /**
     * @brief 保存设置
     */
    void saveSettings();
    
    /**
     * @brief 连接数据库
     * @return 连接是否成功
     */
    bool connectToDatabase();
    
    /**
     * @brief 验证表单输入
     * @return 验证是否通过
     */
    bool validateInput();
    
    /**
     * @brief 执行用户认证
     * @return 认证是否成功
     */
    bool authenticateUser();
    
    /**
     * @brief 更新数据库连接状态
     * @param connected 是否已连接
     * @param message 状态消息
     */
    void updateDatabaseStatus(bool connected, const QString& message = QString());
    
    /**
     * @brief 更新登录状态
     * @param message 状态消息
     * @param isError 是否为错误
     */
    void updateLoginStatus(const QString& message, bool isError = false);
    
    /**
     * @brief 设置控件启用状态
     * @param enabled 是否启用
     */
    void setControlsEnabled(bool enabled);
    
    /**
     * @brief 显示进度
     * @param show 是否显示
     * @param message 进度消息
     */
    void showProgress(bool show, const QString& message = QString());
    
    /**
     * @brief 启动淡入动画
     */
    void startFadeInAnimation();
    
    /**
     * @brief 检查自动登录
     */
    void checkAutoLogin();
    
    /**
     * @brief 清除保存的密码
     */
    void clearSavedPassword();
    
    /**
     * @brief 加密密码（用于保存）
     * @param password 原始密码
     * @return 加密后的密码
     */
    QString encryptPassword(const QString& password);
    
    /**
     * @brief 解密密码（用于读取）
     * @param encryptedPassword 加密的密码
     * @return 解密后的密码
     */
    QString decryptPassword(const QString& encryptedPassword);
    
    /**
     * @brief 获取客户端IP地址
     * @return IP地址
     */
    QString getClientIP();
    
    // 常量
    static const int MAX_LOGIN_ATTEMPTS = 3;
    static const int LOGIN_TIMEOUT_MS = 10000; // 10秒
};

} // namespace AccessControl

#endif // LOGINWINDOW_H
