#include "AreaManagementWidget.h"
#include <QDebug>
#include <QApplication>
#include <QStyle>
#include <QMessageBox>
#include <QHBoxLayout>
#include <QVBoxLayout>
#include <QGridLayout>
#include <QSplitter>
#include <QTreeWidget>
#include <QTreeWidgetItem>
#include <QLineEdit>
#include <QPushButton>
#include <QComboBox>
#include <QTextEdit>
#include <QSpinBox>
#include <QLabel>
#include <QGroupBox>
#include <QMenu>
#include <QAction>
#include <QHeaderView>
#include <QAbstractItemView>
#include <QFileDialog>
#include <QStandardPaths>
#include <QDir>
#include <QProcess>
#include <QProgressDialog>
#include <QTextStream>
#include <QFile>
#include <QFileInfo>
#include <QStringConverter>

namespace AccessControl {

AreaManagementWidget::AreaManagementWidget(std::shared_ptr<IDatabaseProvider> dbProvider, 
                                         QWidget *parent)
    : QWidget(parent)
    , m_dbProvider(dbProvider)
    , m_isEditMode(false)
{
    // 初始化数据访问对象
    m_areaDao = std::make_unique<AreaDao>(m_dbProvider);
    
    // 初始化UI
    initializeUI();
    
    // 连接信号槽
    connectSignals();
    
    // 加载数据
    refreshAreas();
    
    // 设置窗口属性
    setWindowTitle("区域管理");
    resize(1000, 700);
}

AreaManagementWidget::~AreaManagementWidget()
{
}

void AreaManagementWidget::refreshAreas()
{
    loadAreaTree();
    
    // 更新父区域下拉框
    m_comboParent->clear();
    m_comboParent->addItem("无（根区域）", 0);
    
    QList<Area> areas = m_areaDao->findActive();
    for (const Area& area : areas) {
        m_comboParent->addItem(area.fullPath().isEmpty() ? area.name() : area.fullPath(), area.id());
    }
}

// ========== 私有方法：UI初始化 ==========

void AreaManagementWidget::initializeUI()
{
    // 创建主布局
    m_mainLayout = new QHBoxLayout(this);
    m_mainLayout->setContentsMargins(5, 5, 5, 5);
    m_mainLayout->setSpacing(5);
    
    // 创建分割器
    m_splitter = new QSplitter(Qt::Horizontal);
    m_splitter->setChildrenCollapsible(false);
    
    // 初始化左右面板
    initializeLeftPanel();
    initializeRightPanel();
    
    // 设置分割器比例
    m_splitter->setSizes({400, 600});
    
    m_mainLayout->addWidget(m_splitter);
}

void AreaManagementWidget::initializeLeftPanel()
{
    m_leftPanel = new QWidget();
    m_leftLayout = new QVBoxLayout(m_leftPanel);
    m_leftLayout->setContentsMargins(0, 0, 0, 0);
    m_leftLayout->setSpacing(5);
    
    // 初始化工具栏
    initializeToolbar();
    
    // 搜索区域
    m_searchWidget = new QWidget();
    m_searchLayout = new QHBoxLayout(m_searchWidget);
    m_searchLayout->setContentsMargins(0, 0, 0, 0);
    
    m_searchEdit = new QLineEdit();
    m_searchEdit->setPlaceholderText("搜索区域名称或代码...");
    m_searchEdit->setClearButtonEnabled(true);
    
    m_btnSearch = new QPushButton("搜索");
    m_btnClearSearch = new QPushButton("清空");
    m_btnRefresh = new QPushButton("刷新");
    
    m_searchLayout->addWidget(m_searchEdit);
    m_searchLayout->addWidget(m_btnSearch);
    m_searchLayout->addWidget(m_btnClearSearch);
    m_searchLayout->addWidget(m_btnRefresh);
    
    // 树形视图
    m_treeWidget = new QTreeWidget();
    m_treeWidget->setHeaderLabels({"区域名称", "代码", "状态", "描述"});
    m_treeWidget->setAlternatingRowColors(true);
    m_treeWidget->setSelectionMode(QAbstractItemView::SingleSelection);
    m_treeWidget->setContextMenuPolicy(Qt::CustomContextMenu);
    
    // 设置列宽
    m_treeWidget->header()->setSectionResizeMode(QHeaderView::Interactive);
    m_treeWidget->header()->resizeSection(0, 180); // 区域名称
    m_treeWidget->header()->resizeSection(1, 120); // 代码
    m_treeWidget->header()->resizeSection(2, 80);  // 状态
    m_treeWidget->header()->setStretchLastSection(true);
    
    // 添加到布局
    m_leftLayout->addWidget(m_toolbar);
    m_leftLayout->addWidget(m_searchWidget);
    m_leftLayout->addWidget(m_treeWidget);
    
    m_splitter->addWidget(m_leftPanel);
}

void AreaManagementWidget::initializeToolbar()
{
    m_toolbar = new QWidget();
    m_toolbarLayout = new QHBoxLayout(m_toolbar);
    m_toolbarLayout->setContentsMargins(0, 0, 0, 0);
    
    // 创建工具按钮
    m_btnAdd = new QPushButton("添加区域");
    m_btnAddSub = new QPushButton("添加子区域");
    m_btnEdit = new QPushButton("编辑");
    m_btnDelete = new QPushButton("删除");
    m_btnExpandAll = new QPushButton("展开全部");
    m_btnCollapseAll = new QPushButton("收起全部");
    m_btnImport = new QPushButton("导入");
    m_btnExport = new QPushButton("导出");
    
    // 设置图标（使用系统标准图标）
    m_btnAdd->setIcon(style()->standardIcon(QStyle::SP_FileIcon));
    m_btnAddSub->setIcon(style()->standardIcon(QStyle::SP_DirIcon));
    m_btnEdit->setIcon(style()->standardIcon(QStyle::SP_FileDialogDetailedView));
    m_btnDelete->setIcon(style()->standardIcon(QStyle::SP_TrashIcon));
    
    // 设置按钮状态
    m_btnAddSub->setEnabled(false);
    m_btnEdit->setEnabled(false);
    m_btnDelete->setEnabled(false);
    
    // 添加到布局
    m_toolbarLayout->addWidget(m_btnAdd);
    m_toolbarLayout->addWidget(m_btnAddSub);
    m_toolbarLayout->addWidget(m_btnEdit);
    m_toolbarLayout->addWidget(m_btnDelete);
    m_toolbarLayout->addStretch();
    m_toolbarLayout->addWidget(m_btnExpandAll);
    m_toolbarLayout->addWidget(m_btnCollapseAll);
    m_toolbarLayout->addStretch();
    m_toolbarLayout->addWidget(m_btnImport);
    m_toolbarLayout->addWidget(m_btnExport);
}

void AreaManagementWidget::initializeRightPanel()
{
    m_rightPanel = new QWidget();
    m_rightLayout = new QVBoxLayout(m_rightPanel);
    m_rightLayout->setContentsMargins(0, 0, 0, 0);
    m_rightLayout->setSpacing(10);
    
    // 详细信息表单
    m_detailsGroup = new QGroupBox("区域信息");
    m_detailsLayout = new QGridLayout(m_detailsGroup);
    
    // 创建表单控件
    m_lblName = new QLabel("区域名称 *:");
    m_editName = new QLineEdit();
    m_editName->setMaxLength(100);
    m_editName->setPlaceholderText("请输入区域名称（1-100个字符，支持中英文）");
    
    m_lblCode = new QLabel("区域代码 *:");
    m_editCode = new QLineEdit();
    m_editCode->setMaxLength(20);
    m_editCode->setPlaceholderText("请输入区域代码（1-20个字符，建议英文大写）");
    
    m_lblParent = new QLabel("上级区域:");
    m_comboParent = new QComboBox();
    
    m_lblDescription = new QLabel("区域描述:");
    m_editDescription = new QTextEdit();
    m_editDescription->setMaximumHeight(80);
    m_editDescription->setPlaceholderText("请输入区域描述（可选，不超过500个字符）");
    
    m_lblStatus = new QLabel("状态:");
    m_comboStatus = new QComboBox();
    m_comboStatus->addItem("启用", static_cast<int>(Area::Active));
    m_comboStatus->addItem("禁用", static_cast<int>(Area::Inactive));
    
    m_lblSortOrder = new QLabel("排序:");
    m_spinSortOrder = new QSpinBox();
    m_spinSortOrder->setRange(0, 9999);
    m_spinSortOrder->setSuffix(" (数字越小排序越靠前)");
    
    // 添加到表单布局
    m_detailsLayout->addWidget(m_lblName, 0, 0);
    m_detailsLayout->addWidget(m_editName, 0, 1);
    m_detailsLayout->addWidget(m_lblCode, 0, 2);
    m_detailsLayout->addWidget(m_editCode, 0, 3);
    
    m_detailsLayout->addWidget(m_lblParent, 1, 0);
    m_detailsLayout->addWidget(m_comboParent, 1, 1, 1, 3);
    
    m_detailsLayout->addWidget(m_lblDescription, 2, 0);
    m_detailsLayout->addWidget(m_editDescription, 2, 1, 1, 3);
    
    m_detailsLayout->addWidget(m_lblStatus, 3, 0);
    m_detailsLayout->addWidget(m_comboStatus, 3, 1);
    m_detailsLayout->addWidget(m_lblSortOrder, 3, 2);
    m_detailsLayout->addWidget(m_spinSortOrder, 3, 3);
    
    // 操作按钮
    m_buttonWidget = new QWidget();
    m_buttonLayout = new QHBoxLayout(m_buttonWidget);
    m_buttonLayout->setContentsMargins(0, 0, 0, 0);
    
    m_btnSave = new QPushButton("保存");
    m_btnCancel = new QPushButton("取消");
    
    m_btnSave->setIcon(style()->standardIcon(QStyle::SP_DialogOkButton));
    m_btnCancel->setIcon(style()->standardIcon(QStyle::SP_DialogCancelButton));
    
    m_buttonLayout->addStretch();
    m_buttonLayout->addWidget(m_btnSave);
    m_buttonLayout->addWidget(m_btnCancel);
    
    // 统计信息
    m_statsGroup = new QGroupBox("统计信息");
    m_statsLayout = new QGridLayout(m_statsGroup);
    
    m_lblChildCount = new QLabel("子区域数量: 0");
    m_lblDirectControllerCount = new QLabel("直接控制器: 0");
    m_lblTotalControllerCount = new QLabel("总控制器: 0");
    m_lblCreatedAt = new QLabel("创建时间: -");
    m_lblUpdatedAt = new QLabel("更新时间: -");
    
    m_statsLayout->addWidget(m_lblChildCount, 0, 0);
    m_statsLayout->addWidget(m_lblDirectControllerCount, 0, 1);
    m_statsLayout->addWidget(m_lblTotalControllerCount, 0, 2);
    m_statsLayout->addWidget(m_lblCreatedAt, 1, 0, 1, 2);
    m_statsLayout->addWidget(m_lblUpdatedAt, 1, 2);
    
    // 添加使用说明
    QLabel* helpLabel = new QLabel("📌 填写说明：\n"
                                   "• 区域名称：必填，支持中英文，最多100个字符\n"
                                   "• 区域代码：必填，建议使用英文大写字母，最多20个字符\n"
                                   "• 上级区域：选择该区域的上级区域，根区域请选择\"无\"\n"
                                   "• 排序：数字越小排序越靠前，相同排序按名称排序");
    helpLabel->setStyleSheet("QLabel { color: #666; font-size: 12px; padding: 10px; background-color: #f5f5f5; border-radius: 4px; }");
    helpLabel->setWordWrap(true);
    
    m_rightLayout->addWidget(m_detailsGroup);
    m_rightLayout->addWidget(m_buttonWidget);
    m_rightLayout->addWidget(m_statsGroup);
    m_rightLayout->addWidget(helpLabel);
    m_rightLayout->addStretch();
    
    m_splitter->addWidget(m_rightPanel);
    
    setFormEditMode(false);
}

void AreaManagementWidget::initializeContextMenu()
{
    m_contextMenu = new QMenu(this);
    
    m_actionAdd = new QAction("添加区域", this);
    m_actionAddSub = new QAction("添加子区域", this);
    m_actionEdit = new QAction("编辑", this);
    m_actionDelete = new QAction("删除", this);
    m_actionRefresh = new QAction("刷新", this);
    
    m_contextMenu->addAction(m_actionAdd);
    m_contextMenu->addAction(m_actionAddSub);
    m_contextMenu->addSeparator();
    m_contextMenu->addAction(m_actionEdit);
    m_contextMenu->addAction(m_actionDelete);
    m_contextMenu->addSeparator();
    m_contextMenu->addAction(m_actionRefresh);
}

void AreaManagementWidget::connectSignals()
{
    // 树形视图信号
    connect(m_treeWidget, &QTreeWidget::itemSelectionChanged,
            this, &AreaManagementWidget::onTreeSelectionChanged);
    connect(m_treeWidget, &QTreeWidget::customContextMenuRequested,
            this, &AreaManagementWidget::onShowContextMenu);
    
    // 工具栏按钮信号
    connect(m_btnAdd, &QPushButton::clicked, this, &AreaManagementWidget::onAddArea);
    connect(m_btnAddSub, &QPushButton::clicked, this, &AreaManagementWidget::onAddSubArea);
    connect(m_btnEdit, &QPushButton::clicked, this, &AreaManagementWidget::onEditArea);
    connect(m_btnDelete, &QPushButton::clicked, this, &AreaManagementWidget::onDeleteArea);
    connect(m_btnExpandAll, &QPushButton::clicked, m_treeWidget, &QTreeWidget::expandAll);
    connect(m_btnCollapseAll, &QPushButton::clicked, m_treeWidget, &QTreeWidget::collapseAll);
    connect(m_btnImport, &QPushButton::clicked, this, &AreaManagementWidget::onImportAreas);
    connect(m_btnExport, &QPushButton::clicked, this, &AreaManagementWidget::onExportAreas);
    
    // 搜索信号
    connect(m_btnSearch, &QPushButton::clicked, this, &AreaManagementWidget::onSearchAreas);
    connect(m_btnClearSearch, &QPushButton::clicked, this, &AreaManagementWidget::onClearSearch);
    connect(m_btnRefresh, &QPushButton::clicked, this, &AreaManagementWidget::refreshAreas);
    connect(m_searchEdit, &QLineEdit::returnPressed, this, &AreaManagementWidget::onSearchAreas);
    
    // 表单按钮信号
    connect(m_btnSave, &QPushButton::clicked, this, &AreaManagementWidget::onSaveArea);
    connect(m_btnCancel, &QPushButton::clicked, this, &AreaManagementWidget::onCancelEdit);
    
    // 右键菜单信号
    initializeContextMenu();
    connect(m_actionAdd, &QAction::triggered, this, &AreaManagementWidget::onAddArea);
    connect(m_actionAddSub, &QAction::triggered, this, &AreaManagementWidget::onAddSubArea);
    connect(m_actionEdit, &QAction::triggered, this, &AreaManagementWidget::onEditArea);
    connect(m_actionDelete, &QAction::triggered, this, &AreaManagementWidget::onDeleteArea);
    connect(m_actionRefresh, &QAction::triggered, this, &AreaManagementWidget::refreshAreas);
}

// ========== 槽函数实现 ==========

void AreaManagementWidget::onTreeSelectionChanged()
{
    QTreeWidgetItem* currentItem = m_treeWidget->currentItem();
    
    if (currentItem) {
        int areaId = currentItem->data(0, Qt::UserRole).toInt();
        Area area = m_areaDao->findById(areaId);
        
        if (area.id() > 0) {
            m_currentArea = area;
            showAreaDetails(area);
            updateAreaStats(areaId);
            
            // 更新按钮状态
            m_btnAddSub->setEnabled(true);
            m_btnEdit->setEnabled(true);
            m_btnDelete->setEnabled(m_areaDao->canDelete(areaId));
            
            // 更新右键菜单状态
            m_actionAddSub->setEnabled(true);
            m_actionEdit->setEnabled(true);
            m_actionDelete->setEnabled(m_areaDao->canDelete(areaId));
        }
    } else {
        m_currentArea = Area();
        clearDetailsForm();
        
        // 更新按钮状态
        m_btnAddSub->setEnabled(false);
        m_btnEdit->setEnabled(false);
        m_btnDelete->setEnabled(false);
        
        // 更新右键菜单状态
        m_actionAddSub->setEnabled(false);
        m_actionEdit->setEnabled(false);
        m_actionDelete->setEnabled(false);
    }
}

void AreaManagementWidget::onAddArea()
{
    m_currentArea = Area();
    clearDetailsFormForAdd();
    setFormEditMode(true);
    m_isEditMode = true;
    
    // 设置焦点到名称输入框
    m_editName->setFocus();
}

void AreaManagementWidget::onAddSubArea()
{
    if (m_currentArea.id() <= 0) {
        QMessageBox::information(this, "提示", "请先选择一个区域作为父区域！");
        return;
    }
    
    Area newArea;
    newArea.setParentId(m_currentArea.id());
    m_currentArea = newArea;
    
    clearDetailsFormForAdd();
    
    // 设置父区域
    for (int i = 0; i < m_comboParent->count(); ++i) {
        if (m_comboParent->itemData(i).toInt() == newArea.parentId()) {
            m_comboParent->setCurrentIndex(i);
            break;
        }
    }
    
    setFormEditMode(true);
    m_isEditMode = true;
    
    // 设置焦点到名称输入框
    m_editName->setFocus();
}

void AreaManagementWidget::onEditArea()
{
    if (m_currentArea.id() <= 0) {
        QMessageBox::information(this, "提示", "请先选择要编辑的区域！");
        return;
    }
    
    setFormEditMode(true);
    m_isEditMode = true;
    
    // 设置焦点到名称输入框
    m_editName->setFocus();
}

void AreaManagementWidget::onDeleteArea()
{
    if (m_currentArea.id() <= 0) {
        QMessageBox::information(this, "提示", "请先选择要删除的区域！");
        return;
    }
    
    if (!m_areaDao->canDelete(m_currentArea.id())) {
        QMessageBox::warning(this, "无法删除", "该区域包含子区域或关联的控制器，无法删除！");
        return;
    }
    
    int ret = QMessageBox::question(this, "确认删除", 
                                   QString("确定要删除区域 \"%1\" 吗？\n删除后无法恢复！")
                                   .arg(m_currentArea.name()),
                                   QMessageBox::Yes | QMessageBox::No,
                                   QMessageBox::No);
    
    if (ret == QMessageBox::Yes) {
        if (m_areaDao->deleteArea(m_currentArea.id())) {
            QMessageBox::information(this, "删除成功", "区域已成功删除！");
            refreshAreas();
            clearDetailsForm();
        } else {
            QMessageBox::critical(this, "删除失败", "删除区域时发生错误！");
        }
    }
}

void AreaManagementWidget::onSaveArea()
{
    auto validation = validateForm();
    if (!validation.first) {
        QMessageBox::warning(this, "输入错误", validation.second);
        return;
    }
    
    Area area = getAreaFromForm();
    
    bool success = false;
    if (m_currentArea.id() <= 0) {
        // 新增区域
        int areaId = m_areaDao->createArea(area);
        success = (areaId > 0);
        if (success) {
            m_currentArea = area;
            m_currentArea.setId(areaId);
        }
    } else {
        // 更新区域
        area.setId(m_currentArea.id());
        area.setCreatedAt(m_currentArea.createdAt());
        success = m_areaDao->updateArea(area);
        if (success) {
            m_currentArea = area;
        }
    }
    
    if (success) {
        QMessageBox::information(this, "保存成功", "区域信息保存成功！");
        setFormEditMode(false);
        m_isEditMode = false;
        refreshAreas();
        
        // 选中刚保存的区域
        QTreeWidgetItem* item = findTreeItem(m_currentArea.id());
        if (item) {
            m_treeWidget->setCurrentItem(item);
        }
    } else {
        QMessageBox::critical(this, "保存失败", "保存区域信息时发生错误！");
    }
}

void AreaManagementWidget::onCancelEdit()
{
    setFormEditMode(false);
    m_isEditMode = false;
    
    if (m_currentArea.id() > 0) {
        showAreaDetails(m_currentArea);
    } else {
        clearDetailsForm();
    }
}

void AreaManagementWidget::onShowContextMenu(const QPoint& pos)
{
    QTreeWidgetItem* item = m_treeWidget->itemAt(pos);
    if (!item) {
        return;
    }
    
    m_contextMenu->exec(m_treeWidget->mapToGlobal(pos));
}

void AreaManagementWidget::onSearchAreas()
{
    QString keyword = m_searchEdit->text().trimmed();
    applySearchFilter(keyword);
}

void AreaManagementWidget::onClearSearch()
{
    m_searchEdit->clear();
    // 重新加载数据而不是仅仅显示所有缓存的项目
    refreshAreas();
}

void AreaManagementWidget::onExpandAll()
{
    m_treeWidget->expandAll();
}

void AreaManagementWidget::onCollapseAll()
{
    m_treeWidget->collapseAll();
}

void AreaManagementWidget::onImportAreas()
{
    // 显示文件选择对话框
    QString fileName = QFileDialog::getOpenFileName(
        this,
        "导入区域数据",
        QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation),
        "CSV 文件 (*.csv);;所有文件 (*.*)"
    );
    
    if (fileName.isEmpty()) {
        return;
    }
    
    // 创建进度对话框
    QProgressDialog progressDialog("正在导入区域数据...", "取消", 0, 0, this);
    progressDialog.setWindowModality(Qt::WindowModal);
    progressDialog.setAutoClose(true);
    progressDialog.setAutoReset(true);
    progressDialog.show();
    QApplication::processEvents();
    
    // 执行导入
    auto result = importAreasFromCSV(fileName);
    
    progressDialog.close();
    
    if (result.first) {
        QMessageBox::information(this, "导入成功", result.second);
        refreshAreas();
    } else {
        QMessageBox::critical(this, "导入失败", result.second);
    }
}

void AreaManagementWidget::onExportAreas()
{
    // 显示保存文件对话框
    QString fileName = QFileDialog::getSaveFileName(
        this,
        "导出区域数据",
        QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation) + "/areas.csv",
        "CSV 文件 (*.csv);;所有文件 (*.*)"
    );
    
    if (fileName.isEmpty()) {
        return;
    }
    
    // 创建进度对话框
    QProgressDialog progressDialog("正在导出区域数据...", "取消", 0, 0, this);
    progressDialog.setWindowModality(Qt::WindowModal);
    progressDialog.setAutoClose(true);
    progressDialog.setAutoReset(true);
    progressDialog.show();
    QApplication::processEvents();
    
    // 执行导出
    bool success = exportAreasToCSV(fileName);
    
    progressDialog.close();
    
    if (success) {
        int ret = QMessageBox::question(this, "导出成功", 
                                       QString("区域数据已成功导出到：\n%1\n\n是否打开文件所在目录？").arg(fileName),
                                       QMessageBox::Yes | QMessageBox::No,
                                       QMessageBox::Yes);
        
        if (ret == QMessageBox::Yes) {
            // 打开文件所在目录
            QDir dir(QFileInfo(fileName).absolutePath());
            QString path = QDir::toNativeSeparators(dir.absolutePath());
#ifdef Q_OS_WIN
            QProcess::startDetached("explorer", {"/select,", QDir::toNativeSeparators(fileName)});
#elif defined(Q_OS_MAC)
            QProcess::startDetached("open", {"-R", fileName});
#else
            QProcess::startDetached("xdg-open", {path});
#endif
        }
    } else {
        QMessageBox::critical(this, "导出失败", "导出区域数据时发生错误！");
    }
}

void AreaManagementWidget::showAreaDetails(const Area& area)
{
    m_editName->setText(area.name());
    m_editCode->setText(area.code());
    m_editDescription->setPlainText(area.description());
    
    // 设置状态
    for (int i = 0; i < m_comboStatus->count(); ++i) {
        if (m_comboStatus->itemData(i).toInt() == static_cast<int>(area.status())) {
            m_comboStatus->setCurrentIndex(i);
            break;
        }
    }
    
    // 设置父区域
    for (int i = 0; i < m_comboParent->count(); ++i) {
        if (m_comboParent->itemData(i).toInt() == area.parentId()) {
            m_comboParent->setCurrentIndex(i);
            break;
        }
    }
    
    m_spinSortOrder->setValue(area.sortOrder());
    
    // 更新时间显示
    m_lblCreatedAt->setText(QString("创建时间: %1").arg(area.createdAt().toString("yyyy-MM-dd hh:mm:ss")));
    m_lblUpdatedAt->setText(QString("更新时间: %1").arg(area.updatedAt().toString("yyyy-MM-dd hh:mm:ss")));
}

void AreaManagementWidget::updateAreaStats(int areaId)
{
    if (areaId <= 0) return;
    
    // 获取统计信息
    auto stats = m_areaDao->getAreaStats(areaId);
    
    m_lblChildCount->setText(QString("子区域数量: %1").arg(stats.childCount));
    m_lblDirectControllerCount->setText(QString("直接控制器: %1").arg(stats.directControllerCount));
    m_lblTotalControllerCount->setText(QString("总控制器: %1").arg(stats.totalControllerCount));
}

void AreaManagementWidget::clearDetailsForm()
{
    m_editName->clear();
    m_editCode->clear();
    m_editDescription->clear();
    m_comboStatus->setCurrentIndex(0);
    m_comboParent->setCurrentIndex(0);
    m_spinSortOrder->setValue(0);
    
    // 清空统计信息
    m_lblChildCount->setText("子区域数量: 0");
    m_lblDirectControllerCount->setText("直接控制器: 0");
    m_lblTotalControllerCount->setText("总控制器: 0");
    m_lblCreatedAt->setText("创建时间: -");
    m_lblUpdatedAt->setText("更新时间: -");
}

void AreaManagementWidget::clearDetailsFormForAdd()
{
    m_editName->clear();
    m_editCode->clear();
    m_editDescription->clear();
    m_comboStatus->setCurrentIndex(0); // 默认启用
    // 保持父区域选择不变
    m_spinSortOrder->setValue(0);
    
    // 清空统计信息
    m_lblChildCount->setText("子区域数量: 0");
    m_lblDirectControllerCount->setText("直接控制器: 0");
    m_lblTotalControllerCount->setText("总控制器: 0");
    m_lblCreatedAt->setText("创建时间: -");
    m_lblUpdatedAt->setText("更新时间: -");
}

void AreaManagementWidget::setFormEditMode(bool editMode)
{
    m_editName->setReadOnly(!editMode);
    m_editCode->setReadOnly(!editMode);
    m_editDescription->setReadOnly(!editMode);
    m_comboStatus->setEnabled(editMode);
    m_comboParent->setEnabled(editMode);
    m_spinSortOrder->setReadOnly(!editMode);
    
    m_btnSave->setVisible(editMode);
    m_btnCancel->setVisible(editMode);
}

Area AreaManagementWidget::getAreaFromForm()
{
    Area area;
    
    area.setName(m_editName->text().trimmed());
    area.setCode(m_editCode->text().trimmed());
    area.setDescription(m_editDescription->toPlainText().trimmed());
    area.setStatus(static_cast<Area::Status>(m_comboStatus->currentData().toInt()));
    area.setParentId(m_comboParent->currentData().toInt());
    area.setSortOrder(m_spinSortOrder->value());
    
    return area;
}

QPair<bool, QString> AreaManagementWidget::validateForm()
{
    QString name = m_editName->text().trimmed();
    QString code = m_editCode->text().trimmed();
    
    if (name.isEmpty()) {
        return qMakePair(false, "区域名称不能为空！");
    }
    
    if (code.isEmpty()) {
        return qMakePair(false, "区域代码不能为空！");
    }
    
    if (name.length() > 100) {
        return qMakePair(false, "区域名称不能超过100个字符！");
    }
    
    if (code.length() > 20) {
        return qMakePair(false, "区域代码不能超过20个字符！");
    }
    
    if (m_areaDao->isCodeExists(code, m_currentArea.id())) {
        return qMakePair(false, QString("区域代码 \"%1\" 已存在！").arg(code));
    }
    
    return qMakePair(true, "");
}

QTreeWidgetItem* AreaManagementWidget::findTreeItem(int areaId, QTreeWidgetItem* parentItem)
{
    if (parentItem) {
        // 搜索子项目
        for (int i = 0; i < parentItem->childCount(); ++i) {
            QTreeWidgetItem* child = parentItem->child(i);
            if (child->data(0, Qt::UserRole).toInt() == areaId) {
                return child;
            }
            
            // 递归搜索子项目
            QTreeWidgetItem* found = findTreeItem(areaId, child);
            if (found) {
                return found;
            }
        }
    } else {
        // 搜索顶级项目
        for (int i = 0; i < m_treeWidget->topLevelItemCount(); ++i) {
            QTreeWidgetItem* item = m_treeWidget->topLevelItem(i);
            if (item->data(0, Qt::UserRole).toInt() == areaId) {
                return item;
            }
            
            // 递归搜索子项目
            QTreeWidgetItem* found = findTreeItem(areaId, item);
            if (found) {
                return found;
            }
        }
    }
    
    return nullptr;
}

void AreaManagementWidget::loadAreaTree()
{
    m_treeWidget->clear();
    
    // 获取区域树
    QList<std::shared_ptr<AreaTreeNode>> rootNodes = m_areaDao->getAreaTree();
    
    // 构建树形项目
    buildTreeItems(rootNodes);
    
    // 展开所有节点
    m_treeWidget->expandAll();
}

void AreaManagementWidget::buildTreeItems(const QList<std::shared_ptr<AreaTreeNode>>& nodes, 
                                         QTreeWidgetItem* parentItem)
{
    for (const auto& node : nodes) {
        QTreeWidgetItem* item = new QTreeWidgetItem();
        
        // 设置显示文本
        item->setText(0, node->area.name());
        item->setText(1, node->area.code());
        item->setText(2, node->area.statusText());
        item->setText(3, node->area.description());
        
        // 存储区域ID
        item->setData(0, Qt::UserRole, node->area.id());
        
        // 设置图标
        if (node->children.isEmpty()) {
            item->setIcon(0, style()->standardIcon(QStyle::SP_FileIcon));
        } else {
            item->setIcon(0, style()->standardIcon(QStyle::SP_DirIcon));
        }
        
        // 添加到父项目或根级别
        if (parentItem) {
            parentItem->addChild(item);
        } else {
            m_treeWidget->addTopLevelItem(item);
        }
        
        // 递归处理子节点
        if (!node->children.isEmpty()) {
            buildTreeItems(node->children, item);
        }
    }
}

void AreaManagementWidget::applySearchFilter(const QString& keyword)
{
    if (keyword.isEmpty()) {
        // 显示所有项目
        for (int i = 0; i < m_treeWidget->topLevelItemCount(); ++i) {
            filterTreeItem(m_treeWidget->topLevelItem(i), "");
        }
    } else {
        // 应用过滤
        for (int i = 0; i < m_treeWidget->topLevelItemCount(); ++i) {
            filterTreeItem(m_treeWidget->topLevelItem(i), keyword);
        }
    }
}

bool AreaManagementWidget::filterTreeItem(QTreeWidgetItem* item, const QString& keyword)
{
    if (!item) return false;
    
    bool visible = false;
    
    if (keyword.isEmpty()) {
        visible = true;
    } else {
        // 检查当前项是否匹配
        QString name = item->text(0);
        QString code = item->text(1);
        
        if (name.contains(keyword, Qt::CaseInsensitive) || 
            code.contains(keyword, Qt::CaseInsensitive)) {
            visible = true;
        }
        
        // 检查子项
        for (int i = 0; i < item->childCount(); ++i) {
            if (filterTreeItem(item->child(i), keyword)) {
                visible = true;
            }
        }
    }
    
    item->setHidden(!visible);
    return visible;
}

bool AreaManagementWidget::exportAreasToCSV(const QString& fileName)
{
    QFile file(fileName);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        qDebug() << "Failed to open file for writing:" << fileName;
        return false;
    }
    
    // 写入UTF-8 BOM以确保Excel正确识别编码
    QByteArray bom;
    bom.append(char(0xEF));
    bom.append(char(0xBB));
    bom.append(char(0xBF));
    file.write(bom);
    
    QTextStream out(&file);
    out.setEncoding(QStringConverter::Utf8);
    
    // 写入CSV头部
    out << "区域名称,区域代码,上级区域代码,描述,状态,排序,创建时间,更新时间\n";
    
    // 获取所有区域数据
    QList<Area> areas = m_areaDao->findAll();
    
    // 创建代码到名称的映射，便于查找上级区域名称
    QMap<int, QString> idToCodeMap;
    for (const Area& area : areas) {
        idToCodeMap[area.id()] = area.code();
    }
    
    // 写入数据行
    for (const Area& area : areas) {
        QString parentCode = "";
        if (area.parentId() > 0 && idToCodeMap.contains(area.parentId())) {
            parentCode = idToCodeMap[area.parentId()];
        }
        
        QString statusText = (area.status() == Area::Active) ? "启用" : "禁用";
        
        // 处理可能包含逗号或引号的字段，使用标准CSV转义规则
        auto escapeCSVField = [](const QString& field) -> QString {
            if (field.contains(',') || field.contains('"') || field.contains('\n') || field.contains('\r')) {
                return "\"" + field.trimmed().replace("\"", "\"\"") + "\"";
            }
            return field.trimmed();
        };
        
        out << escapeCSVField(area.name()) << ","
            << escapeCSVField(area.code()) << ","
            << escapeCSVField(parentCode) << ","
            << escapeCSVField(area.description()) << ","
            << escapeCSVField(statusText) << ","
            << area.sortOrder() << ","
            << area.createdAt().toString("yyyy-MM-dd hh:mm:ss") << ","
            << area.updatedAt().toString("yyyy-MM-dd hh:mm:ss") << "\n";
    }
    
    file.close();
    return true;
}

QPair<bool, QString> AreaManagementWidget::importAreasFromCSV(const QString& fileName)
{
    QFile file(fileName);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        return qMakePair(false, QString("无法打开文件：%1").arg(fileName));
    }
    
    // 读取所有内容并检测BOM
    QByteArray fileContent = file.readAll();
    file.close();
    
    // 检查并移除UTF-8 BOM
    if (fileContent.startsWith("\xEF\xBB\xBF")) {
        fileContent.remove(0, 3);
    }
    
    // 使用UTF-8解码
    QString content = QString::fromUtf8(fileContent);
    QStringList lines = content.split('\n', Qt::SkipEmptyParts);
    
    QList<Area> importAreas;
    QStringList errors;
    int lineNumber = 0;
    bool hasHeader = false;
    
    for (const QString& line : lines) {
        QString trimmedLine = line.trimmed();
        lineNumber++;
        
        if (trimmedLine.isEmpty()) {
            continue;
        }
        
        // 解析CSV行
        QStringList fields = parseCSVLine(trimmedLine);
        
        // 跳过头部行
        if (!hasHeader) {
            hasHeader = true;
            if (fields.size() >= 2 && 
                (fields[0].contains("区域名称") || fields[0].contains("name", Qt::CaseInsensitive))) {
                continue;
            }
        }
        
        // 验证字段数量
        if (fields.size() < 2) {
            errors.append(QString("第%1行：字段数量不足（至少需要区域名称和代码）").arg(lineNumber));
            continue;
        }
        
        // 解析区域数据
        auto parseResult = parseCSVArea(fields, lineNumber);
        if (parseResult.second) {
            importAreas.append(parseResult.first);
        } else {
            errors.append(QString("第%1行：数据解析失败").arg(lineNumber));
        }
    }
    
    if (importAreas.isEmpty()) {
        return qMakePair(false, "没有找到有效的区域数据。\n错误信息：\n" + errors.join("\n"));
    }
    
    // 验证导入数据
    auto validationResult = validateImportData(importAreas);
    if (!validationResult.first) {
        return qMakePair(false, validationResult.second);
    }
    
    // 开始导入数据
    int successCount = 0;
    int updateCount = 0;
    int skipCount = 0;
    
    // 创建代码到ID的映射
    QMap<QString, int> codeToIdMap;
    QList<Area> existingAreas = m_areaDao->findAll();
    for (const Area& area : existingAreas) {
        codeToIdMap[area.code()] = area.id();
    }
    
    // 按层级顺序处理区域（先处理根区域，再处理子区域）
    QList<Area> sortedAreas = sortAreasByLevel(importAreas);
    
    for (Area area : sortedAreas) {
        // 处理父区域关系
        if (!area.fullPath().isEmpty()) {
            QString parentCode = area.fullPath();
            if (codeToIdMap.contains(parentCode)) {
                area.setParentId(codeToIdMap[parentCode]);
                qDebug() << "导入CSV：设置区域" << area.code() << "的上级区域ID为：" << area.parentId();
            } else {
                qDebug() << "导入CSV：未找到上级区域" << parentCode << "，设置为根区域";
                area.setParentId(0);
            }
        }
        
        // 清空临时存储的父代码
        area.setFullPath("");
        
        if (codeToIdMap.contains(area.code())) {
            // 更新现有区域
            area.setId(codeToIdMap[area.code()]);
            if (m_areaDao->updateArea(area)) {
                updateCount++;
                successCount++;
            }
        } else {
            // 创建新区域
            int areaId = m_areaDao->createArea(area);
            if (areaId > 0) {
                codeToIdMap[area.code()] = areaId;
                successCount++;
            }
        }
    }
    
    QString resultMessage = QString("导入完成！\n成功处理：%1 个区域\n其中更新：%2 个\n新增：%3 个")
                           .arg(successCount).arg(updateCount).arg(successCount - updateCount);
    
    if (!errors.isEmpty()) {
        resultMessage += "\n\n错误信息：\n" + errors.join("\n");
    }
    
    return qMakePair(true, resultMessage);
}

QPair<bool, QString> AreaManagementWidget::validateImportData(const QList<Area>& areas)
{
    QStringList errors;
    QSet<QString> codes;
    
    for (const Area& area : areas) {
        // 检查必填字段
        if (area.name().trimmed().isEmpty()) {
            errors.append(QString("区域名称不能为空（代码：%1）").arg(area.code()));
        }
        
        if (area.code().trimmed().isEmpty()) {
            errors.append(QString("区域代码不能为空（名称：%1）").arg(area.name()));
        }
        
        // 检查代码重复
        if (codes.contains(area.code())) {
            errors.append(QString("区域代码重复：%1").arg(area.code()));
        } else {
            codes.insert(area.code());
        }
        
        // 检查字段长度
        if (area.name().length() > 100) {
            errors.append(QString("区域名称过长（%1），最多100个字符").arg(area.code()));
        }
        
        if (area.code().length() > 20) {
            errors.append(QString("区域代码过长（%1），最多20个字符").arg(area.code()));
        }
    }
    
    if (!errors.isEmpty()) {
        return qMakePair(false, "数据验证失败：\n" + errors.join("\n"));
    }
    
    return qMakePair(true, "数据验证通过");
}

QPair<Area, bool> AreaManagementWidget::parseCSVArea(const QStringList& fields, int lineNumber)
{
    Area area;
    
    if (fields.size() < 2) {
        return qMakePair(area, false);
    }
    
    try {
        // 区域名称（必填）
        area.setName(fields[0].trimmed());
        
        // 区域代码（必填）
        area.setCode(fields[1].trimmed());
        
        // 上级区域代码（可选）- 临时存储在fullPath中
        if (fields.size() > 2 && !fields[2].trimmed().isEmpty()) {
            area.setFullPath(fields[2].trimmed());
            qDebug() << "解析CSV：区域" << area.code() << "的上级区域代码为：" << fields[2].trimmed();
        } else {
            qDebug() << "解析CSV：区域" << area.code() << "为根区域（无上级）";
        }
        
        // 描述（可选）
        if (fields.size() > 3) {
            area.setDescription(fields[3].trimmed());
        }
        
        // 状态（可选，默认启用）
        if (fields.size() > 4) {
            QString status = fields[4].trimmed();
            if (status == "禁用" || status == "0" || status.toLower() == "disabled") {
                area.setStatus(Area::Inactive);
            } else {
                area.setStatus(Area::Active);
            }
        } else {
            area.setStatus(Area::Active);
        }
        
        // 排序（可选，默认0）
        if (fields.size() > 5) {
            bool ok;
            int sortOrder = fields[5].trimmed().toInt(&ok);
            if (ok) {
                area.setSortOrder(sortOrder);
            }
        }
        
        // 设置默认时间
        QDateTime now = QDateTime::currentDateTime();
        area.setCreatedAt(now);
        area.setUpdatedAt(now);
        
        return qMakePair(area, true);
        
    } catch (...) {
        return qMakePair(area, false);
    }
}

QStringList AreaManagementWidget::parseCSVLine(const QString& line)
{
    QStringList fields;
    QString current;
    bool inQuotes = false;
    
    for (int i = 0; i < line.length(); ++i) {
        QChar c = line[i];
        
        if (c == '"') {
            if (inQuotes && i + 1 < line.length() && line[i + 1] == '"') {
                // 转义的引号
                current += '"';
                ++i; // 跳过下一个引号
            } else {
                // 切换引号状态
                inQuotes = !inQuotes;
            }
        } else if (c == ',' && !inQuotes) {
            // 字段分隔符
            fields.append(current.trimmed());
            current.clear();
        } else {
            current += c;
        }
    }
    
    // 添加最后一个字段
    fields.append(current.trimmed());
    
    return fields;
}

QList<Area> AreaManagementWidget::sortAreasByLevel(const QList<Area>& areas)
{
    QList<Area> sortedAreas;
    QList<Area> remainingAreas = areas;
    
    // 先添加根区域（没有父区域的）
    for (auto it = remainingAreas.begin(); it != remainingAreas.end(); ) {
        if (it->fullPath().isEmpty()) {
            sortedAreas.append(*it);
            it = remainingAreas.erase(it);
        } else {
            ++it;
        }
    }
    
    // 循环处理剩余区域，直到所有区域都被处理
    while (!remainingAreas.isEmpty()) {
        bool foundParent = false;
        
        for (auto it = remainingAreas.begin(); it != remainingAreas.end(); ) {
            QString parentCode = it->fullPath();
            
            // 检查父区域是否已经在已排序列表中
            bool parentExists = false;
            for (const Area& sortedArea : sortedAreas) {
                if (sortedArea.code() == parentCode) {
                    parentExists = true;
                    break;
                }
            }
            
            if (parentExists) {
                sortedAreas.append(*it);
                it = remainingAreas.erase(it);
                foundParent = true;
            } else {
                ++it;
            }
        }
        
        // 如果没有找到任何父区域，将剩余区域作为根区域处理
        if (!foundParent && !remainingAreas.isEmpty()) {
            for (Area& area : remainingAreas) {
                area.setFullPath(""); // 清空父代码，作为根区域
            }
            sortedAreas.append(remainingAreas);
            break;
        }
    }
    
    return sortedAreas;
}
} 