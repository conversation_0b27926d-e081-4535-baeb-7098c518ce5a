-- 迁移文件：003_create_areas_table.sql
-- 描述：创建区域表
-- 创建时间：2024-07-04
-- 作者：AI Assistant

-- ========== 创建areas表 ==========

CREATE TABLE IF NOT EXISTS areas (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(50),
    parent_id INTEGER DEFAULT -1,
    level INTEGER DEFAULT 1,
    path VARCHAR(500),
    description TEXT,
    manager_id INTEGER,
    manager_name VARCHAR(100),
    contact_info VARCHAR(200),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES areas(id) ON DELETE SET NULL
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_areas_name ON areas(name);
CREATE INDEX IF NOT EXISTS idx_areas_code ON areas(code);
CREATE INDEX IF NOT EXISTS idx_areas_parent_id ON areas(parent_id);
CREATE INDEX IF NOT EXISTS idx_areas_path ON areas(path);

-- 创建触发器，在更新areas时自动更新updated_at字段
CREATE TRIGGER IF NOT EXISTS trigger_areas_update 
    AFTER UPDATE ON areas
    FOR EACH ROW
BEGIN
    UPDATE areas SET updated_at = CURRENT_TIMESTAMP WHERE id = OLD.id;
END;

-- 插入根区域
INSERT OR IGNORE INTO areas (id, name, code, parent_id, level, path, description)
VALUES (1, '总区域', 'ALL', -1, 1, '1', '所有区域的根节点'); 