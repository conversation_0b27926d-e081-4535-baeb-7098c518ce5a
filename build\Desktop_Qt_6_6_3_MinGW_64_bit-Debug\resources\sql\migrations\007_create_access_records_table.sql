-- 迁移文件：007_create_access_records_table.sql
-- 描述：创建门禁记录表
-- 创建时间：2024-07-10
-- 作者：AI Assistant

-- ========== 创建access_records表 ==========

CREATE TABLE IF NOT EXISTS access_records (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    card_number VARCHAR(50) NOT NULL,           -- 卡号
    consumer_id INTEGER,                        -- 持卡人ID
    door_id INTEGER,                            -- 门ID
    controller_id INTEGER,                      -- 控制器ID
    event_time TIMESTAMP NOT NULL,              -- 事件时间
    event_type INTEGER NOT NULL,                -- 事件类型：0-刷卡开门，1-按钮开门，2-远程开门，3-常开，4-常闭，5-报警，6-门磁异常
    event_status INTEGER NOT NULL,              -- 事件状态：0-成功，1-失败，2-拒绝，3-超时
    description TEXT,                           -- 事件描述
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (consumer_id) REFERENCES consumers(id) ON DELETE SET NULL,
    FOREIGN KEY (door_id) REFERENCES doors(id) ON DELETE SET NULL,
    FOREIGN KEY (controller_id) REFERENCES controllers(id) ON DELETE SET NULL
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_access_records_card_number ON access_records(card_number);
CREATE INDEX IF NOT EXISTS idx_access_records_consumer_id ON access_records(consumer_id);
CREATE INDEX IF NOT EXISTS idx_access_records_door_id ON access_records(door_id);
CREATE INDEX IF NOT EXISTS idx_access_records_controller_id ON access_records(controller_id);
CREATE INDEX IF NOT EXISTS idx_access_records_event_time ON access_records(event_time);
CREATE INDEX IF NOT EXISTS idx_access_records_event_type ON access_records(event_type);
CREATE INDEX IF NOT EXISTS idx_access_records_event_status ON access_records(event_status); 