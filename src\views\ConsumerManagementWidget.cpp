#include "ConsumerManagementWidget.h"
#include "ui_ConsumerManagementWidget.h"
#include "ConsumerDialog.h"
#include "GlobalSearchDialog.h"
#include "CardLineEdit.h"
#include "../database/dao/DepartmentDao.h"
#include <QTableWidget>
#include <QHeaderView>
#include <QMessageBox>
#include <QDateTime>
#include <QDebug>
#include <QSqlQuery>
#include <QSqlDatabase>
#include <QShortcut>
#include <QKeySequence>
#include <QFileDialog>
#include <QTextStream>
#include <QFile>
#include <QHBoxLayout>
#include <QDateTime>
#include <QInputDialog>

namespace AccessControl {

ConsumerManagementWidget::ConsumerManagementWidget(std::shared_ptr<IDatabaseProvider> dbProvider, QWidget *parent)
    : QWidget(parent),
    ui(new Ui::ConsumerManagementWidget),
    m_databaseProvider(dbProvider),
    m_globalSearchDialog(nullptr)
{
    ui->setupUi(this);

    // 初始化DAO
    m_consumerDao = std::make_unique<ConsumerDao>(m_databaseProvider);
    m_departmentDao = std::make_unique<DepartmentDao>(m_databaseProvider);

    // 获取UI元素引用
    m_consumerTable = ui->consumerTableWidget;
    m_nameSearchEdit = ui->nameSearchEdit;

    // 将cardSearchEdit从UI中的QLineEdit替换为CardLineEdit
    // 首先获取原有QLineEdit的parent和位置
    QLineEdit* originalCardEdit = ui->cardSearchEdit;
    QWidget* parentWidget = originalCardEdit->parentWidget();
    QHBoxLayout* layout = qobject_cast<QHBoxLayout*>(parentWidget->layout());

    // 创建新的CardLineEdit
    m_cardSearchEdit = new CardLineEdit(parentWidget);
    m_cardSearchEdit->setPlaceholderText("输入工号或卡号");

    // 在布局中找到原有cardSearchEdit的位置
    int index = -1;
    for (int i = 0; i < layout->count(); ++i) {
        QLayoutItem* item = layout->itemAt(i);
        if (item && item->widget() == originalCardEdit) {
            index = i;
            break;
        }
    }

    // 移除原有的QLineEdit并添加新的CardLineEdit
    if (index != -1) {
        layout->removeWidget(originalCardEdit);
        originalCardEdit->hide();
        originalCardEdit->deleteLater();
        layout->insertWidget(index, m_cardSearchEdit);
    }

    m_deptSearchCombo = ui->deptSearchCombo;
    m_totalLabel = ui->totalLabel;
    m_statusLabel = ui->statusLabel;

    // 获取按钮引用
    m_batchAddButton = ui->batchAddButton;
    m_addButton = ui->addButton;
    m_editButton = ui->editButton;
    m_deleteButton = ui->deleteButton;
    m_printButton = ui->printButton;
    m_exportButton = ui->exportButton;
    m_importButton = ui->importButton;
    m_lostButton = ui->lostButton;
    m_searchButton = ui->searchButton;
    m_filterButton = ui->filterButton;
    m_clearFilterButton = ui->clearFilterButton;


    // 设置界面
    setupUi();
    setupConnections();

    // 设置快捷键
    m_searchShortcut = new QShortcut(QKeySequence("Ctrl+F"), this);
    connect(m_searchShortcut, &QShortcut::activated, this, &ConsumerManagementWidget::onGlobalSearch);

    // 加载数据
    loadDepartments();
    loadConsumers();
}

ConsumerManagementWidget::~ConsumerManagementWidget()
{
    if (m_globalSearchDialog) {
        m_globalSearchDialog->close();
        delete m_globalSearchDialog;
    }
    delete ui;
}

void ConsumerManagementWidget::setupUi()
{
    setupTableHeaders();

    // 设置表格属性
    m_consumerTable->setAlternatingRowColors(true);
    m_consumerTable->setSelectionBehavior(QAbstractItemView::SelectRows);
    m_consumerTable->setSelectionMode(QAbstractItemView::ExtendedSelection);
    m_consumerTable->setSortingEnabled(true);
    m_consumerTable->horizontalHeader()->setStretchLastSection(true);

    // 设置表格列调整模式
    m_consumerTable->horizontalHeader()->setSectionResizeMode(QHeaderView::Interactive);

    // 更均匀地设置列宽
    m_consumerTable->setColumnWidth(0, 90);   // 工号
    m_consumerTable->setColumnWidth(1, 90);   // 姓名
    m_consumerTable->setColumnWidth(2, 100);  // 卡号
    m_consumerTable->setColumnWidth(3, 70);   // 考勤
    m_consumerTable->setColumnWidth(4, 70);   // 倒班
    m_consumerTable->setColumnWidth(5, 70);   // 门禁
    m_consumerTable->setColumnWidth(6, 90);   // 起始日期
    m_consumerTable->setColumnWidth(7, 90);   // 截止日期
    m_consumerTable->setColumnWidth(8, 100);  // 部门
    m_consumerTable->setColumnWidth(9, 100);  // 手机号
    m_consumerTable->setColumnWidth(10, 120); // 身份证

    // 设置查询输入框的宽度
    m_nameSearchEdit->setFixedWidth(130);      // 姓名输入框
    m_cardSearchEdit->setFixedWidth(160);      // 工号/卡号输入框
    m_deptSearchCombo->setFixedWidth(220);     // 部门下拉框

    // 设置按钮的固定宽度
    m_filterButton->setFixedWidth(80);         // 查询按钮
    m_clearFilterButton->setFixedWidth(80);    // 清空条件按钮

    // 在查询行添加弹性空间，使控件靠左显示
    QHBoxLayout* searchLayout = qobject_cast<QHBoxLayout*>(ui->searchFrame->layout());
    if (searchLayout) {
        searchLayout->addStretch(); // 添加弹性空间到末尾
    }

    updateButtonStates();
}

void ConsumerManagementWidget::setupConnections()
{
    // 功能按钮连接
    connect(m_batchAddButton, &QPushButton::clicked, this, &ConsumerManagementWidget::onBatchAdd);
    connect(m_addButton, &QPushButton::clicked, this, &ConsumerManagementWidget::onAdd);
    connect(m_editButton, &QPushButton::clicked, this, &ConsumerManagementWidget::onEdit);
    connect(m_deleteButton, &QPushButton::clicked, this, &ConsumerManagementWidget::onDelete);
    connect(m_printButton, &QPushButton::clicked, this, &ConsumerManagementWidget::onPrint);
    connect(m_exportButton, &QPushButton::clicked, this, &ConsumerManagementWidget::onExportExcel);
    connect(m_importButton, &QPushButton::clicked, this, &ConsumerManagementWidget::onImportUsers);
    connect(m_lostButton, &QPushButton::clicked, this, &ConsumerManagementWidget::onLost);
    connect(m_searchButton, &QPushButton::clicked, this, &ConsumerManagementWidget::onSearch);

    // 搜索过滤连接
    connect(m_filterButton, &QPushButton::clicked, this, &ConsumerManagementWidget::onFilter);
    connect(m_clearFilterButton, &QPushButton::clicked, this, &ConsumerManagementWidget::onClearFilter);
    connect(m_nameSearchEdit, &QLineEdit::textChanged, this, &ConsumerManagementWidget::onNameSearchChanged);
    connect(m_cardSearchEdit, &QLineEdit::textChanged, this, &ConsumerManagementWidget::onCardSearchChanged);
    connect(m_deptSearchCombo, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &ConsumerManagementWidget::onDeptSearchChanged);

    // 表格连接
    connect(m_consumerTable, &QTableWidget::cellDoubleClicked, this, &ConsumerManagementWidget::onTableDoubleClicked);
    connect(m_consumerTable, &QTableWidget::itemSelectionChanged, this, &ConsumerManagementWidget::onTableSelectionChanged);

    // 搜索框回车键
    connect(m_nameSearchEdit, &QLineEdit::returnPressed, this, &ConsumerManagementWidget::onFilter);
    connect(m_cardSearchEdit, &QLineEdit::returnPressed, this, &ConsumerManagementWidget::onFilter);
}

void ConsumerManagementWidget::setupTableHeaders()
{
    QStringList headers;
    headers << "工号" << "姓名" << "卡号" << "考勤" << "倒班" << "门禁"
            << "起始日期" << "截止日期" << "部门" << "手机号" << "身份证";

    m_consumerTable->setColumnCount(headers.size());
    m_consumerTable->setHorizontalHeaderLabels(headers);
}

void ConsumerManagementWidget::loadDepartments()
{
    m_deptSearchCombo->clear();
    m_deptSearchCombo->addItem("全部部门", -1);

    try {
        // 先更新所有部门的路径（确保多级路径正确）
        m_departmentDao->updateDepartmentPaths(0); // 0表示更新所有部门

        auto departments = m_departmentDao->findActive();
        for (const auto& dept : departments) {
            // 使用fullPath()来显示多级部门结构，如果没有则使用部门名称
            QString displayText = dept.fullPath();
            if (displayText.isEmpty()) {
                displayText = dept.name();
            } else {
                // 确保显示文本使用"\"分隔符
                displayText = displayText.replace('/', '\\');
            }

            m_deptSearchCombo->addItem(displayText, dept.id());
        }
    } catch (const std::exception& e) {
        qDebug() << "Load departments failed:" << e.what();
        m_statusLabel->setText("加载部门信息失败");
    }
}

void ConsumerManagementWidget::loadConsumers()
{
    qDebug() << "ConsumerManagementWidget::loadConsumers: Starting to load consumers...";
    try {
        m_allConsumers = m_consumerDao->getAllConsumers();
        qDebug() << "ConsumerManagementWidget::loadConsumers: Retrieved" << m_allConsumers.size() << "consumers from database";
        m_filteredConsumers = m_allConsumers;
        populateConsumerTable(m_filteredConsumers);
        updateStatusBar();
        m_statusLabel->setText("门禁卡持有者数据加载完成");
        qDebug() << "ConsumerManagementWidget::loadConsumers: Consumer loading completed successfully";
    } catch (const std::exception& e) {
        qDebug() << "Load consumers failed:" << e.what();
        m_statusLabel->setText("加载门禁卡持有者数据失败");
        QMessageBox::critical(this, "错误", "加载门禁卡持有者数据失败：" + QString(e.what()));
    }
}

void ConsumerManagementWidget::refreshConsumerTable()
{
    loadConsumers();
}

void ConsumerManagementWidget::populateConsumerTable(const std::vector<Consumer>& consumers)
{
    m_consumerTable->setRowCount(0);
    m_consumerTable->setRowCount(consumers.size());

    for (size_t i = 0; i < consumers.size(); ++i) {
        fillConsumerRow(i, consumers[i]);
    }

    updateStatusBar();
}

void ConsumerManagementWidget::fillConsumerRow(int row, const Consumer& consumer)
{
    // 工号
    m_consumerTable->setItem(row, 0, new QTableWidgetItem(consumer.workNumber()));

    // 姓名
    m_consumerTable->setItem(row, 1, new QTableWidgetItem(consumer.getDisplayName()));

    // 卡号 - 获取用户的主卡号
    QString primaryCardNumber = getPrimaryCardNumber(consumer.id());
    m_consumerTable->setItem(row, 2, new QTableWidgetItem(primaryCardNumber));

    // 考勤
    m_consumerTable->setItem(row, 3, new QTableWidgetItem(formatBoolValue(consumer.attendanceEnabled())));

    // 倒班
    m_consumerTable->setItem(row, 4, new QTableWidgetItem(formatBoolValue(consumer.shiftWork())));

    // 门禁
    m_consumerTable->setItem(row, 5, new QTableWidgetItem(formatBoolValue(consumer.accessEnabled())));

    // 起始日期
    m_consumerTable->setItem(row, 6, new QTableWidgetItem(consumer.validFrom().toString("yyyy-MM-dd")));

    // 截止日期
    m_consumerTable->setItem(row, 7, new QTableWidgetItem(consumer.validUntil().toString("yyyy-MM-dd")));

    // 部门
    m_consumerTable->setItem(row, 8, new QTableWidgetItem(getDepartmentName(consumer.departmentId())));

    // 手机号
    m_consumerTable->setItem(row, 9, new QTableWidgetItem(consumer.phoneNumber()));

    // 身份证（隐私保护显示）
    m_consumerTable->setItem(row, 10, new QTableWidgetItem(formatIdNumber(consumer.idNumber())));

    // 存储用户ID到行数据中
    m_consumerTable->item(row, 0)->setData(Qt::UserRole, consumer.id());
}

QString ConsumerManagementWidget::getDepartmentName(int departmentId)
{
    if (departmentId <= 0) return "未分配";

    try {
        // 获取部门的完整路径
        auto departmentPath = m_departmentDao->getDepartmentPath(departmentId);

        if (departmentPath.isEmpty()) {
            return "未知部门";
        }

        // 构建完整路径字符串，用"\"分隔
        QStringList pathNames;
        for (const auto& dept : departmentPath) {
            pathNames.append(dept.name());
        }

        return pathNames.join("\\");

    } catch (const std::exception& e) {
        qDebug() << "getDepartmentName failed:" << e.what();
        return "未知部门";
    } catch (...) {
        qDebug() << "getDepartmentName failed: Unknown error";
        return "未知部门";
    }
}

QString ConsumerManagementWidget::formatDate(const QDateTime& dateTime)
{
    if (dateTime.isValid()) {
        return dateTime.toString("yyyy-MM-dd hh:mm");
    }
    return "-";
}

QString ConsumerManagementWidget::formatBoolValue(bool value)
{
    return value ? "启用" : "禁用";
}

void ConsumerManagementWidget::updateStatusBar()
{
    int total = m_filteredConsumers.size();
    int allTotal = m_allConsumers.size();

    if (total == allTotal) {
        m_totalLabel->setText(QString("总计：%1 条记录").arg(total));
    } else {
        m_totalLabel->setText(QString("显示：%1 条记录 (共 %2 条)").arg(total).arg(allTotal));
    }
}

void ConsumerManagementWidget::updateButtonStates()
{
    bool hasSelection = !getSelectedConsumers().empty();

    m_editButton->setEnabled(hasSelection);
    m_deleteButton->setEnabled(hasSelection);
    m_lostButton->setEnabled(hasSelection);
}

// 功能按钮实现
void ConsumerManagementWidget::onBatchAdd()
{
    m_statusLabel->setText("批量添加功能待实现");
    QMessageBox::information(this, "提示", "批量添加功能正在开发中...");
}

void ConsumerManagementWidget::onAdd()
{
    // 创建用户添加对话框
    ConsumerDialog* userDialog = new ConsumerDialog(m_databaseProvider, this, ConsumerDialog::Mode::Add);

    // 设置为非模态对话框
    userDialog->setWindowModality(Qt::NonModal);

    // 连接对话框的完成信号
    connect(userDialog, &QDialog::accepted, this, [this]() {
        // 刷新表格
        refreshConsumerTable();
        m_statusLabel->setText("添加用户完成");
    });

    connect(userDialog, &QDialog::rejected, this, [this]() {
        m_statusLabel->setText("取消添加用户");
    });

    // 设置对话框属性
    userDialog->setAttribute(Qt::WA_DeleteOnClose);

    // 显示对话框
    userDialog->show();
}

void ConsumerManagementWidget::onEdit()
{
    auto selectedConsumers = getSelectedConsumers();
    if (selectedConsumers.empty()) {
        QMessageBox::warning(this, "警告", "请先选择要编辑的门禁卡持有者！");
        return;
    }

    if (selectedConsumers.size() > 1) {
        QMessageBox::warning(this, "警告", "一次只能编辑一个门禁卡持有者，请选择单个门禁卡持有者！");
        return;
    }

    // 获取选中的门禁卡持有者ID
    int consumerId = selectedConsumers[0].id();

    // 创建门禁卡持有者编辑对话框
    ConsumerDialog* consumerDialog = new ConsumerDialog(m_databaseProvider, this, ConsumerDialog::Mode::Edit, consumerId);

    // 设置为非模态对话框
    consumerDialog->setWindowModality(Qt::NonModal);

    // 连接对话框的完成信号
    connect(consumerDialog, &QDialog::accepted, this, [this]() {
        // 刷新表格
        refreshConsumerTable();
        m_statusLabel->setText("编辑门禁卡持有者完成");
    });

    connect(consumerDialog, &QDialog::rejected, this, [this]() {
        m_statusLabel->setText("取消编辑门禁卡持有者");
    });

    // 设置对话框属性
    consumerDialog->setAttribute(Qt::WA_DeleteOnClose);

    // 显示对话框
    consumerDialog->show();
}

void ConsumerManagementWidget::onDelete()
{
    auto selectedConsumers = getSelectedConsumers();
    if (selectedConsumers.empty()) {
        QMessageBox::warning(this, "警告", "请先选择要删除的门禁卡持有者！");
        return;
    }

    QString message;
    if (selectedConsumers.size() == 1) {
        message = QString("确定要删除门禁卡持有者 \"%1\" 吗？").arg(selectedConsumers[0].getDisplayName());
    } else {
        message = QString("确定要删除选中的 %1 个门禁卡持有者吗？").arg(selectedConsumers.size());
    }

    int ret = QMessageBox::question(this, "确认删除", message,
                                   QMessageBox::Yes | QMessageBox::No);

    if (ret == QMessageBox::Yes) {
        try {
            std::vector<int> consumerIds;
            for (const auto& consumer : selectedConsumers) {
                consumerIds.push_back(consumer.id());
            }

            if (m_consumerDao->batchDelete(consumerIds)) {
                QMessageBox::information(this, "成功", QString("成功删除 %1 个门禁卡持有者").arg(selectedConsumers.size()));
                refreshConsumerTable();
                m_statusLabel->setText("删除门禁卡持有者完成");
            } else {
                QMessageBox::critical(this, "错误", "删除门禁卡持有者失败！");
                m_statusLabel->setText("删除门禁卡持有者失败");
            }
        } catch (const std::exception& e) {
            QMessageBox::critical(this, "错误", QString("删除门禁卡持有者时发生错误：%1").arg(e.what()));
            m_statusLabel->setText("删除门禁卡持有者出错");
        }
    }
}

void ConsumerManagementWidget::onPrint()
{
    m_statusLabel->setText("打印功能待实现");
    QMessageBox::information(this, "提示", "打印功能正在开发中...");
}

void ConsumerManagementWidget::onExport()
{
    m_statusLabel->setText("导出功能待实现");
    QMessageBox::information(this, "提示", "导出Excel功能正在开发中...");
}

void ConsumerManagementWidget::onImport()
{
    m_statusLabel->setText("导入功能待实现");
    QMessageBox::information(this, "提示", "导入门禁卡持有者功能正在开发中...");
}

void ConsumerManagementWidget::onLost()
{
    auto selectedConsumers = getSelectedConsumers();
    if (selectedConsumers.empty()) {
        QMessageBox::warning(this, "警告", "请先选择要挂失的门禁卡持有者！");
        return;
    }

    m_statusLabel->setText("挂失功能待实现");
    QMessageBox::information(this, "提示", "挂失功能正在开发中...");
}

void ConsumerManagementWidget::onSearch()
{
    onGlobalSearch();
}

// 搜索筛选实现
void ConsumerManagementWidget::onFilter()
{
    QString name = m_nameSearchEdit->text().trimmed();
    QString cardNumber = m_cardSearchEdit->text().trimmed();
    int departmentId = m_deptSearchCombo->currentData().toInt();

    if (name.isEmpty() && cardNumber.isEmpty() && departmentId == -1) {
        // 没有筛选条件，显示所有门禁卡持有者
        m_filteredConsumers = m_allConsumers;
    } else {
        // 有筛选条件，调用数据库查询
        try {
            m_filteredConsumers = m_consumerDao->getConsumersWithAdvancedFilter(name, cardNumber, departmentId);
            m_statusLabel->setText("筛选完成");
        } catch (const std::exception& e) {
            qDebug() << "Filter consumers failed:" << e.what();
            m_statusLabel->setText("筛选失败");
            QMessageBox::critical(this, "错误", QString("筛选门禁卡持有者时发生错误：%1").arg(e.what()));
            return;
        }
    }

    populateConsumerTable(m_filteredConsumers);
}

void ConsumerManagementWidget::onClearFilter()
{
    m_nameSearchEdit->clear();
    m_cardSearchEdit->clear();
    m_deptSearchCombo->setCurrentIndex(0);

    m_filteredConsumers = m_allConsumers;
    populateConsumerTable(m_filteredConsumers);
    m_statusLabel->setText("已清除筛选条件");
}

void ConsumerManagementWidget::onNameSearchChanged()
{
    // 实时搜索可以在这里实现，现在先留空
}

void ConsumerManagementWidget::onCardSearchChanged()
{
    // 实时搜索可以在这里实现，现在先留空
}

void ConsumerManagementWidget::onDeptSearchChanged()
{
    // 部门变化时可以自动筛选，现在先留空
}

// 表格操作实现
void ConsumerManagementWidget::onTableDoubleClicked(int row, int column)
{
    Q_UNUSED(column)

    if (row >= 0 && row < m_consumerTable->rowCount()) {
        // 双击编辑用户
        onEdit();
    }
}

void ConsumerManagementWidget::onTableSelectionChanged()
{
    updateButtonStates();
}

// 辅助方法实现
std::vector<Consumer> ConsumerManagementWidget::filterConsumers(const std::vector<Consumer>& consumers)
{
    std::vector<Consumer> filtered;
    for (const auto& consumer : consumers) {
        if (matchesSearchCriteria(consumer)) {
            filtered.push_back(consumer);
        }
    }
    return filtered;
}

bool ConsumerManagementWidget::matchesSearchCriteria(const Consumer& consumer)
{
    QString name = m_nameSearchEdit->text().trimmed();
    QString cardNumber = m_cardSearchEdit->text().trimmed();
    int departmentId = m_deptSearchCombo->currentData().toInt();

    // 姓名匹配
    if (!name.isEmpty()) {
        if (!consumer.getDisplayName().contains(name, Qt::CaseInsensitive)) {
            return false;
        }
    }

    // 卡号匹配
    if (!cardNumber.isEmpty()) {
        if (!consumer.workNumber().contains(cardNumber, Qt::CaseInsensitive)) {
            return false;
        }
    }

    // 部门匹配
    if (departmentId != -1) {
        if (consumer.departmentId() != departmentId) {
            return false;
        }
    }

    return true;
}

std::vector<Consumer> ConsumerManagementWidget::getSelectedConsumers()
{
    std::vector<Consumer> selectedConsumers;
    auto selectedRanges = m_consumerTable->selectedRanges();

    for (const auto& range : selectedRanges) {
        for (int row = range.topRow(); row <= range.bottomRow(); ++row) {
            if (row < m_consumerTable->rowCount()) {
                QTableWidgetItem* item = m_consumerTable->item(row, 0);
                if (item) {
                    int consumerId = item->data(Qt::UserRole).toInt();

                    // 从过滤后的用户列表中找到对应用户
                    for (const auto& consumer : m_filteredConsumers) {
                        if (consumer.id() == consumerId) {
                            selectedConsumers.push_back(consumer);
                            break;
                        }
                    }
                }
            }
        }
    }

    return selectedConsumers;
}

Consumer* ConsumerManagementWidget::getCurrentConsumer()
{
    int currentRow = m_consumerTable->currentRow();
    if (currentRow >= 0 && currentRow < m_consumerTable->rowCount()) {
        QTableWidgetItem* item = m_consumerTable->item(currentRow, 0);
        if (item) {
            int consumerId = item->data(Qt::UserRole).toInt();

            // 从过滤后的用户列表中找到对应用户
            for (auto& consumer : m_filteredConsumers) {
                if (consumer.id() == consumerId) {
                    return &consumer;
                }
            }
        }
    }
    return nullptr;
}

QString ConsumerManagementWidget::getPrimaryCardNumber(int consumerId)
{
    if (!m_databaseProvider || consumerId <= 0) {
        return QString();
    }

    try {
        QSqlDatabase db = m_databaseProvider->getDatabase();
        QSqlQuery query(db);

        // 查询用户的所有正常状态的卡号，主卡优先
        query.prepare(
            "SELECT card_number FROM consumer_cards "
            "WHERE consumer_id = ? AND status = 0 "
            "ORDER BY is_primary DESC, id"
        );
        query.addBindValue(consumerId);

        QStringList cardNumbers;
        if (query.exec()) {
            while (query.next()) {
                QString cardNumber = query.value("card_number").toString();
                if (!cardNumber.isEmpty()) {
                    cardNumbers.append(cardNumber);
                }
            }
        }

        if (!cardNumbers.isEmpty()) {
            // 如果只有一张卡，直接返回
            if (cardNumbers.size() == 1) {
                return cardNumbers.first();
            }
            // 如果有多张卡，用逗号分隔，主卡在前
            return cardNumbers.join(", ");
        }

    } catch (const std::exception& e) {
        qDebug() << "getPrimaryCardNumber: Exception:" << e.what();
    }

    return QString(); // 没有找到卡号时返回空字符串
}

void ConsumerManagementWidget::onGlobalSearch()
{
    if (!m_globalSearchDialog) {
        m_globalSearchDialog = new GlobalSearchDialog(m_databaseProvider, this);
        connect(m_globalSearchDialog, &GlobalSearchDialog::consumerSelected,
                this, &ConsumerManagementWidget::onConsumerSelectedFromSearch);
    }

    // 使用新的showAndClear方法，每次打开都清空历史
    m_globalSearchDialog->showAndClear();
}

void ConsumerManagementWidget::onConsumerSelectedFromSearch(int consumerId)
{
    // 在表格中选中对应的用户
    for (int row = 0; row < m_consumerTable->rowCount(); ++row) {
        QTableWidgetItem* item = m_consumerTable->item(row, 0); // 工号列
        if (item && item->data(Qt::UserRole).toInt() == consumerId) {
            m_consumerTable->selectRow(row);
            m_consumerTable->scrollToItem(item);
            break;
        }
    }

    // 如果当前显示的数据中没有该用户，则重新加载数据
    bool found = false;
    for (const auto& consumer : m_filteredConsumers) {
        if (consumer.id() == consumerId) {
            found = true;
            break;
        }
    }

    if (!found) {
        // 清除筛选条件，显示所有用户
        m_nameSearchEdit->clear();
        m_cardSearchEdit->clear();
        m_deptSearchCombo->setCurrentIndex(0);
        m_filteredConsumers = m_allConsumers;
        populateConsumerTable(m_filteredConsumers);

        // 再次尝试选中用户
        for (int row = 0; row < m_consumerTable->rowCount(); ++row) {
            QTableWidgetItem* item = m_consumerTable->item(row, 0);
            if (item && item->data(Qt::UserRole).toInt() == consumerId) {
                m_consumerTable->selectRow(row);
                m_consumerTable->scrollToItem(item);
                break;
            }
        }
    }
}

QString ConsumerManagementWidget::formatIdNumber(const QString& idNumber)
{
    if (idNumber.isEmpty()) {
        return QString();
    }

    // 身份证号隐私保护：显示前6位和后4位，中间用*替换
    if (idNumber.length() >= 10) {
        QString front = idNumber.left(6);
        QString back = idNumber.right(4);
        int middleLength = idNumber.length() - 10;
        QString middle = QString("*").repeated(middleLength > 0 ? middleLength : 8);
        return front + middle + back;
    } else if (idNumber.length() > 4) {
        // 如果身份证号较短，只显示前2位和后2位
        QString front = idNumber.left(2);
        QString back = idNumber.right(2);
        QString middle = QString("*").repeated(idNumber.length() - 4);
        return front + middle + back;
    } else {
        // 如果身份证号很短，全部用*替换
        return QString("*").repeated(idNumber.length());
    }
}

void ConsumerManagementWidget::onExportExcel()
{
    // 暂时简化实现，避免编译错误
    QMessageBox::information(this, tr("导出功能"), tr("导出功能正在开发中，敬请期待！"));
}

void ConsumerManagementWidget::onImportUsers()
{
    // 暂时简化实现，避免编译错误
    QMessageBox::information(this, tr("导入功能"), tr("导入功能正在开发中，敬请期待！"));
}

} // namespace AccessControl