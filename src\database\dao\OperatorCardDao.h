#ifndef USERCARDDAO_H
#define USERCARDDAO_H

#include "../IDatabaseProvider.h"
#include "../../models/UserCard.h"
#include <QList>
#include <QVariant>
#include <memory>

namespace AccessControl {

/**
 * @brief 用户卡片数据访问对象
 * 负责用户卡片信息的数据库操作
 */
class UserCardDao {
public:
    explicit UserCardDao(std::shared_ptr<IDatabaseProvider> provider);
    ~UserCardDao() = default;

    // ========== 表管理 ==========
    
    /**
     * @brief 创建用户卡片表
     * @return 创建是否成功
     */
    bool createTable();
    
    /**
     * @brief 检查用户卡片表是否存在
     * @return 表是否存在
     */
    bool tableExists();
    
    /**
     * @brief 初始化表结构
     * @return 初始化是否成功
     */
    bool initializeTable();

    // ========== CRUD 操作 ==========
    
    /**
     * @brief 创建用户卡片
     * @param card 用户卡片对象
     * @return 创建是否成功，成功时会设置ID
     */
    bool createUserCard(UserCard& card);
    
    /**
     * @brief 根据ID获取用户卡片
     * @param cardId 卡片ID
     * @return 用户卡片对象
     */
    UserCard getUserCardById(int cardId);
    
    /**
     * @brief 根据用户ID获取所有卡片
     * @param userId 用户ID
     * @return 用户卡片列表
     */
    QList<UserCard> getUserCardsByUserId(int userId);
    
    /**
     * @brief 根据卡号获取用户卡片
     * @param cardNumber 卡号
     * @return 用户卡片对象
     */
    UserCard getUserCardByNumber(const QString& cardNumber);
    
    /**
     * @brief 更新用户卡片
     * @param card 用户卡片对象
     * @return 更新是否成功
     */
    bool updateUserCard(const UserCard& card);
    
    /**
     * @brief 删除用户卡片
     * @param cardId 卡片ID
     * @return 删除是否成功
     */
    bool deleteUserCard(int cardId);
    
    /**
     * @brief 根据用户ID删除所有卡片
     * @param userId 用户ID
     * @return 删除是否成功
     */
    bool deleteUserCardsByUserId(int userId);
    
    /**
     * @brief 获取所有用户卡片
     * @return 用户卡片列表
     */
    QList<UserCard> getAllUserCards();

    // ========== 查询和搜索 ==========
    
    /**
     * @brief 根据卡片类型获取用户卡片
     * @param cardType 卡片类型
     * @return 用户卡片列表
     */
    QList<UserCard> getUserCardsByType(UserCard::CardType cardType);
    
    /**
     * @brief 根据卡片状态获取用户卡片
     * @param status 卡片状态
     * @return 用户卡片列表
     */
    QList<UserCard> getUserCardsByStatus(UserCard::CardStatus status);
    
    /**
     * @brief 获取用户的主卡
     * @param userId 用户ID
     * @return 主卡对象
     */
    UserCard getUserPrimaryCard(int userId);
    
    /**
     * @brief 搜索用户卡片
     * @param keyword 关键词（卡号、备注）
     * @param cardType 卡片类型过滤
     * @param status 卡片状态过滤
     * @param limit 限制数量
     * @param offset 偏移量
     * @return 用户卡片列表
     */
    QList<UserCard> searchUserCards(const QString& keyword = QString(),
                                   const QString& cardType = QString(),
                                   const QString& status = QString(),
                                   int limit = 50,
                                   int offset = 0);

    // ========== 卡片管理 ==========
    
    /**
     * @brief 设置主卡
     * @param cardId 卡片ID
     * @return 设置是否成功
     */
    bool setPrimaryCard(int cardId);
    
    /**
     * @brief 取消主卡
     * @param userId 用户ID
     * @return 取消是否成功
     */
    bool unsetPrimaryCard(int userId);
    
    /**
     * @brief 更新卡片最后使用时间
     * @param cardId 卡片ID
     * @param lastUsed 最后使用时间
     * @return 更新是否成功
     */
    bool updateLastUsed(int cardId, const QDateTime& lastUsed);
    
    /**
     * @brief 检查卡号是否存在
     * @param cardNumber 卡号
     * @param excludeCardId 排除的卡片ID
     * @return 卡号是否存在
     */
    bool cardNumberExists(const QString& cardNumber, int excludeCardId = 0);

    // ========== 统计信息 ==========
    
    /**
     * @brief 获取用户卡片总数
     * @return 总数
     */
    int getUserCardCount();
    
    /**
     * @brief 获取用户卡片统计
     * @param userId 用户ID
     * @return 统计信息映射
     */
    QMap<QString, int> getUserCardStats(int userId);
    
    /**
     * @brief 获取卡片类型分布
     * @return 类型分布映射
     */
    QMap<QString, int> getCardTypeDistribution();

    // ========== 批量操作 ==========
    
    /**
     * @brief 批量创建用户卡片
     * @param cards 用户卡片列表
     * @return 成功创建的数量
     */
    int batchCreateUserCards(const QList<UserCard>& cards);
    
    /**
     * @brief 批量更新用户卡片
     * @param cards 用户卡片列表
     * @return 成功更新的数量
     */
    int batchUpdateUserCards(const QList<UserCard>& cards);

private:
    std::shared_ptr<IDatabaseProvider> m_provider;

    /**
     * @brief 从查询结果构建用户卡片对象
     * @param query 查询结果
     * @return 用户卡片对象
     */
    UserCard buildUserCardFromQuery(const QSqlQuery& query);
    
    /**
     * @brief 获取用户卡片表的SQL创建语句
     * @return SQL语句
     */
    QString getUserCardTableSQL();
    
    /**
     * @brief 获取插入用户卡片的SQL语句
     * @return SQL语句
     */
    QString getInsertUserCardSQL();
    
    /**
     * @brief 获取更新用户卡片的SQL语句
     * @return SQL语句
     */
    QString getUpdateUserCardSQL();
    
    /**
     * @brief 检查数据库提供者是否有效
     * @return 是否有效
     */
    bool isProviderValid();
};

} // namespace AccessControl

#endif // USERCARDDAO_H 