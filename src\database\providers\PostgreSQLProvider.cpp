#include "PostgreSQLProvider.h"
#include <QSqlError>
#include <QSqlRecord>
#include <QUuid>
#include <QDebug>
#include <QDateTime>
#include <QApplication>
#include <QProcess>
#include <QDir>
#include <QStandardPaths>
#include <QJsonDocument>
#include <QJsonObject>
#include <QRegularExpression>
#include <QMutexLocker>

namespace AccessControl {

PostgreSQLProvider::PostgreSQLProvider()
    : m_connectionName(generateConnectionName())
    , m_errorCode(0)
    , m_isolationLevel(ReadCommitted)
    , m_healthCheckTimer(new QTimer())
    , m_reconnectAttempts(0)
{
    clearError();
    
    // 配置健康检查定时器
    m_healthCheckTimer->setInterval(30000); // 30秒检查一次
    // 使用 QObject::connect 的静态方法
    QObject::connect(m_healthCheckTimer, &QTimer::timeout, [this]() {
        healthCheck();
    });
}

PostgreSQLProvider::~PostgreSQLProvider() {
    stopHealthCheck();
    disconnect();
    
    if (m_healthCheckTimer) {
        delete m_healthCheckTimer;
        m_healthCheckTimer = nullptr;
    }
}

bool PostgreSQLProvider::connect(const DatabaseConfig& config) {
    qDebug() << "开始连接PostgreSQL数据库...";
    
    QMutexLocker locker(&m_mutex);
    
    // 保存配置
    m_config = config;
    m_reconnectAttempts = 0;
    
    try {
        // 检查PostgreSQL驱动
        QStringList drivers = QSqlDatabase::drivers();
        if (!drivers.contains("QPSQL")) {
            setErrorUnsafe("QPSQL驱动不可用，请安装PostgreSQL驱动");
            return false;
        }
        
        // 创建数据库连接
        m_database = QSqlDatabase::addDatabase("QPSQL", m_connectionName);
        
        // 设置连接参数
        m_database.setHostName(config.host);
        m_database.setPort(config.port);
        m_database.setDatabaseName(config.database);
        m_database.setUserName(config.username);
        m_database.setPassword(config.password);
        
        // 设置连接选项
        QString options = "connect_timeout=10;";
        if (config.options.contains("sslmode")) {
            options += QString("sslmode=%1;").arg(config.options["sslmode"].toString());
        } else {
            options += "sslmode=prefer;"; // 默认优先使用SSL
        }
        m_database.setConnectOptions(options);
        
        // 打开数据库连接
        if (!m_database.open()) {
            setErrorUnsafe(QString("无法连接PostgreSQL数据库: %1").arg(m_database.lastError().text()),
                          m_database.lastError().nativeErrorCode().toInt());
            return false;
        }
        
        // 测试连接
        if (!testConnectionUnsafe()) {
            disconnectUnsafe();
            return false;
        }
        
        // 初始化数据库设置
        if (!initializeSettingsUnsafe()) {
            disconnectUnsafe();
            return false;
        }
        
        clearErrorUnsafe();
        qDebug() << "PostgreSQL数据库连接成功:" << config.host << ":" << config.port << "/" << config.database;
        
        // 启动健康检查
        startHealthCheck();
        
        return true;
        
    } catch (const std::exception& e) {
        setErrorUnsafe(QString("连接PostgreSQL数据库异常: %1").arg(e.what()));
        return false;
    }
}

void PostgreSQLProvider::disconnect() {
    QMutexLocker locker(&m_mutex);
    stopHealthCheck();
    disconnectUnsafe();
}

bool PostgreSQLProvider::isConnected() const {
    QMutexLocker locker(&m_mutex);
    return isConnectedUnsafe();
}

bool PostgreSQLProvider::reconnect() {
    QMutexLocker locker(&m_mutex);
    
    if (m_reconnectAttempts >= MAX_RECONNECT_ATTEMPTS) {
        setErrorUnsafe("已达到最大重连次数");
        return false;
    }
    
    qDebug() << QString("尝试重连PostgreSQL数据库 (第%1次)").arg(m_reconnectAttempts + 1);
    
    disconnectUnsafe();
    m_reconnectAttempts++;
    
    locker.unlock();
    bool success = connect(m_config);
    locker.relock();
    
    if (success) {
        m_reconnectAttempts = 0;
        qDebug() << "PostgreSQL数据库重连成功";
    }
    
    return success;
}

QSqlDatabase PostgreSQLProvider::getDatabase() {
    QMutexLocker locker(&m_mutex);
    return m_database;
}

QSqlQuery PostgreSQLProvider::query(const QString& sql) {
    QMutexLocker locker(&m_mutex);
    
    if (!isConnectedUnsafe()) {
        setErrorUnsafe("数据库未连接");
        return QSqlQuery();
    }
    
    QSqlQuery q(m_database);
    if (!q.exec(sql)) {
        setErrorUnsafe(QString("查询执行失败: %1").arg(q.lastError().text()),
                      q.lastError().nativeErrorCode().toInt());
    } else {
        clearErrorUnsafe();
    }
    
    return q;
}

bool PostgreSQLProvider::execute(const QString& sql) {
    QMutexLocker locker(&m_mutex);
    return executeUnsafe(sql);
}

QSqlQuery PostgreSQLProvider::prepareQuery(const QString& sql) {
    QMutexLocker locker(&m_mutex);
    
    QSqlQuery q(m_database);
    if (!q.prepare(sql)) {
        setErrorUnsafe(QString("SQL预编译失败: %1").arg(q.lastError().text()),
                      q.lastError().nativeErrorCode().toInt());
    } else {
        clearErrorUnsafe();
    }
    
    return q;
}

QVariant PostgreSQLProvider::lastInsertId() {
    QMutexLocker locker(&m_mutex);
    
    if (!isConnectedUnsafe()) {
        return QVariant();
    }
    
    // PostgreSQL使用RETURNING子句或currval()函数获取最后插入的ID
    QSqlQuery q("SELECT lastval()", m_database);
    if (q.next()) {
        return q.value(0);
    }
    
    return QVariant();
}

int PostgreSQLProvider::numRowsAffected() {
    QMutexLocker locker(&m_mutex);
    
    // 在PostgreSQL中，可以通过查询统计信息获取影响的行数
    QSqlQuery q("SELECT ROW_COUNT()", m_database);
    if (q.next()) {
        return q.value(0).toInt();
    }
    
    return 0;
}

bool PostgreSQLProvider::transaction() {
    QMutexLocker locker(&m_mutex);
    return transactionUnsafe();
}

bool PostgreSQLProvider::commit() {
    QMutexLocker locker(&m_mutex);
    return commitUnsafe();
}

bool PostgreSQLProvider::rollback() {
    QMutexLocker locker(&m_mutex);
    return rollbackUnsafe();
}

QString PostgreSQLProvider::lastError() const {
    QMutexLocker locker(&m_mutex);
    return m_lastError;
}

int PostgreSQLProvider::errorCode() const {
    QMutexLocker locker(&m_mutex);
    return m_errorCode;
}

bool PostgreSQLProvider::hasError() const {
    QMutexLocker locker(&m_mutex);
    return !m_lastError.isEmpty();
}

QString PostgreSQLProvider::databaseType() const {
    return "PostgreSQL";
}

QString PostgreSQLProvider::databaseVersion() const {
    QMutexLocker locker(&m_mutex);
    
    if (!isConnectedUnsafe()) {
        return QString();
    }
    
    QSqlQuery q("SELECT version()", m_database);
    if (q.next()) {
        QString version = q.value(0).toString();
        // 提取版本号
        QRegularExpression rx("PostgreSQL\\s+([\\d\\.]+)");
        QRegularExpressionMatch match = rx.match(version);
        if (match.hasMatch()) {
            return match.captured(1);
        }
        return version;
    }
    
    return QString();
}

QStringList PostgreSQLProvider::tableNames() const {
    QMutexLocker locker(&m_mutex);
    
    if (!isConnectedUnsafe()) {
        return QStringList();
    }
    
    QStringList tables;
    QSqlQuery q("SELECT tablename FROM pg_tables WHERE schemaname = 'public'", m_database);
    
    while (q.next()) {
        tables << q.value(0).toString();
    }
    
    return tables;
}

bool PostgreSQLProvider::tableExists(const QString& tableName) const {
    QMutexLocker locker(&m_mutex);
    
    if (!isConnectedUnsafe()) {
        return false;
    }
    
    QSqlQuery q(m_database);
    q.prepare("SELECT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = ?)");
    q.addBindValue(tableName);
    
    if (q.exec() && q.next()) {
        return q.value(0).toBool();
    }
    
    return false;
}

QStringList PostgreSQLProvider::columnNames(const QString& tableName) const {
    QMutexLocker locker(&m_mutex);
    
    if (!isConnectedUnsafe()) {
        return QStringList();
    }
    
    QStringList columns;
    QSqlQuery q(m_database);
    q.prepare("SELECT column_name FROM information_schema.columns WHERE table_name = ? ORDER BY ordinal_position");
    q.addBindValue(tableName);
    
    if (q.exec()) {
        while (q.next()) {
            columns << q.value(0).toString();
        }
    }
    
    return columns;
}

bool PostgreSQLProvider::executeBatch(const QStringList& sqlList) {
    QMutexLocker locker(&m_mutex);
    
    if (!transactionUnsafe()) {
        return false;
    }
    
    try {
        for (const QString& sql : sqlList) {
            if (!executeUnsafe(sql)) {
                rollbackUnsafe();
                return false;
            }
        }
        
        return commitUnsafe();
        
    } catch (...) {
        rollbackUnsafe();
        return false;
    }
}

bool PostgreSQLProvider::batchInsert(const QString& tableName, 
                                    const QStringList& columns, 
                                    const QList<QVariantList>& values) {
    if (columns.isEmpty() || values.isEmpty()) {
        QMutexLocker locker(&m_mutex);
        setErrorUnsafe("批量插入参数无效");
        return false;
    }
    
    QMutexLocker locker(&m_mutex);
    
    if (!transactionUnsafe()) {
        return false;
    }
    
    try {
        // 使用PostgreSQL的COPY命令或批量INSERT进行优化
        QString sql = QString("INSERT INTO %1 (%2) VALUES (%3)")
                      .arg(tableName)
                      .arg(columns.join(", "))
                      .arg(QString("?").repeated(columns.size()).replace("??", "?, ?"));
        
        QSqlQuery q(m_database);
        if (!q.prepare(sql)) {
            setErrorUnsafe(QString("SQL预编译失败: %1").arg(q.lastError().text()));
            rollbackUnsafe();
            return false;
        }
        
        for (const QVariantList& row : values) {
            // 清除之前的绑定值
            q.bindValue(0, QVariant()); // 重置绑定
            
            // 绑定新的参数
            for (int i = 0; i < row.size() && i < columns.size(); ++i) {
                q.bindValue(i, row[i]);
            }
            
            if (!q.exec()) {
                setErrorUnsafe(QString("批量插入执行失败: %1").arg(q.lastError().text()));
                rollbackUnsafe();
                return false;
            }
        }
        
        return commitUnsafe();
        
    } catch (...) {
        rollbackUnsafe();
        return false;
    }
}

bool PostgreSQLProvider::optimize() {
    QMutexLocker locker(&m_mutex);
    
    // PostgreSQL的优化操作
    return executeUnsafe("VACUUM ANALYZE") && executeUnsafe("REINDEX DATABASE " + m_config.database);
}

bool PostgreSQLProvider::checkIntegrity() {
    // PostgreSQL没有直接的完整性检查命令，但可以检查系统表
    return true; // 简化实现
}

qint64 PostgreSQLProvider::databaseSize() const {
    QMutexLocker locker(&m_mutex);
    
    if (!isConnectedUnsafe()) {
        return 0;
    }
    
    QSqlQuery q(m_database);
    q.prepare("SELECT pg_database_size(?)");
    q.addBindValue(m_config.database);
    
    if (q.exec() && q.next()) {
        return q.value(0).toLongLong();
    }
    
    return 0;
}

bool PostgreSQLProvider::backup(const QString& backupPath) {
    QMutexLocker locker(&m_mutex);
    
    if (!isConnectedUnsafe()) {
        setErrorUnsafe("数据库未连接");
        return false;
    }
    
    // 使用pg_dump进行备份
    QProcess process;
    QStringList arguments;
    
    arguments << "-h" << m_config.host
              << "-p" << QString::number(m_config.port)
              << "-U" << m_config.username
              << "-d" << m_config.database
              << "-f" << backupPath
              << "--verbose";
    
    // 设置密码环境变量
    QProcessEnvironment env = QProcessEnvironment::systemEnvironment();
    env.insert("PGPASSWORD", m_config.password);
    process.setProcessEnvironment(env);
    
    process.start("pg_dump", arguments);
    
    if (!process.waitForFinished(300000)) { // 5分钟超时
        setErrorUnsafe(QString("备份超时或失败: %1").arg(process.errorString()));
        return false;
    }
    
    if (process.exitCode() != 0) {
        setErrorUnsafe(QString("备份失败: %1").arg(QString::fromUtf8(process.readAllStandardError())));
        return false;
    }
    
    clearErrorUnsafe();
    return true;
}

bool PostgreSQLProvider::restore(const QString& backupPath) {
    QMutexLocker locker(&m_mutex);
    
    if (!QFile::exists(backupPath)) {
        setErrorUnsafe(QString("备份文件不存在: %1").arg(backupPath));
        return false;
    }
    
    // 使用psql或pg_restore进行恢复
    QProcess process;
    QStringList arguments;
    
    arguments << "-h" << m_config.host
              << "-p" << QString::number(m_config.port)
              << "-U" << m_config.username
              << "-d" << m_config.database
              << "-f" << backupPath;
    
    // 设置密码环境变量
    QProcessEnvironment env = QProcessEnvironment::systemEnvironment();
    env.insert("PGPASSWORD", m_config.password);
    process.setProcessEnvironment(env);
    
    process.start("psql", arguments);
    
    if (!process.waitForFinished(300000)) { // 5分钟超时
        setErrorUnsafe(QString("恢复超时或失败: %1").arg(process.errorString()));
        return false;
    }
    
    if (process.exitCode() != 0) {
        setErrorUnsafe(QString("恢复失败: %1").arg(QString::fromUtf8(process.readAllStandardError())));
        return false;
    }
    
    clearErrorUnsafe();
    return true;
}

// ========== PostgreSQL特有功能 ==========

bool PostgreSQLProvider::setIsolationLevel(IsolationLevel level) {
    QMutexLocker locker(&m_mutex);
    
    QString sql = QString("SET TRANSACTION ISOLATION LEVEL %1").arg(isolationLevelToString(level));
    if (executeUnsafe(sql)) {
        m_isolationLevel = level;
        return true;
    }
    
    return false;
}

bool PostgreSQLProvider::createPartitionedTable(const QString& baseTableName, 
                                               const QString& partitionType, 
                                               const QString& partitionColumn) {
    QMutexLocker locker(&m_mutex);
    
    QString sql = QString("CREATE TABLE %1 (...) PARTITION BY %2 (%3)")
                  .arg(baseTableName, partitionType, partitionColumn);
    
    // 注意：这里需要完整的表结构定义，这是一个简化版本
    return executeUnsafe(sql);
}

bool PostgreSQLProvider::createPartition(const QString& partitionName, 
                                        const QString& baseTableName, 
                                        const QString& partitionCondition) {
    QMutexLocker locker(&m_mutex);
    
    QString sql = QString("CREATE TABLE %1 PARTITION OF %2 %3")
                  .arg(partitionName, baseTableName, partitionCondition);
    
    return executeUnsafe(sql);
}

bool PostgreSQLProvider::vacuum(const QString& tableName, bool full) {
    QMutexLocker locker(&m_mutex);
    
    QString sql = full ? "VACUUM FULL" : "VACUUM";
    if (!tableName.isEmpty()) {
        sql += " " + tableName;
    }
    
    return executeUnsafe(sql);
}

bool PostgreSQLProvider::reindex(const QString& indexName) {
    QMutexLocker locker(&m_mutex);
    
    QString sql;
    if (indexName.isEmpty()) {
        sql = "REINDEX DATABASE " + m_config.database;
    } else {
        sql = "REINDEX INDEX " + indexName;
    }
    
    return executeUnsafe(sql);
}

bool PostgreSQLProvider::analyze(const QString& tableName) {
    QMutexLocker locker(&m_mutex);
    
    QString sql = "ANALYZE";
    if (!tableName.isEmpty()) {
        sql += " " + tableName;
    }
    
    return executeUnsafe(sql);
}

int PostgreSQLProvider::activeConnections() const {
    QMutexLocker locker(&m_mutex);
    
    if (!isConnectedUnsafe()) {
        return 0;
    }
    
    QSqlQuery q("SELECT count(*) FROM pg_stat_activity WHERE state = 'active'", m_database);
    if (q.next()) {
        return q.value(0).toInt();
    }
    
    return 0;
}

QString PostgreSQLProvider::getDatabaseStats() const {
    QMutexLocker locker(&m_mutex);
    
    if (!isConnectedUnsafe()) {
        return "{}";
    }
    
    QJsonObject stats;
    
    // 获取数据库大小
    QSqlQuery q(m_database);
    q.prepare("SELECT pg_database_size(?)");
    q.addBindValue(m_config.database);
    if (q.exec() && q.next()) {
        stats["database_size"] = q.value(0).toLongLong();
    }
    
    // 获取连接数
    q.exec("SELECT count(*) FROM pg_stat_activity");
    if (q.next()) {
        stats["total_connections"] = q.value(0).toInt();
    }
    
    // 获取活动连接数
    q.exec("SELECT count(*) FROM pg_stat_activity WHERE state = 'active'");
    if (q.next()) {
        stats["active_connections"] = q.value(0).toInt();
    }
    
    return QJsonDocument(stats).toJson(QJsonDocument::Compact);
}

bool PostgreSQLProvider::createExtension(const QString& extensionName) {
    QMutexLocker locker(&m_mutex);
    
    QString sql = QString("CREATE EXTENSION IF NOT EXISTS %1").arg(extensionName);
    return executeUnsafe(sql);
}

bool PostgreSQLProvider::extensionExists(const QString& extensionName) const {
    QMutexLocker locker(&m_mutex);
    
    if (!isConnectedUnsafe()) {
        return false;
    }
    
    QSqlQuery q(m_database);
    q.prepare("SELECT EXISTS (SELECT 1 FROM pg_extension WHERE extname = ?)");
    q.addBindValue(extensionName);
    
    if (q.exec() && q.next()) {
        return q.value(0).toBool();
    }
    
    return false;
}

void PostgreSQLProvider::healthCheck() {
    if (!testConnection()) {
        qWarning() << "PostgreSQL连接健康检查失败，尝试重连...";
        reconnect();
    }
}

// ========== 私有方法实现 ==========

QString PostgreSQLProvider::generateConnectionName() {
    return QString("PostgreSQLConn_%1_%2")
           .arg(QDateTime::currentMSecsSinceEpoch())
           .arg(QUuid::createUuid().toString(QUuid::WithoutBraces));
}

bool PostgreSQLProvider::initializeSettings() {
    QMutexLocker locker(&m_mutex);
    return initializeSettingsUnsafe();
}

void PostgreSQLProvider::setError(const QString& error, int code) {
    QMutexLocker locker(&m_mutex);
    setErrorUnsafe(error, code);
}

void PostgreSQLProvider::clearError() {
    QMutexLocker locker(&m_mutex);
    clearErrorUnsafe();
}

bool PostgreSQLProvider::testConnection() {
    QMutexLocker locker(&m_mutex);
    return testConnectionUnsafe();
}

void PostgreSQLProvider::startHealthCheck() {
    if (!m_healthCheckTimer->isActive()) {
        m_healthCheckTimer->start();
    }
}

void PostgreSQLProvider::stopHealthCheck() {
    if (m_healthCheckTimer->isActive()) {
        m_healthCheckTimer->stop();
    }
}

QString PostgreSQLProvider::isolationLevelToString(IsolationLevel level) const {
    switch (level) {
        case ReadUncommitted: return "READ UNCOMMITTED";
        case ReadCommitted: return "READ COMMITTED";
        case RepeatableRead: return "REPEATABLE READ";
        case Serializable: return "SERIALIZABLE";
        default: return "READ COMMITTED";
    }
}

// ========== 无锁版本方法实现 ==========

bool PostgreSQLProvider::initializeSettingsUnsafe() {
    try {
        // 设置时区
        if (!executeUnsafe("SET timezone = 'UTC'")) {
            qWarning() << "警告：设置时区失败";
        }
        
        // 设置字符编码
        if (!executeUnsafe("SET client_encoding = 'UTF8'")) {
            qWarning() << "警告：设置字符编码失败";
        }
        
        // 设置默认事务隔离级别
        QString sql = QString("SET default_transaction_isolation = '%1'")
                      .arg(isolationLevelToString(m_isolationLevel));
        if (!executeUnsafe(sql)) {
            qWarning() << "警告：设置事务隔离级别失败";
        }
        
        return true;
        
    } catch (const std::exception& e) {
        setErrorUnsafe(QString("初始化设置异常: %1").arg(e.what()));
        return false;
    }
}

void PostgreSQLProvider::setErrorUnsafe(const QString& error, int code) {
    m_lastError = error;
    m_errorCode = code;
    qDebug() << "PostgreSQLProvider错误:" << error;
}

void PostgreSQLProvider::clearErrorUnsafe() {
    m_lastError.clear();
    m_errorCode = 0;
}

bool PostgreSQLProvider::isConnectedUnsafe() const {
    return m_database.isOpen() && m_database.isValid();
}

void PostgreSQLProvider::disconnectUnsafe() {
    if (m_database.isOpen()) {
        m_database.close();
    }
    
    if (QSqlDatabase::contains(m_connectionName)) {
        QSqlDatabase::removeDatabase(m_connectionName);
    }
}

bool PostgreSQLProvider::executeUnsafe(const QString& sql) {
    if (!isConnectedUnsafe()) {
        setErrorUnsafe("数据库未连接");
        return false;
    }
    
    QSqlQuery q(m_database);
    if (!q.exec(sql)) {
        setErrorUnsafe(QString("SQL执行失败: %1").arg(q.lastError().text()),
                      q.lastError().nativeErrorCode().toInt());
        return false;
    }
    
    clearErrorUnsafe();
    return true;
}

bool PostgreSQLProvider::transactionUnsafe() {
    if (!isConnectedUnsafe()) {
        setErrorUnsafe("数据库未连接");
        return false;
    }
    
    if (!m_database.transaction()) {
        setErrorUnsafe(QString("开始事务失败: %1").arg(m_database.lastError().text()));
        return false;
    }
    
    clearErrorUnsafe();
    return true;
}

bool PostgreSQLProvider::commitUnsafe() {
    if (!isConnectedUnsafe()) {
        setErrorUnsafe("数据库未连接");
        return false;
    }
    
    if (!m_database.commit()) {
        setErrorUnsafe(QString("提交事务失败: %1").arg(m_database.lastError().text()));
        return false;
    }
    
    clearErrorUnsafe();
    return true;
}

bool PostgreSQLProvider::rollbackUnsafe() {
    if (!isConnectedUnsafe()) {
        setErrorUnsafe("数据库未连接");
        return false;
    }
    
    if (!m_database.rollback()) {
        setErrorUnsafe(QString("回滚事务失败: %1").arg(m_database.lastError().text()));
        return false;
    }
    
    clearErrorUnsafe();
    return true;
}

bool PostgreSQLProvider::testConnectionUnsafe() {
    if (!isConnectedUnsafe()) {
        return false;
    }
    
    QSqlQuery q("SELECT 1", m_database);
    return q.exec() && q.next();
}

} // namespace AccessControl

#include "PostgreSQLProvider.moc" 