#ifndef DATABASEMIGRATION_H
#define DATABASEMIGRATION_H

#include <QString>
#include <QSqlDatabase>
#include <QList>
#include <vector>
#include <memory>
#include "IDatabaseProvider.h"

namespace AccessControl {

/**
 * @brief 数据库迁移管理器
 * 负责执行数据库迁移脚本
 */
class DatabaseMigration {
public:
    /**
     * @brief 构造函数
     * @param dbProvider 数据库提供者
     */
    explicit DatabaseMigration(std::shared_ptr<IDatabaseProvider> dbProvider);
    
    /**
     * @brief 析构函数
     */
    ~DatabaseMigration();
    
    /**
     * @brief 执行所有待执行的迁移
     * @return 执行成功返回true，失败返回false
     */
    bool runMigrations();
    
    /**
     * @brief 检查特定表是否存在
     * @param tableName 表名
     * @return 存在返回true，不存在返回false
     */
    bool tableExists(const QString& tableName);
    
    /**
     * @brief 创建部门表
     * @return 创建成功返回true，失败返回false
     */
    bool createDepartmentsTable();
    
    /**
     * @brief 创建区域表
     * @return 创建成功返回true，失败返回false
     */
    bool createAreasTable();
    
    /**
     * @brief 创建用户扩展信息表
     * @return 创建成功返回true，失败返回false
     */
    bool createUserProfilesTable();
    
    /**
     * @brief 扩展用户表结构
     * @return 扩展成功返回true，失败返回false
     */
    bool extendUsersTable();
    
    /**
     * @brief 创建用户卡片表
     * @return 创建成功返回true，失败返回false
     */
    bool createUserCardsTable();
    
    /**
     * @brief 创建用户生物识别表
     * @return 创建成功返回true，失败返回false
     */
    bool createUserBiometricTable();
    
    /**
     * @brief 插入示例部门数据
     */
    void insertSampleDepartments();
    
    /**
     * @brief 获取迁移文件列表
     * @return 迁移文件路径列表
     */
    std::vector<QString> getMigrationFiles();

private:
    std::shared_ptr<IDatabaseProvider> m_dbProvider;
    
    /**
     * @brief 执行SQL文件
     * @param sqlFilePath SQL文件路径
     * @return 执行成功返回true，失败返回false
     */
    bool executeSqlFile(const QString& sqlFilePath);
    
    /**
     * @brief 执行SQL语句
     * @param sql SQL语句
     * @return 执行成功返回true，失败返回false
     */
    bool executeSql(const QString& sql);
    
    /**
     * @brief 检查表字段是否存在
     * @param tableName 表名
     * @param columnName 字段名
     * @return 存在返回true，不存在返回false
     */
    bool columnExists(const QString& tableName, const QString& columnName);

    /**
     * @brief 确保默认admin用户存在
     * @return 成功返回true，失败返回false
     */
    bool ensureDefaultAdminUser();

    /**
     * @brief 创建示例用户数据（用于测试）
     * @return 成功返回true，失败返回false
     */
    bool createSampleUsers();
};

} // namespace AccessControl

#endif // DATABASEMIGRATION_H 