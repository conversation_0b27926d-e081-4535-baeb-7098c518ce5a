#ifndef AREAMANAGEMENTWIDGET_H
#define AREAMANAGEMENTWIDGET_H

#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QTreeWidget>
#include <QTreeWidgetItem>
#include <QPushButton>
#include <QLineEdit>
#include <QTextEdit>
#include <QComboBox>
#include <QLabel>
#include <QGroupBox>
#include <QSplitter>
#include <QMessageBox>
#include <QMenu>
#include <QAction>
#include <QHeaderView>
#include <QSpinBox>
#include <QFileDialog>
#include <QStandardPaths>
#include <QProgressDialog>
#include <QApplication>
#include <QTextStream>
#include <QDir>
#include <memory>

#include "../models/Area.h"
#include "../database/dao/AreaDao.h"
#include "../database/IDatabaseProvider.h"

namespace AccessControl {

/**
 * @brief 区域管理界面
 * 提供区域的树形视图和CRUD操作功能
 */
class AreaManagementWidget : public QWidget
{
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param dbProvider 数据库提供者
     * @param parent 父组件
     */
    explicit AreaManagementWidget(std::shared_ptr<IDatabaseProvider> dbProvider, 
                                 QWidget *parent = nullptr);
    
    /**
     * @brief 析构函数
     */
    ~AreaManagementWidget();

    /**
     * @brief 刷新区域数据
     */
    void refreshAreas();

private slots:
    /**
     * @brief 树形视图选择改变
     */
    void onTreeSelectionChanged();
    
    /**
     * @brief 添加区域
     */
    void onAddArea();
    
    /**
     * @brief 添加子区域
     */
    void onAddSubArea();
    
    /**
     * @brief 编辑区域
     */
    void onEditArea();
    
    /**
     * @brief 删除区域
     */
    void onDeleteArea();
    
    /**
     * @brief 保存区域
     */
    void onSaveArea();
    
    /**
     * @brief 取消编辑
     */
    void onCancelEdit();
    
    /**
     * @brief 搜索区域
     */
    void onSearchAreas();
    
    /**
     * @brief 清空搜索
     */
    void onClearSearch();
    
    /**
     * @brief 展开所有节点
     */
    void onExpandAll();
    
    /**
     * @brief 收起所有节点
     */
    void onCollapseAll();
    
    /**
     * @brief 导入区域
     */
    void onImportAreas();
    
    /**
     * @brief 导出区域
     */
    void onExportAreas();
    
    /**
     * @brief 显示右键菜单
     */
    void onShowContextMenu(const QPoint& pos);

private:
    /**
     * @brief 初始化UI界面
     */
    void initializeUI();
    
    /**
     * @brief 初始化左侧面板（树形视图）
     */
    void initializeLeftPanel();
    
    /**
     * @brief 初始化右侧面板（详细信息）
     */
    void initializeRightPanel();
    
    /**
     * @brief 初始化工具栏
     */
    void initializeToolbar();
    
    /**
     * @brief 初始化右键菜单
     */
    void initializeContextMenu();
    
    /**
     * @brief 连接信号和槽
     */
    void connectSignals();
    
    /**
     * @brief 加载区域树
     */
    void loadAreaTree();
    
    /**
     * @brief 构建树形项目
     * @param nodes 区域树节点列表
     * @param parentItem 父树形项目
     */
    void buildTreeItems(const QList<std::shared_ptr<AreaTreeNode>>& nodes, 
                       QTreeWidgetItem* parentItem = nullptr);
    
    /**
     * @brief 显示区域详细信息
     * @param area 区域对象
     */
    void showAreaDetails(const Area& area);
    
    /**
     * @brief 清空详细信息表单
     */
    void clearDetailsForm();
    
    /**
     * @brief 清空表单用于添加新区域（保持父区域选择）
     */
    void clearDetailsFormForAdd();
    
    /**
     * @brief 设置表单编辑模式
     * @param editMode 是否为编辑模式
     */
    void setFormEditMode(bool editMode);
    
    /**
     * @brief 从表单获取区域对象
     * @return 区域对象
     */
    Area getAreaFromForm();
    
    /**
     * @brief 验证表单数据
     * @return 验证结果和错误消息
     */
    QPair<bool, QString> validateForm();
    
    /**
     * @brief 查找树形项目
     * @param areaId 区域ID
     * @param parentItem 父项目（nullptr表示从根开始）
     * @return 找到的树形项目
     */
    QTreeWidgetItem* findTreeItem(int areaId, QTreeWidgetItem* parentItem = nullptr);
    
    /**
     * @brief 更新区域统计信息
     * @param areaId 区域ID
     */
    void updateAreaStats(int areaId);
    
    /**
     * @brief 应用搜索过滤
     * @param keyword 搜索关键词
     */
    void applySearchFilter(const QString& keyword);
    
    /**
     * @brief 递归过滤树形项目
     * @param item 树形项目
     * @param keyword 搜索关键词
     * @return 是否匹配
     */
    bool filterTreeItem(QTreeWidgetItem* item, const QString& keyword);
    
    /**
     * @brief 导出区域数据到CSV文件
     * @param fileName 文件路径
     * @return 是否成功
     */
    bool exportAreasToCSV(const QString& fileName);
    
    /**
     * @brief 从CSV文件导入区域数据
     * @param fileName 文件路径
     * @return 导入结果信息
     */
    QPair<bool, QString> importAreasFromCSV(const QString& fileName);
    
    /**
     * @brief 生成CSV示例文件
     * @param fileName 文件路径
     * @return 是否成功
     */
    bool generateCSVTemplate(const QString& fileName);
    
    /**
     * @brief 验证导入的区域数据
     * @param areas 区域列表
     * @return 验证结果和错误信息
     */
    QPair<bool, QString> validateImportData(const QList<Area>& areas);
    
    /**
     * @brief 解析CSV行数据为区域对象
     * @param fields CSV字段列表
     * @param lineNumber 行号（用于错误报告）
     * @return 区域对象和是否成功
     */
    QPair<Area, bool> parseCSVArea(const QStringList& fields, int lineNumber);
    
    /**
     * @brief 解析CSV行字符串为字段列表
     * @param line CSV行字符串
     * @return 字段列表
     */
    QStringList parseCSVLine(const QString& line);
    
    /**
     * @brief 按层级排序区域列表
     * @param areas 区域列表
     * @return 排序后的区域列表
     */
    QList<Area> sortAreasByLevel(const QList<Area>& areas);

private:
    // 数据访问
    std::shared_ptr<IDatabaseProvider> m_dbProvider;
    std::unique_ptr<AreaDao> m_areaDao;
    
    // 当前状态
    Area m_currentArea;
    bool m_isEditMode;
    
    // 主布局
    QHBoxLayout* m_mainLayout;
    QSplitter* m_splitter;
    
    // 左侧面板（树形视图）
    QWidget* m_leftPanel;
    QVBoxLayout* m_leftLayout;
    
    // 工具栏
    QWidget* m_toolbar;
    QHBoxLayout* m_toolbarLayout;
    QPushButton* m_btnAdd;
    QPushButton* m_btnAddSub;
    QPushButton* m_btnEdit;
    QPushButton* m_btnDelete;
    QPushButton* m_btnExpandAll;
    QPushButton* m_btnCollapseAll;
    QPushButton* m_btnImport;
    QPushButton* m_btnExport;
    
    // 搜索区域
    QWidget* m_searchWidget;
    QHBoxLayout* m_searchLayout;
    QLineEdit* m_searchEdit;
    QPushButton* m_btnSearch;
    QPushButton* m_btnClearSearch;
    QPushButton* m_btnRefresh;
    
    // 树形视图
    QTreeWidget* m_treeWidget;
    
    // 右侧面板（详细信息）
    QWidget* m_rightPanel;
    QVBoxLayout* m_rightLayout;
    
    // 详细信息表单
    QGroupBox* m_detailsGroup;
    QGridLayout* m_detailsLayout;
    QLabel* m_lblName;
    QLineEdit* m_editName;
    QLabel* m_lblCode;
    QLineEdit* m_editCode;
    QLabel* m_lblParent;
    QComboBox* m_comboParent;
    QLabel* m_lblDescription;
    QTextEdit* m_editDescription;
    QLabel* m_lblStatus;
    QComboBox* m_comboStatus;
    QLabel* m_lblSortOrder;
    QSpinBox* m_spinSortOrder;
    
    // 操作按钮
    QWidget* m_buttonWidget;
    QHBoxLayout* m_buttonLayout;
    QPushButton* m_btnSave;
    QPushButton* m_btnCancel;
    
    // 统计信息
    QGroupBox* m_statsGroup;
    QGridLayout* m_statsLayout;
    QLabel* m_lblChildCount;
    QLabel* m_lblDirectControllerCount;
    QLabel* m_lblTotalControllerCount;
    QLabel* m_lblCreatedAt;
    QLabel* m_lblUpdatedAt;
    
    // 右键菜单
    QMenu* m_contextMenu;
    QAction* m_actionAdd;
    QAction* m_actionAddSub;
    QAction* m_actionEdit;
    QAction* m_actionDelete;
    QAction* m_actionRefresh;
};

} // namespace AccessControl

#endif // AREAMANAGEMENTWIDGET_H 