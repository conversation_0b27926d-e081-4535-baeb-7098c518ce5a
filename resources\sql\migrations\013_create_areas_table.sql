-- 迁移文件：013_create_areas_table.sql
-- 描述：创建区域表
-- 创建时间：2025-07-26
-- 作者：AI Assistant

-- ========== 创建areas表 ==========

CREATE TABLE IF NOT EXISTS areas (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    area_name VARCHAR(100) NOT NULL,              -- 区域名称
    area_code VARCHAR(20) NOT NULL UNIQUE,       -- 区域代码（唯一）
    parent_id INTEGER,                           -- 上级区域ID（NULL表示顶级区域）
    description TEXT,                            -- 区域描述
    enabled INTEGER NOT NULL DEFAULT 1,         -- 状态：1=启用，0=禁用
    sort_order INTEGER NOT NULL DEFAULT 0,      -- 排序顺序
    level_depth INTEGER NOT NULL DEFAULT 1,     -- 区域层级
    full_path TEXT,                              -- 完整路径（如：总区域/华东区/上海）
    created_at DATETIME NOT NULL,               -- 创建时间
    updated_at DATETIME NOT NULL,               -- 更新时间
    
    -- 外键约束
    FOREIGN KEY (parent_id) REFERENCES areas(id) ON DELETE SET NULL
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_areas_parent_id ON areas(parent_id);
CREATE INDEX IF NOT EXISTS idx_areas_code ON areas(area_code);
CREATE INDEX IF NOT EXISTS idx_areas_enabled ON areas(enabled);
CREATE INDEX IF NOT EXISTS idx_areas_sort_order ON areas(sort_order);
CREATE INDEX IF NOT EXISTS idx_areas_name ON areas(area_name);

-- 插入示例数据
INSERT OR IGNORE INTO areas (area_name, area_code, parent_id, description, enabled, sort_order, level_depth, full_path, created_at, updated_at) VALUES
('总区域', 'ALL', NULL, '所有区域的根节点', 1, 1, 1, '总区域', datetime('now'), datetime('now')),
('华东区', 'EAST', 1, '华东地区', 1, 1, 2, '总区域/华东区', datetime('now'), datetime('now')),
('华北区', 'NORTH', 1, '华北地区', 1, 2, 2, '总区域/华北区', datetime('now'), datetime('now')),
('华南区', 'SOUTH', 1, '华南地区', 1, 3, 2, '总区域/华南区', datetime('now'), datetime('now')),
('西部区', 'WEST', 1, '西部地区', 1, 4, 2, '总区域/西部区', datetime('now'), datetime('now')); 