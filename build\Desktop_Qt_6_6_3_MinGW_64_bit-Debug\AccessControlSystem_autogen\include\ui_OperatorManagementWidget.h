/********************************************************************************
** Form generated from reading UI file 'OperatorManagementWidget.ui'
**
** Created by: Qt User Interface Compiler version 6.6.3
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_OPERATORMANAGEMENTWIDGET_H
#define UI_OPERATORMANAGEMENTWIDGET_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QFrame>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QTableWidget>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_OperatorManagementWidget
{
public:
    QVBoxLayout *verticalLayout;
    QFrame *buttonFrame;
    QHBoxLayout *horizontalLayout_2;
    QPushButton *addButton;
    QPushButton *editButton;
    QPushButton *deleteButton;
    QPushButton *resetPasswordButton;
    QPushButton *changeStatusButton;
    QSpacerItem *horizontalSpacer;
    QFrame *searchFrame;
    QHBoxLayout *horizontalLayout;
    QLabel *usernameLabel;
    QLineEdit *usernameSearchEdit;
    QLabel *roleLabel;
    QComboBox *roleFilterCombo;
    QPushButton *searchButton;
    QPushButton *filterButton;
    QPushButton *clearFilterButton;
    QTableWidget *operatorTableWidget;
    QFrame *statusFrame;
    QHBoxLayout *horizontalLayout_3;
    QLabel *totalLabel;
    QSpacerItem *horizontalSpacer_2;
    QLabel *statusLabel;

    void setupUi(QWidget *OperatorManagementWidget)
    {
        if (OperatorManagementWidget->objectName().isEmpty())
            OperatorManagementWidget->setObjectName("OperatorManagementWidget");
        OperatorManagementWidget->resize(800, 600);
        verticalLayout = new QVBoxLayout(OperatorManagementWidget);
        verticalLayout->setObjectName("verticalLayout");
        buttonFrame = new QFrame(OperatorManagementWidget);
        buttonFrame->setObjectName("buttonFrame");
        buttonFrame->setFrameShape(QFrame::StyledPanel);
        buttonFrame->setFrameShadow(QFrame::Raised);
        horizontalLayout_2 = new QHBoxLayout(buttonFrame);
        horizontalLayout_2->setObjectName("horizontalLayout_2");
        addButton = new QPushButton(buttonFrame);
        addButton->setObjectName("addButton");

        horizontalLayout_2->addWidget(addButton);

        editButton = new QPushButton(buttonFrame);
        editButton->setObjectName("editButton");

        horizontalLayout_2->addWidget(editButton);

        deleteButton = new QPushButton(buttonFrame);
        deleteButton->setObjectName("deleteButton");

        horizontalLayout_2->addWidget(deleteButton);

        resetPasswordButton = new QPushButton(buttonFrame);
        resetPasswordButton->setObjectName("resetPasswordButton");

        horizontalLayout_2->addWidget(resetPasswordButton);

        changeStatusButton = new QPushButton(buttonFrame);
        changeStatusButton->setObjectName("changeStatusButton");

        horizontalLayout_2->addWidget(changeStatusButton);

        horizontalSpacer = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        horizontalLayout_2->addItem(horizontalSpacer);


        verticalLayout->addWidget(buttonFrame);

        searchFrame = new QFrame(OperatorManagementWidget);
        searchFrame->setObjectName("searchFrame");
        searchFrame->setFrameShape(QFrame::StyledPanel);
        searchFrame->setFrameShadow(QFrame::Raised);
        horizontalLayout = new QHBoxLayout(searchFrame);
        horizontalLayout->setObjectName("horizontalLayout");
        usernameLabel = new QLabel(searchFrame);
        usernameLabel->setObjectName("usernameLabel");

        horizontalLayout->addWidget(usernameLabel);

        usernameSearchEdit = new QLineEdit(searchFrame);
        usernameSearchEdit->setObjectName("usernameSearchEdit");

        horizontalLayout->addWidget(usernameSearchEdit);

        roleLabel = new QLabel(searchFrame);
        roleLabel->setObjectName("roleLabel");

        horizontalLayout->addWidget(roleLabel);

        roleFilterCombo = new QComboBox(searchFrame);
        roleFilterCombo->setObjectName("roleFilterCombo");

        horizontalLayout->addWidget(roleFilterCombo);

        searchButton = new QPushButton(searchFrame);
        searchButton->setObjectName("searchButton");

        horizontalLayout->addWidget(searchButton);

        filterButton = new QPushButton(searchFrame);
        filterButton->setObjectName("filterButton");

        horizontalLayout->addWidget(filterButton);

        clearFilterButton = new QPushButton(searchFrame);
        clearFilterButton->setObjectName("clearFilterButton");

        horizontalLayout->addWidget(clearFilterButton);


        verticalLayout->addWidget(searchFrame);

        operatorTableWidget = new QTableWidget(OperatorManagementWidget);
        operatorTableWidget->setObjectName("operatorTableWidget");
        operatorTableWidget->setAlternatingRowColors(true);
        operatorTableWidget->setSelectionBehavior(QAbstractItemView::SelectRows);
        operatorTableWidget->horizontalHeader()->setStretchLastSection(true);

        verticalLayout->addWidget(operatorTableWidget);

        statusFrame = new QFrame(OperatorManagementWidget);
        statusFrame->setObjectName("statusFrame");
        statusFrame->setFrameShape(QFrame::StyledPanel);
        statusFrame->setFrameShadow(QFrame::Raised);
        horizontalLayout_3 = new QHBoxLayout(statusFrame);
        horizontalLayout_3->setObjectName("horizontalLayout_3");
        totalLabel = new QLabel(statusFrame);
        totalLabel->setObjectName("totalLabel");

        horizontalLayout_3->addWidget(totalLabel);

        horizontalSpacer_2 = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        horizontalLayout_3->addItem(horizontalSpacer_2);

        statusLabel = new QLabel(statusFrame);
        statusLabel->setObjectName("statusLabel");

        horizontalLayout_3->addWidget(statusLabel);


        verticalLayout->addWidget(statusFrame);


        retranslateUi(OperatorManagementWidget);

        QMetaObject::connectSlotsByName(OperatorManagementWidget);
    } // setupUi

    void retranslateUi(QWidget *OperatorManagementWidget)
    {
        OperatorManagementWidget->setWindowTitle(QCoreApplication::translate("OperatorManagementWidget", "\346\223\215\344\275\234\345\221\230\347\256\241\347\220\206", nullptr));
        addButton->setText(QCoreApplication::translate("OperatorManagementWidget", "\346\267\273\345\212\240", nullptr));
        editButton->setText(QCoreApplication::translate("OperatorManagementWidget", "\347\274\226\350\276\221", nullptr));
        deleteButton->setText(QCoreApplication::translate("OperatorManagementWidget", "\345\210\240\351\231\244", nullptr));
        resetPasswordButton->setText(QCoreApplication::translate("OperatorManagementWidget", "\351\207\215\347\275\256\345\257\206\347\240\201", nullptr));
        changeStatusButton->setText(QCoreApplication::translate("OperatorManagementWidget", "\346\233\264\346\224\271\347\212\266\346\200\201", nullptr));
        usernameLabel->setText(QCoreApplication::translate("OperatorManagementWidget", "\347\224\250\346\210\267\345\220\215/\345\247\223\345\220\215:", nullptr));
        usernameSearchEdit->setPlaceholderText(QCoreApplication::translate("OperatorManagementWidget", "\350\276\223\345\205\245\347\224\250\346\210\267\345\220\215\346\210\226\345\247\223\345\220\215", nullptr));
        roleLabel->setText(QCoreApplication::translate("OperatorManagementWidget", "\350\247\222\350\211\262:", nullptr));
        searchButton->setText(QCoreApplication::translate("OperatorManagementWidget", "\346\237\245\350\257\242", nullptr));
        filterButton->setText(QCoreApplication::translate("OperatorManagementWidget", "\347\255\233\351\200\211", nullptr));
        clearFilterButton->setText(QCoreApplication::translate("OperatorManagementWidget", "\346\270\205\351\231\244\347\255\233\351\200\211", nullptr));
        totalLabel->setText(QCoreApplication::translate("OperatorManagementWidget", "\345\205\261 0 \344\270\252\346\223\215\344\275\234\345\221\230", nullptr));
        statusLabel->setText(QCoreApplication::translate("OperatorManagementWidget", "\345\260\261\347\273\252", nullptr));
    } // retranslateUi

};

namespace Ui {
    class OperatorManagementWidget: public Ui_OperatorManagementWidget {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_OPERATORMANAGEMENTWIDGET_H
