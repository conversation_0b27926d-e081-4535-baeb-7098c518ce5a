#ifndef CONSUMERPHOTOWIDGET_H
#define CONSUMERPHOTOWIDGET_H

#include <QWidget>
#include <QLabel>
#include <QMenu>
#include <QAction>
#include <QByteArray>

namespace AccessControl {

/**
 * @brief 消费者照片管理控件
 * 
 * 提供照片上传、拍照和删除功能，通过右键菜单操作
 */
class ConsumerPhotoWidget : public QWidget
{
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param parent 父窗口
     */
    explicit ConsumerPhotoWidget(QWidget *parent = nullptr);
    
    /**
     * @brief 析构函数
     */
    ~ConsumerPhotoWidget();

    /**
     * @brief 设置照片数据
     * @param photoData 照片二进制数据
     */
    void setPhotoData(const QByteArray& photoData);

    /**
     * @brief 获取照片数据
     * @return 照片二进制数据
     */
    QByteArray photoData() const;

    /**
     * @brief 清除照片
     */
    void clearPhoto();

    /**
     * @brief 是否有照片
     * @return 是否有照片
     */
    bool hasPhoto() const;
    
    /**
     * @brief 保存照片到文件
     * @param fileName 文件名（不含扩展名），会自动添加.jpg扩展名
     * @param directory 保存目录，默认为当前目录
     * @return 保存是否成功
     */
    bool savePhotoToFile(const QString& fileName, const QString& directory = ".") const;

signals:
    /**
     * @brief 照片变更信号
     */
    void photoChanged();

protected:
    /**
     * @brief 鼠标按下事件
     */
    void mousePressEvent(QMouseEvent *event) override;

    /**
     * @brief 绘制事件
     */
    void paintEvent(QPaintEvent *event) override;

private slots:
    /**
     * @brief 上传照片
     */
    void uploadPhoto();

    /**
     * @brief 拍照
     */
    void takePhoto();

    /**
     * @brief 删除照片
     */
    void deletePhoto();

private:
    /**
     * @brief 初始化UI
     */
    void initUI();

    /**
     * @brief 创建右键菜单
     */
    void createContextMenu();

    /**
     * @brief 更新显示
     */
    void updateDisplay();

private:
    QLabel* m_photoLabel;        ///< 照片显示标签
    QMenu* m_contextMenu;        ///< 右键菜单
    QAction* m_uploadAction;     ///< 上传照片动作
    QAction* m_takePhotoAction;  ///< 拍照动作
    QAction* m_deleteAction;     ///< 删除照片动作
    
    QByteArray m_photoData;      ///< 照片数据
    QPixmap m_photoPixmap;       ///< 照片像素图
};

} // namespace AccessControl

#endif // CONSUMERPHOTOWIDGET_H 