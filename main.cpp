#include "src/views/MainWindow.h"
#include "src/views/LoginWindow.h"
#include "src/database/DatabaseFactory.h"

#include <QApplication>
#include <QLocale>
#include <QTranslator>
#include <QMessageBox>
#include <QDir>
#include <QStandardPaths>
#include <QDebug>
#include <QFile>
#include <QTextStream>
#include <QDateTime>
#include <QMutex>

using namespace AccessControl;

// 全局日志文件
static QFile* g_logFile = nullptr;
static QTextStream* g_logStream = nullptr;
static QMutex g_logMutex;

/**
 * @brief 自定义消息处理器，将日志同时输出到控制台和文件
 */
void messageHandler(QtMsgType type, const QMessageLogContext &context, const QString &msg)
{
    QMutexLocker locker(&g_logMutex);
    
    QString timestamp = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz");
    QString typeStr;
    
    switch (type) {
    case QtDebugMsg:    typeStr = "Debug"; break;
    case QtWarningMsg:  typeStr = "Warning"; break;
    case QtCriticalMsg: typeStr = "Critical"; break;
    case QtFatalMsg:    typeStr = "Fatal"; break;
    case QtInfoMsg:     typeStr = "Info"; break;
    }
    
    QString formattedMsg = QString("[%1] %2: %3").arg(timestamp, typeStr, msg);
    
    // 输出到控制台
    fprintf(stderr, "%s\n", formattedMsg.toLocal8Bit().constData());
    
    // 输出到文件
    if (g_logStream) {
        *g_logStream << formattedMsg << Qt::endl;
        g_logStream->flush();
    }
}

/**
 * @brief 初始化日志系统
 */
void initializeLogging() {
    // 获取数据目录
    QString dataDir = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
    
    // 输出到控制台进行调试
    fprintf(stderr, "DEBUG: Data directory: %s\n", dataDir.toLocal8Bit().constData());
    
    // 确保目录存在
    if (!QDir().mkpath(dataDir)) {
        fprintf(stderr, "ERROR: Failed to create directory: %s\n", dataDir.toLocal8Bit().constData());
        return;
    }
    
    // 创建日志文件
    QString logPath = dataDir + "/debug.log";
    fprintf(stderr, "DEBUG: Log path: %s\n", logPath.toLocal8Bit().constData());
    
    g_logFile = new QFile(logPath);
    
    if (g_logFile->open(QIODevice::WriteOnly | QIODevice::Append)) {
        g_logStream = new QTextStream(g_logFile);
        
        // 先写入一条测试消息
        *g_logStream << "=== Log System Initialized ===" << Qt::endl;
        *g_logStream << "Timestamp: " << QDateTime::currentDateTime().toString() << Qt::endl;
        g_logStream->flush();
        
        // 安装自定义消息处理器
        qInstallMessageHandler(messageHandler);
        
        fprintf(stderr, "SUCCESS: Log system initialized\n");
        qDebug() << "=== 日志系统初始化完成 ===";
        qDebug() << "日志文件:" << logPath;
    } else {
        fprintf(stderr, "ERROR: Failed to open log file: %s, Error: %s\n", 
                logPath.toLocal8Bit().constData(), 
                g_logFile->errorString().toLocal8Bit().constData());
    }
}

/**
 * @brief 清理日志系统
 */
void cleanupLogging() {
    if (g_logStream) {
        delete g_logStream;
        g_logStream = nullptr;
    }
    if (g_logFile) {
        g_logFile->close();
        delete g_logFile;
        g_logFile = nullptr;
    }
}

/**
 * @brief 初始化应用程序配置
 */
void initializeApplication() {
    // 设置应用程序信息
    QApplication::setApplicationName("AccessControlSystem");
    QApplication::setApplicationDisplayName("专业智能门禁管理系统");
    QApplication::setApplicationVersion("1.0.0-alpha2");
    QApplication::setOrganizationName("AccessControl Inc.");
    QApplication::setOrganizationDomain("accesscontrol.local");
    
    // 设置应用程序图标
    // QApplication::setWindowIcon(QIcon(":/icons/app.png"));
}

/**
 * @brief 检查并创建必要的目录
 */
void ensureDirectories() {
    // 创建数据目录
    QString dataDir = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
    QDir().mkpath(dataDir);
    
    // 创建日志目录
    QString logDir = dataDir + "/logs";
    QDir().mkpath(logDir);
    
    qDebug() << "数据目录:" << dataDir;
    qDebug() << "日志目录:" << logDir;
}

/**
 * @brief 设置默认数据库配置
 */
DatabaseConfig getDefaultDatabaseConfig() {
    DatabaseConfig config;
    config.type = DatabaseConfig::SQLite;
    
    // 将数据库文件放在用户数据目录
    QString dataDir = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
    config.filePath = dataDir + "/access_control.db";
    
    qDebug() << "默认数据库文件:" << config.filePath;
    return config;
}

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);

    // 初始化日志系统（在其他初始化之前）
    initializeLogging();
    
    // 初始化应用程序
    initializeApplication();
    ensureDirectories();
    
    qDebug() << "=== 专业智能门禁管理系统启动 ===";
    qDebug() << "版本:" << QApplication::applicationVersion();
    qDebug() << "Qt版本:" << qVersion();
    
    // 设置多语言支持
    QTranslator translator;
    const QStringList uiLanguages = QLocale::system().uiLanguages();
    for (const QString &locale : uiLanguages) {
        const QString baseName = "AccessControlSystem_" + QLocale(locale).name();
        if (translator.load(":/i18n/" + baseName)) {
            app.installTranslator(&translator);
            qDebug() << "加载语言包:" << baseName;
            break;
        }
    }
    
    // 检查数据库驱动
    auto supportedTypes = DatabaseFactory::getSupportedTypes();
    if (supportedTypes.isEmpty()) {
        QMessageBox::critical(nullptr, "系统错误", 
                             "未找到任何可用的数据库驱动！\n"
                             "请确保Qt SQL模块正确安装。");
        return -1;
    }
    
    qDebug() << "支持的数据库类型:";
    for (auto type : supportedTypes) {
        qDebug() << "  -" << DatabaseFactory::getTypeName(type);
    }
    
    // 创建登录窗口
    LoginWindow loginWindow;
    loginWindow.setDatabaseConfig(getDefaultDatabaseConfig());
    
    // 显示登录窗口
    qDebug() << "显示登录窗口";
    if (loginWindow.exec() != QDialog::Accepted) {
        qDebug() << "用户取消登录或登录失败，程序退出";
        return 0;
    }
    
    // 获取登录用户信息
    auto databaseProvider = loginWindow.getDatabaseProvider();
    
    if (!databaseProvider) {
        QMessageBox::critical(nullptr, "登录错误", "登录验证失败，程序将退出。");
        return -1;
    }
    
    qDebug() << "数据库连接成功";
    
    // 创建并显示主窗口
    AccessControl::MainWindow mainWindow(loginWindow.getCurrentOperator(), databaseProvider);
    
    // 显示主窗口
    mainWindow.showMainWindow();
    
    qDebug() << "主窗口已显示，进入事件循环";
    
    int result = app.exec();
    
    qDebug() << "应用程序退出，返回代码:" << result;
    
    // 清理日志系统
    cleanupLogging();
    
    return result;
}
