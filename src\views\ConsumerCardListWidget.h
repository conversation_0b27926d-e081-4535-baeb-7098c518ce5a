#ifndef CONSUMERCARDLISTWIDGET_H
#define CONSUMERCARDLISTWIDGET_H

#include <QWidget>
#include <QTableWidget>
#include <QPushButton>
#include <QStringList>

namespace AccessControl {

/**
 * @brief 消费者门禁卡列表控件
 * 
 * 提供门禁卡列表显示和管理功能
 */
class ConsumerCardListWidget : public QWidget
{
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param parent 父窗口
     */
    explicit ConsumerCardListWidget(QWidget *parent = nullptr);

    /**
     * @brief 析构函数
     */
    ~ConsumerCardListWidget();

    /**
     * @brief 设置卡片列表
     * @param cards 卡片列表
     */
    void setCards(const QStringList& cards);

    /**
     * @brief 获取卡片列表
     * @return 卡片列表
     */
    QStringList cards() const;

signals:
    /**
     * @brief 卡片变更信号
     */
    void cardsChanged();

private slots:
    /**
     * @brief 添加卡片
     */
    void addCard();

    /**
     * @brief 编辑卡片
     */
    void editCard();

    /**
     * @brief 删除卡片
     */
    void deleteCard();

private:
    /**
     * @brief 初始化UI
     */
    void initUI();

    /**
     * @brief 刷新卡片列表显示
     */
    void refreshCardList();

private:
    QTableWidget* m_cardTable;    ///< 卡片表格
    QPushButton* m_addButton;     ///< 添加按钮
    QPushButton* m_editButton;    ///< 编辑按钮
    QPushButton* m_deleteButton;  ///< 删除按钮

    QStringList m_cards;          ///< 卡片列表（简化为字符串列表）
};

} // namespace AccessControl

#endif // CONSUMERCARDLISTWIDGET_H 