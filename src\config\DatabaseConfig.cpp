#include "DatabaseConfig.h"
#include <QJsonDocument>
#include <QJsonObject>
#include <QDebug>

namespace AccessControl {

int DatabaseConfig::getDefaultPort() const {
    switch (type) {
        case PostgreSQL:
            return 5432;
        case MySQL:
            return 3306;
        case SQLite:
        default:
            return 0; // SQLite不需要端口
    }
}

QString DatabaseConfig::getConnectionString() const {
    QString connectionString;
    
    switch (type) {
        case SQLite:
            connectionString = QString("QSQLITE;database=%1").arg(filePath);
            break;
            
        case PostgreSQL: {
            int actualPort = (port == 0) ? getDefaultPort() : port;
            connectionString = QString("QPSQL;host=%1;port=%2;dbname=%3")
                              .arg(host)
                              .arg(actualPort)
                              .arg(database);
            if (!username.isEmpty()) {
                connectionString += QString(";user=%1").arg(username);
            }
            if (!password.isEmpty()) {
                connectionString += QString(";password=%1").arg(password);
            }
            if (useSSL) {
                connectionString += ";sslmode=require";
            }
            break;
        }
        
        case MySQL: {
            int actualPort = (port == 0) ? getDefaultPort() : port;
            connectionString = QString("QMYSQL;host=%1;port=%2;database=%3")
                              .arg(host)
                              .arg(actualPort)
                              .arg(database);
            if (!username.isEmpty()) {
                connectionString += QString(";user=%1").arg(username);
            }
            if (!password.isEmpty()) {
                connectionString += QString(";password=%1").arg(password);
            }
            break;
        }
    }
    
    return connectionString;
}

bool DatabaseConfig::isValid() const {
    switch (type) {
        case SQLite:
            return !filePath.isEmpty();
            
        case PostgreSQL:
        case MySQL:
            return !host.isEmpty() && 
                   !database.isEmpty() && 
                   !username.isEmpty();
            
        default:
            return false;
    }
}

void DatabaseConfig::fromJson(const QVariantMap& json) {
    if (json.contains("type")) {
        type = static_cast<DatabaseType>(json["type"].toInt());
    }
    
    if (json.contains("host")) {
        host = json["host"].toString();
    }
    
    if (json.contains("port")) {
        port = json["port"].toInt();
    }
    
    if (json.contains("database")) {
        database = json["database"].toString();
    }
    
    if (json.contains("username")) {
        username = json["username"].toString();
    }
    
    if (json.contains("password")) {
        password = json["password"].toString();
    }
    
    if (json.contains("filePath")) {
        filePath = json["filePath"].toString();
    }
    
    if (json.contains("maxConnections")) {
        maxConnections = json["maxConnections"].toInt();
    }
    
    if (json.contains("minConnections")) {
        minConnections = json["minConnections"].toInt();
    }
    
    if (json.contains("connectionTimeout")) {
        connectionTimeout = json["connectionTimeout"].toInt();
    }
    
    if (json.contains("useSSL")) {
        useSSL = json["useSSL"].toBool();
    }
    
    if (json.contains("sslCert")) {
        sslCert = json["sslCert"].toString();
    }
    
    if (json.contains("sslKey")) {
        sslKey = json["sslKey"].toString();
    }
    
    if (json.contains("sslCA")) {
        sslCA = json["sslCA"].toString();
    }
    
    if (json.contains("options")) {
        options = json["options"].toMap();
    }
}

QVariantMap DatabaseConfig::toJson() const {
    QVariantMap json;
    
    json["type"] = static_cast<int>(type);
    json["host"] = host;
    json["port"] = port;
    json["database"] = database;
    json["username"] = username;
    json["password"] = password; // 注意：实际应用中密码应该加密存储
    json["filePath"] = filePath;
    json["maxConnections"] = maxConnections;
    json["minConnections"] = minConnections;
    json["connectionTimeout"] = connectionTimeout;
    json["useSSL"] = useSSL;
    json["sslCert"] = sslCert;
    json["sslKey"] = sslKey;
    json["sslCA"] = sslCA;
    json["options"] = options;
    
    return json;
}

QString DatabaseConfig::getTypeName() const {
    switch (type) {
        case SQLite:
            return "SQLite";
        case PostgreSQL:
            return "PostgreSQL";
        case MySQL:
            return "MySQL";
        default:
            return "Unknown";
    }
}

bool DatabaseConfig::operator==(const DatabaseConfig& other) const {
    return type == other.type &&
           host == other.host &&
           port == other.port &&
           database == other.database &&
           username == other.username &&
           password == other.password &&
           filePath == other.filePath &&
           maxConnections == other.maxConnections &&
           minConnections == other.minConnections &&
           connectionTimeout == other.connectionTimeout &&
           useSSL == other.useSSL &&
           sslCert == other.sslCert &&
           sslKey == other.sslKey &&
           sslCA == other.sslCA &&
           options == other.options;
}

} // namespace AccessControl 