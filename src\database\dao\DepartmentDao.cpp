#include "DepartmentDao.h"
#include <QSqlQuery>
#include <QSqlError>
#include <QSqlDatabase>
#include <QDebug>
#include <QVariant>
#include <QDateTime>
#include <QPair>

namespace AccessControl {

DepartmentDao::DepartmentDao(std::shared_ptr<IDatabaseProvider> dbProvider)
    : m_dbProvider(dbProvider)
{
}

DepartmentDao::~DepartmentDao()
{
}

// ========== 基本CRUD操作 ==========

int DepartmentDao::createDepartment(Department& department)
{
    if (!m_dbProvider) {
        qWarning() << "DepartmentDao: Database provider is null";
        return -1;
    }
    
    // 验证部门数据
    auto validation = validateDepartment(department);
    if (!validation.first) {
        qWarning() << "DepartmentDao: Validation failed:" << validation.second;
        return -1;
    }
    
    // 检查代码是否已存在
    if (isCodeExists(department.code())) {
        qWarning() << "DepartmentDao: Department code already exists:" << department.code();
        return -1;
    }
    
    QSqlDatabase db = m_dbProvider->getDatabase();
    if (!db.isOpen()) {
        qWarning() << "DepartmentDao: Database is not open";
        return -1;
    }
    
    // 先检查表结构进行调试
    QSqlQuery checkQuery(db);
    checkQuery.exec("PRAGMA table_info(departments)");
    
    qDebug() << "=== Departments Table Structure ===";
    QStringList columnNames;
    while (checkQuery.next()) {
        QString columnName = checkQuery.value(1).toString();
        columnNames << columnName;
        qDebug() << "Column:" << checkQuery.value(0).toInt() << columnName << checkQuery.value(2).toString();
    }
    qDebug() << "Total columns:" << columnNames.size();
    qDebug() << "Column names:" << columnNames;
    
    // 使用完整的插入方法，包含所有必要字段
    QSqlQuery query(db);
    
    QString sql = "INSERT INTO departments (name, code, parent_id, description, status, sort_order, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
    query.prepare(sql);
    
    QDateTime currentTime = QDateTime::currentDateTime();
    query.addBindValue(department.name());
    query.addBindValue(department.code());
    
    // 处理parent_id - 如果为0或负数，使用NULL
    if (department.parentId() <= 0) {
        query.addBindValue(QVariant());
    } else {
        query.addBindValue(department.parentId());
    }
    
    query.addBindValue(department.description());
    query.addBindValue(static_cast<int>(department.status()));
    query.addBindValue(department.sortOrder());
    query.addBindValue(currentTime);
    query.addBindValue(currentTime);
    
    qDebug() << "DepartmentDao: Executing SQL:" << sql;
    qDebug() << "DepartmentDao: Parameters count:" << query.boundValues().size();
    qDebug() << "DepartmentDao: Parameter values:";
    qDebug() << "  name:" << department.name();
    qDebug() << "  code:" << department.code();
    qDebug() << "  parent_id:" << (department.parentId() <= 0 ? "NULL" : QString::number(department.parentId()));
    qDebug() << "  description:" << department.description();
    qDebug() << "  status:" << static_cast<int>(department.status());
    qDebug() << "  sort_order:" << department.sortOrder();
    qDebug() << "  created_at:" << currentTime.toString();
    qDebug() << "  updated_at:" << currentTime.toString();
    
    if (!query.exec()) {
        QString errorMsg = QString("数据库创建部门失败: %1").arg(query.lastError().text());
        qWarning() << "DepartmentDao: Failed to create department:" << errorMsg;
        qWarning() << "SQL:" << query.lastQuery();
        qWarning() << "Error type:" << query.lastError().type();
        qWarning() << "Driver text:" << query.lastError().driverText();
        qWarning() << "Database text:" << query.lastError().databaseText();
        
        // 如果插入失败，尝试基本测试（包含必要的NOT NULL字段）
        qDebug() << "Trying basic test with required fields...";
        QSqlQuery testQuery(db);
        testQuery.prepare("INSERT INTO departments (name, code, created_at, updated_at) VALUES (?, ?, ?, ?)");
        QDateTime testTime = QDateTime::currentDateTime();
        testQuery.addBindValue("Test");
        testQuery.addBindValue("TEST_CODE");
        testQuery.addBindValue(testTime);
        testQuery.addBindValue(testTime);
        if (testQuery.exec()) {
            qDebug() << "Basic test succeeded, deleting test record...";
            QSqlQuery deleteQuery(db);
            deleteQuery.exec("DELETE FROM departments WHERE code = 'TEST_CODE'");
        } else {
            qDebug() << "Basic test also failed:" << testQuery.lastError().text();
        }
        
        return -1;
    }
    
    // 获取新插入的ID
    int departmentId = query.lastInsertId().toInt();
    department.setId(departmentId);
    
    // 更新部门路径信息
    updateDepartmentPaths(departmentId);
    
    qDebug() << "DepartmentDao: Created department with ID:" << departmentId;
    return departmentId;
}

bool DepartmentDao::updateDepartment(const Department& department)
{
    if (!m_dbProvider || department.id() <= 0) {
        return false;
    }
    
    // 验证部门数据
    auto validation = validateDepartment(department);
    if (!validation.first) {
        qWarning() << "DepartmentDao: Validation failed:" << validation.second;
        return false;
    }
    
    // 检查代码是否已存在（排除自己）
    if (isCodeExists(department.code(), department.id())) {
        qWarning() << "DepartmentDao: Department code already exists:" << department.code();
        return false;
    }
    
    QSqlDatabase db = m_dbProvider->getDatabase();
    if (!db.isOpen()) {
        return false;
    }
    
    QSqlQuery query(db);
    query.prepare("UPDATE departments SET name = ?, code = ?, parent_id = ?, "
                  "description = ?, status = ?, sort_order = ?, updated_at = ? "
                  "WHERE id = ?");
    
    query.addBindValue(department.name());
    query.addBindValue(department.code());
    if (department.parentId() <= 0) {
        query.addBindValue(QVariant());
    } else {
        query.addBindValue(department.parentId());
    }
    query.addBindValue(department.description());
    query.addBindValue(static_cast<int>(department.status()));
    query.addBindValue(department.sortOrder());
    query.addBindValue(QDateTime::currentDateTime());
    query.addBindValue(department.id());
    
    if (!query.exec()) {
        qWarning() << "DepartmentDao: Failed to update department:" << query.lastError().text();
        return false;
    }
    
    // 更新部门路径信息
    updateDepartmentPaths(department.id());
    
    return query.numRowsAffected() > 0;
}

bool DepartmentDao::deleteDepartment(int departmentId)
{
    if (!m_dbProvider || departmentId <= 0) {
        return false;
    }
    
    // 检查是否可以删除
    if (!canDelete(departmentId)) {
        qWarning() << "DepartmentDao: Cannot delete department, has children or users";
        return false;
    }
    
    QSqlDatabase db = m_dbProvider->getDatabase();
    if (!db.isOpen()) {
        return false;
    }
    
    QSqlQuery query(db);
    query.prepare("DELETE FROM departments WHERE id = ?");
    query.addBindValue(departmentId);
    
    if (!query.exec()) {
        qWarning() << "DepartmentDao: Failed to delete department:" << query.lastError().text();
        return false;
    }
    
    return query.numRowsAffected() > 0;
}

Department DepartmentDao::findById(int departmentId)
{
    Department department;
    
    if (!m_dbProvider || departmentId <= 0) {
        return department;
    }
    
    QSqlDatabase db = m_dbProvider->getDatabase();
    if (!db.isOpen()) {
        return department;
    }
    
    QSqlQuery query(db);
    query.prepare("SELECT * FROM departments WHERE id = ?");
    query.addBindValue(departmentId);
    
    if (query.exec() && query.next()) {
        department = buildDepartmentFromQuery(query);
    } else if (!query.exec()) {
        qWarning() << "DepartmentDao: Failed to find department by ID:" << query.lastError().text();
    }
    
    return department;
}

Department DepartmentDao::findByCode(const QString& code)
{
    Department department;
    
    if (!m_dbProvider || code.isEmpty()) {
        return department;
    }
    
    QSqlDatabase db = m_dbProvider->getDatabase();
    if (!db.isOpen()) {
        return department;
    }
    
    QSqlQuery query(db);
    query.prepare("SELECT * FROM departments WHERE code = ?");
    query.addBindValue(code);
    
    if (query.exec() && query.next()) {
        department = buildDepartmentFromQuery(query);
    } else if (!query.exec()) {
        qWarning() << "DepartmentDao: Failed to find department by code:" << query.lastError().text();
    }
    
    return department;
}

// ========== 查询操作 ==========

QList<Department> DepartmentDao::findAll()
{
    QList<Department> departments;
    
    if (!m_dbProvider) {
        return departments;
    }
    
    QSqlDatabase db = m_dbProvider->getDatabase();
    if (!db.isOpen()) {
        return departments;
    }
    
    QSqlQuery query(db);
    query.prepare("SELECT * FROM departments ORDER BY sort_order, name");
    
    if (query.exec()) {
        while (query.next()) {
            departments.append(buildDepartmentFromQuery(query));
        }
    } else {
        qWarning() << "DepartmentDao: Failed to find all departments:" << query.lastError().text();
    }
    
    return departments;
}

QList<Department> DepartmentDao::findActive()
{
    QList<Department> departments;
    
    if (!m_dbProvider) {
        return departments;
    }
    
    QSqlDatabase db = m_dbProvider->getDatabase();
    if (!db.isOpen()) {
        return departments;
    }
    
    QSqlQuery query(db);
    query.prepare("SELECT * FROM departments WHERE status = 1 ORDER BY sort_order, name");
    
    if (query.exec()) {
        while (query.next()) {
            departments.append(buildDepartmentFromQuery(query));
        }
    } else {
        qWarning() << "DepartmentDao: Failed to find active departments:" << query.lastError().text();
    }
    
    return departments;
}

QList<Department> DepartmentDao::findByParentId(int parentId)
{
    QList<Department> departments;
    
    if (!m_dbProvider) {
        return departments;
    }
    
    QSqlDatabase db = m_dbProvider->getDatabase();
    if (!db.isOpen()) {
        return departments;
    }
    
    QSqlQuery query(db);
    if (parentId == 0) {
        query.prepare("SELECT * FROM departments WHERE parent_id IS NULL ORDER BY sort_order, name");
    } else {
        query.prepare("SELECT * FROM departments WHERE parent_id = ? ORDER BY sort_order, name");
        query.addBindValue(parentId);
    }
    
    if (query.exec()) {
        while (query.next()) {
            departments.append(buildDepartmentFromQuery(query));
        }
    } else {
        qWarning() << "DepartmentDao: Failed to find departments by parent ID:" << query.lastError().text();
    }
    
    return departments;
}

QList<Department> DepartmentDao::findByName(const QString& name)
{
    QList<Department> departments;
    
    if (!m_dbProvider || name.isEmpty()) {
        return departments;
    }
    
    QSqlDatabase db = m_dbProvider->getDatabase();
    if (!db.isOpen()) {
        return departments;
    }
    
    QSqlQuery query(db);
    query.prepare("SELECT * FROM departments WHERE name LIKE ? ORDER BY sort_order, name");
    query.addBindValue(QString("%%1%").arg(name));
    
    if (query.exec()) {
        while (query.next()) {
            departments.append(buildDepartmentFromQuery(query));
        }
    } else {
        qWarning() << "DepartmentDao: Failed to find departments by name:" << query.lastError().text();
    }
    
    return departments;
}

QList<std::shared_ptr<DepartmentTreeNode>> DepartmentDao::getDepartmentTree()
{
    QList<Department> allDepartments = findAll();
    return buildTreeNodes(allDepartments, 0);
}

QList<Department> DepartmentDao::getAllChildren(int departmentId)
{
    QList<Department> children;
    
    if (!m_dbProvider || departmentId <= 0) {
        return children;
    }
    
    QSqlDatabase db = m_dbProvider->getDatabase();
    if (!db.isOpen()) {
        return children;
    }
    
    // 使用递归CTE查询所有子部门
    QSqlQuery query(db);
    query.prepare(R"(
        WITH RECURSIVE department_tree AS (
            SELECT id, name, code, parent_id, description, status, sort_order, 
                   level, full_path, created_at, updated_at
            FROM departments 
            WHERE parent_id = ?
            
            UNION ALL
            
            SELECT d.id, d.name, d.code, d.parent_id, d.description, d.status, d.sort_order,
                   d.level, d.full_path, d.created_at, d.updated_at
            FROM departments d
            JOIN department_tree dt ON d.parent_id = dt.id
        )
        SELECT * FROM department_tree ORDER BY level, sort_order, name
    )");
    query.addBindValue(departmentId);
    
    if (query.exec()) {
        while (query.next()) {
            children.append(buildDepartmentFromQuery(query));
        }
    } else {
        qWarning() << "DepartmentDao: Failed to get all children:" << query.lastError().text();
    }
    
    return children;
}

// ========== 业务逻辑方法 ==========

bool DepartmentDao::isCodeExists(const QString& code, int excludeId)
{
    if (!m_dbProvider || code.isEmpty()) {
        return false;
    }
    
    QSqlDatabase db = m_dbProvider->getDatabase();
    if (!db.isOpen()) {
        return false;
    }
    
    QSqlQuery query(db);
    if (excludeId > 0) {
        query.prepare("SELECT COUNT(*) FROM departments WHERE code = ? AND id != ?");
        query.addBindValue(code);
        query.addBindValue(excludeId);
    } else {
        query.prepare("SELECT COUNT(*) FROM departments WHERE code = ?");
        query.addBindValue(code);
    }
    
    if (query.exec() && query.next()) {
        return query.value(0).toInt() > 0;
    }
    
    return false;
}

bool DepartmentDao::canDelete(int departmentId)
{
    if (!m_dbProvider || departmentId <= 0) {
        return false;
    }
    
    QSqlDatabase db = m_dbProvider->getDatabase();
    if (!db.isOpen()) {
        return false;
    }
    
    // 检查是否有子部门
    QSqlQuery query(db);
    query.prepare("SELECT COUNT(*) FROM departments WHERE parent_id = ?");
    query.addBindValue(departmentId);
    
    if (query.exec() && query.next()) {
        if (query.value(0).toInt() > 0) {
            return false; // 有子部门，不能删除
        }
    }
    
    // 检查是否有关联用户（如果用户表存在的话）
    query.prepare("SELECT COUNT(*) FROM access_users WHERE department_id = ?");
    query.addBindValue(departmentId);
    
    if (query.exec() && query.next()) {
        if (query.value(0).toInt() > 0) {
            return false; // 有关联用户，不能删除
        }
    }
    
    return true;
}

DepartmentDao::DepartmentStats DepartmentDao::getDepartmentStats(int departmentId)
{
    DepartmentStats stats = {0, 0, 0};
    
    if (!m_dbProvider || departmentId <= 0) {
        return stats;
    }
    
    QSqlDatabase db = m_dbProvider->getDatabase();
    if (!db.isOpen()) {
        return stats;
    }
    
    // 获取子部门数量
    QSqlQuery query(db);
    query.prepare("SELECT COUNT(*) FROM departments WHERE parent_id = ?");
    query.addBindValue(departmentId);
    
    if (query.exec() && query.next()) {
        stats.childCount = query.value(0).toInt();
    }
    
    // 获取直接用户数量（如果用户表存在）
    query.prepare("SELECT COUNT(*) FROM access_users WHERE department_id = ?");
    query.addBindValue(departmentId);
    
    if (query.exec() && query.next()) {
        stats.directUserCount = query.value(0).toInt();
    }
    
    // 获取总用户数量（包括子部门）
    QList<Department> allChildren = getAllChildren(departmentId);
    stats.totalUserCount = stats.directUserCount;
    
    for (const Department& child : allChildren) {
        query.prepare("SELECT COUNT(*) FROM access_users WHERE department_id = ?");
        query.addBindValue(child.id());
        
        if (query.exec() && query.next()) {
            stats.totalUserCount += query.value(0).toInt();
        }
    }
    
    return stats;
}

bool DepartmentDao::updateDepartmentPaths(int departmentId)
{
    if (!m_dbProvider) {
        return false;
    }
    
    QSqlDatabase db = m_dbProvider->getDatabase();
    if (!db.isOpen()) {
        return false;
    }
    
    // 如果指定了部门ID，只更新该部门及其子部门
    // 如果departmentId为0，更新所有部门
    
    try {
        // 使用递归CTE来计算路径和层级
        QSqlQuery query(db);
        
        QString updateSql = R"(
            WITH RECURSIVE department_hierarchy AS (
                -- 基础情况：根部门
                SELECT 
                    id,
                    name,
                    parent_id,
                    1 as level,
                    name as full_path
                FROM departments 
                WHERE parent_id IS NULL OR parent_id = 0
                
                UNION ALL
                
                -- 递归情况：子部门
                SELECT 
                    d.id,
                    d.name,
                    d.parent_id,
                    dh.level + 1 as level,
                    dh.full_path || '/' || d.name as full_path
                FROM departments d
                JOIN department_hierarchy dh ON d.parent_id = dh.id
            )
            UPDATE departments 
            SET 
                level = (SELECT level FROM department_hierarchy WHERE department_hierarchy.id = departments.id),
                full_path = (SELECT full_path FROM department_hierarchy WHERE department_hierarchy.id = departments.id),
                updated_at = ?
            WHERE EXISTS (SELECT 1 FROM department_hierarchy WHERE department_hierarchy.id = departments.id)
        )";
        
        // 如果指定了部门ID，添加条件只更新该部门及其子部门
        if (departmentId > 0) {
            updateSql += R"(
                AND departments.id IN (
                    WITH RECURSIVE children AS (
                        SELECT id FROM departments WHERE id = ?
                        UNION ALL
                        SELECT d.id FROM departments d
                        JOIN children c ON d.parent_id = c.id
                    )
                    SELECT id FROM children
                )
            )";
        }
        
        query.prepare(updateSql);
        query.addBindValue(QDateTime::currentDateTime());
        
        if (departmentId > 0) {
            query.addBindValue(departmentId);
        }
        
        if (!query.exec()) {
            qWarning() << "DepartmentDao: Failed to update department paths:" << query.lastError().text();
            return false;
        }
        
        qDebug() << "DepartmentDao: Updated" << query.numRowsAffected() << "department paths";
        return true;
        
    } catch (const std::exception& e) {
        qWarning() << "DepartmentDao: Exception in updateDepartmentPaths:" << e.what();
        return false;
    }
}

// ========== 私有方法 ==========

Department DepartmentDao::buildDepartmentFromQuery(const QSqlQuery& query)
{
    Department department;
    
    department.setId(query.value("id").toInt());
    department.setName(query.value("name").toString());
    department.setCode(query.value("code").toString());
    department.setParentId(query.value("parent_id").toInt());
    department.setDescription(query.value("description").toString());
    department.setStatus(static_cast<Department::Status>(query.value("status").toInt()));
    department.setSortOrder(query.value("sort_order").toInt());
    department.setCreatedAt(query.value("created_at").toDateTime());
    department.setUpdatedAt(query.value("updated_at").toDateTime());
    department.setLevel(query.value("level").toInt());
    department.setFullPath(query.value("full_path").toString());
    
    return department;
}

QList<std::shared_ptr<DepartmentTreeNode>> DepartmentDao::buildTreeNodes(
    const QList<Department>& departments, int parentId)
{
    QList<std::shared_ptr<DepartmentTreeNode>> nodes;
    
    for (const Department& dept : departments) {
        if (dept.parentId() == parentId) {
            auto node = std::make_shared<DepartmentTreeNode>(dept);
            
            // 递归构建子节点
            auto children = buildTreeNodes(departments, dept.id());
            for (auto& child : children) {
                node->addChild(child);
            }
            
            nodes.append(node);
        }
    }
    
    return nodes;
}

QPair<bool, QString> DepartmentDao::validateDepartment(const Department& department)
{
    if (!department.isValid()) {
        return qMakePair(false, "部门数据无效");
    }
    
    // 部门名称验证
    QString name = department.name().trimmed();
    if (name.isEmpty()) {
        return qMakePair(false, "部门名称不能为空！\n要求：1-100个字符，支持中英文");
    }
    if (name.length() > 100) {
        return qMakePair(false, QString("部门名称过长！\n当前：%1个字符，要求：不超过100个字符").arg(name.length()));
    }
    
    // 部门代码验证
    QString code = department.code().trimmed();
    if (code.isEmpty()) {
        return qMakePair(false, "部门代码不能为空！\n要求：1-20个字符，建议使用英文大写字母");
    }
    if (code.length() > 20) {
        return qMakePair(false, QString("部门代码过长！\n当前：%1个字符，要求：不超过20个字符").arg(code.length()));
    }
    
    // 代码格式验证（建议使用字母和数字）
    bool hasValidChar = true;
    for (const QChar& ch : code) {
        if (!ch.isLetterOrNumber() && ch != '_' && ch != '-') {
            hasValidChar = false;
            break;
        }
    }
    if (!hasValidChar) {
        return qMakePair(false, "部门代码格式不正确！\n要求：只能包含字母、数字、下划线(_)和连字符(-)");
    }
    
    // 部门描述验证
    if (department.description().length() > 500) {
        return qMakePair(false, QString("部门描述过长！\n当前：%1个字符，要求：不超过500个字符").arg(department.description().length()));
    }
    
    // 检查是否形成循环引用
    if (department.id() > 0 && department.parentId() == department.id()) {
        return qMakePair(false, "部门不能以自己作为上级部门！");
    }
    
    return qMakePair(true, "");
}

QList<Department> DepartmentDao::getDepartmentPath(int departmentId)
{
    QList<Department> path;
    
    if (!m_dbProvider || departmentId <= 0) {
        return path;
    }
    
    QSqlDatabase db = m_dbProvider->getDatabase();
    if (!db.isOpen()) {
        qWarning() << "DepartmentDao::getDepartmentPath: Database is not open";
        return path;
    }
    
    try {
        // 从指定部门开始，向上遍历到根部门
        int currentId = departmentId;
        
        while (currentId > 0) {
            QSqlQuery query(db);
            query.prepare(
                "SELECT id, name, code, parent_id, description, status, "
                "sort_order, level, full_path, created_at, updated_at "
                "FROM departments WHERE id = ?"
            );
            query.addBindValue(currentId);
            
            if (!query.exec()) {
                qWarning() << "DepartmentDao::getDepartmentPath: Query failed:" << query.lastError().text();
                break;
            }
            
            if (!query.next()) {
                qWarning() << "DepartmentDao::getDepartmentPath: Department not found with ID:" << currentId;
                break;
            }
            
            // 构建部门对象
            Department dept = buildDepartmentFromQuery(query);
            path.prepend(dept); // 添加到路径的开头，这样最终路径是从根到叶子
            
            // 移动到父部门
            currentId = dept.parentId();
            
            // 防止无限循环
            if (path.size() > 20) { // 假设部门层级不会超过20层
                qWarning() << "DepartmentDao::getDepartmentPath: Too many levels, possible circular reference";
                break;
            }
        }
        
    } catch (const std::exception& e) {
        qWarning() << "DepartmentDao::getDepartmentPath: Exception:" << e.what();
        path.clear();
    }
    
    return path;
}

} // namespace AccessControl 