#include "AutoLoginDialog.h"
#include <QApplication>
#include <QTimer>
#include <QDebug>

namespace AccessControl {

AutoLoginDialog::AutoLoginDialog(QWidget *parent)
    : QDialog(parent)
    , m_settings(new QSettings("AccessControl", "LoginSettings", this))
{
    setWindowTitle("自动登录设置");
    setWindowFlags(Qt::Dialog | Qt::WindowCloseButtonHint);
    setModal(true);
    setFixedSize(400, 350);
    
    initializeUI();
    initializeStyles();
    initializeConnections();
    loadCurrentSettings();
}

AutoLoginDialog::~AutoLoginDialog() {
}

// ========== 槽函数实现 ==========

void AutoLoginDialog::onAutoLoginToggled(bool enabled) {
    // 如果启用自动登录，必须同时启用记住密码
    if (enabled) {
        m_rememberPasswordCheck->setChecked(true);
    }
    
    // 更新控件启用状态
    m_usernameEdit->setEnabled(enabled);
    m_passwordEdit->setEnabled(enabled || m_rememberPasswordCheck->isChecked());
    
    updateStatus(enabled ? "自动登录已启用" : "自动登录已禁用");
}

void AutoLoginDialog::onRememberPasswordToggled(bool enabled) {
    // 如果禁用记住密码，必须同时禁用自动登录
    if (!enabled) {
        m_enableAutoLoginCheck->setChecked(false);
    }
    
    // 更新控件启用状态
    m_passwordEdit->setEnabled(enabled || m_enableAutoLoginCheck->isChecked());
    
    updateStatus(enabled ? "已启用记住密码" : "已禁用记住密码");
}

void AutoLoginDialog::applySettings() {
    if (!validateInput()) {
        return;
    }
    
    try {
        saveSettings();
        
        // 验证设置是否正确保存
        m_settings->sync();
        bool savedAutoLogin = m_settings->value("autoLogin", false).toBool();
        bool savedRememberPassword = m_settings->value("rememberPassword", false).toBool();
        QString savedUsername = m_settings->value("username").toString();
        
        qDebug() << "Settings saved and verified:";
        qDebug() << "autoLogin:" << savedAutoLogin;
        qDebug() << "rememberPassword:" << savedRememberPassword;
        qDebug() << "username:" << savedUsername;
        
        updateStatus("设置已保存并验证", false);
        
        // 延迟关闭对话框
        QTimer::singleShot(1000, this, &QDialog::accept);
        
    } catch (const std::exception& e) {
        updateStatus(QString("保存设置失败: %1").arg(e.what()), true);
    }
}

void AutoLoginDialog::resetToDefault() {
    int ret = QMessageBox::question(this, "重置确认",
                                   "确定要重置为默认设置吗？这将清除所有自动登录相关的设置。",
                                   QMessageBox::Yes | QMessageBox::No,
                                   QMessageBox::No);
    
    if (ret == QMessageBox::Yes) {
        // 重置所有控件
        m_enableAutoLoginCheck->setChecked(false);
        m_rememberPasswordCheck->setChecked(false);
        m_usernameEdit->clear();
        m_passwordEdit->clear();
        
        // 清除设置
        m_settings->remove("autoLogin");
        m_settings->remove("rememberPassword");
        m_settings->remove("username");
        m_settings->remove("password");
        m_settings->sync();
        
        updateStatus("已重置为默认设置", false);
    }
}

// ========== 私有方法实现 ==========

void AutoLoginDialog::initializeUI() {
    // 创建主布局
    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setSpacing(15);
    m_mainLayout->setContentsMargins(20, 20, 20, 20);
    
    // 创建自动登录设置组
    m_autoLoginGroup = new QGroupBox("自动登录设置");
    m_formLayout = new QFormLayout(m_autoLoginGroup);
    m_formLayout->setSpacing(10);
    
    // 创建控件
    m_enableAutoLoginCheck = new QCheckBox("启用自动登录");
    m_enableAutoLoginCheck->setToolTip("启用后，程序启动时将自动使用保存的用户名和密码登录");
    
    m_rememberPasswordCheck = new QCheckBox("记住密码");
    m_rememberPasswordCheck->setToolTip("保存密码以便下次登录时自动填入");
    
    m_usernameEdit = new QLineEdit();
    m_usernameEdit->setPlaceholderText("请输入用户名");
    m_usernameEdit->setEnabled(false);
    
    m_passwordEdit = new QLineEdit();
    m_passwordEdit->setEchoMode(QLineEdit::Password);
    m_passwordEdit->setPlaceholderText("请输入密码");
    m_passwordEdit->setEnabled(false);
    
    // 添加到表单布局
    m_formLayout->addRow(m_enableAutoLoginCheck);
    m_formLayout->addRow(m_rememberPasswordCheck);
    m_formLayout->addRow("用户名:", m_usernameEdit);
    m_formLayout->addRow("密码:", m_passwordEdit);
    
    // 状态标签
    m_statusLabel = new QLabel();
    m_statusLabel->setAlignment(Qt::AlignCenter);
    m_statusLabel->setWordWrap(true);
    
    // 创建按钮布局
    m_buttonLayout = new QHBoxLayout();
    
    m_applyBtn = new QPushButton("应用");
    m_applyBtn->setDefault(true);
    
    m_resetBtn = new QPushButton("重置");
    
    m_closeBtn = new QPushButton("关闭");
    
    m_buttonLayout->addStretch();
    m_buttonLayout->addWidget(m_applyBtn);
    m_buttonLayout->addWidget(m_resetBtn);
    m_buttonLayout->addWidget(m_closeBtn);
    
    // 组装主布局
    m_mainLayout->addWidget(m_autoLoginGroup);
    m_mainLayout->addWidget(m_statusLabel);
    m_mainLayout->addLayout(m_buttonLayout);
}

void AutoLoginDialog::initializeStyles() {
    setStyleSheet(R"(
        QDialog {
            background-color: #f5f5f5;
        }
        
        QGroupBox {
            font-weight: bold;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            margin-top: 10px;
            padding-top: 10px;
            background-color: white;
        }
        
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 8px 0 8px;
            color: #1976D2;
        }
        
        QCheckBox {
            font-size: 14px;
            spacing: 8px;
        }
        
        QCheckBox::indicator {
            width: 18px;
            height: 18px;
        }
        
        QCheckBox::indicator:unchecked {
            border: 2px solid #ccc;
            border-radius: 3px;
            background-color: white;
        }
        
        QCheckBox::indicator:checked {
            border: 2px solid #1976D2;
            border-radius: 3px;
            background-color: #1976D2;
            image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxMiAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEwIDNMNC41IDguNUwyIDYiIHN0cm9rZT0id2hpdGUiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=);
        }
        
        QLineEdit {
            padding: 8px;
            border: 2px solid #e0e0e0;
            border-radius: 6px;
            font-size: 14px;
            background-color: white;
        }
        
        QLineEdit:focus {
            border-color: #1976D2;
        }
        
        QLineEdit:disabled {
            background-color: #f5f5f5;
            color: #999;
        }
        
        QPushButton {
            background-color: #1976D2;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            font-weight: bold;
            font-size: 14px;
            min-width: 80px;
        }
        
        QPushButton:hover {
            background-color: #1565C0;
        }
        
        QPushButton:pressed {
            background-color: #0D47A1;
        }
        
        QPushButton#resetBtn {
            background-color: #757575;
        }
        
        QPushButton#resetBtn:hover {
            background-color: #616161;
        }
        
        QPushButton#closeBtn {
            background-color: #757575;
        }
        
        QPushButton#closeBtn:hover {
            background-color: #616161;
        }
    )");
    
    // 设置按钮对象名称
    m_resetBtn->setObjectName("resetBtn");
    m_closeBtn->setObjectName("closeBtn");
}

void AutoLoginDialog::initializeConnections() {
    // 复选框信号连接
    connect(m_enableAutoLoginCheck, &QCheckBox::toggled, 
            this, &AutoLoginDialog::onAutoLoginToggled);
    connect(m_rememberPasswordCheck, &QCheckBox::toggled, 
            this, &AutoLoginDialog::onRememberPasswordToggled);
    
    // 按钮信号连接
    connect(m_applyBtn, &QPushButton::clicked, 
            this, &AutoLoginDialog::applySettings);
    connect(m_resetBtn, &QPushButton::clicked, 
            this, &AutoLoginDialog::resetToDefault);
    connect(m_closeBtn, &QPushButton::clicked, 
            this, &QDialog::reject);
    
    // 回车键应用设置
    connect(m_usernameEdit, &QLineEdit::returnPressed, 
            m_passwordEdit, QOverload<>::of(&QLineEdit::setFocus));
    connect(m_passwordEdit, &QLineEdit::returnPressed, 
            this, &AutoLoginDialog::applySettings);
}

void AutoLoginDialog::loadCurrentSettings() {
    // 加载当前设置
    bool autoLogin = m_settings->value("autoLogin", false).toBool();
    bool rememberPassword = m_settings->value("rememberPassword", false).toBool();
    QString username = m_settings->value("username").toString();
    
    m_enableAutoLoginCheck->setChecked(autoLogin);
    m_rememberPasswordCheck->setChecked(rememberPassword);
    m_usernameEdit->setText(username);
    
    // 如果记住密码，加载密码
    if (rememberPassword) {
        QString encryptedPassword = m_settings->value("password").toString();
        if (!encryptedPassword.isEmpty()) {
            QString password = decryptPassword(encryptedPassword);
            m_passwordEdit->setText(password);
        }
    }
    
    // 更新状态
    if (autoLogin) {
        updateStatus("当前已启用自动登录");
    } else if (rememberPassword) {
        updateStatus("当前已启用记住密码");
    } else {
        updateStatus("当前未启用自动登录功能");
    }
}

void AutoLoginDialog::saveSettings() {
    // 保存基本设置
    m_settings->setValue("autoLogin", m_enableAutoLoginCheck->isChecked());
    m_settings->setValue("rememberPassword", m_rememberPasswordCheck->isChecked());
    m_settings->setValue("username", m_usernameEdit->text().trimmed());
    
    // 保存密码
    if (m_rememberPasswordCheck->isChecked() && !m_passwordEdit->text().isEmpty()) {
        QString encryptedPassword = encryptPassword(m_passwordEdit->text());
        m_settings->setValue("password", encryptedPassword);
    } else {
        m_settings->remove("password");
    }
    
    // 同步设置
    m_settings->sync();
}

bool AutoLoginDialog::validateInput() {
    // 如果启用自动登录，必须有用户名和密码
    if (m_enableAutoLoginCheck->isChecked()) {
        if (m_usernameEdit->text().trimmed().isEmpty()) {
            updateStatus("启用自动登录时必须填写用户名", true);
            m_usernameEdit->setFocus();
            return false;
        }
        
        if (m_passwordEdit->text().isEmpty()) {
            updateStatus("启用自动登录时必须填写密码", true);
            m_passwordEdit->setFocus();
            return false;
        }
        
        if (m_passwordEdit->text().length() < 6) {
            updateStatus("密码长度不能少于6位", true);
            m_passwordEdit->setFocus();
            return false;
        }
    }
    
    // 如果只启用记住密码，需要用户名
    if (m_rememberPasswordCheck->isChecked() && !m_enableAutoLoginCheck->isChecked()) {
        if (m_usernameEdit->text().trimmed().isEmpty()) {
            updateStatus("启用记住密码时必须填写用户名", true);
            m_usernameEdit->setFocus();
            return false;
        }
    }
    
    return true;
}

QString AutoLoginDialog::encryptPassword(const QString& password) {
    // 使用简单的XOR加密（与LoginWindow保持一致）
    QString key = "AccessControlSystem2024!@#$";
    QByteArray passwordBytes = password.toUtf8();
    QByteArray keyBytes = key.toUtf8();
    QByteArray encrypted;
    
    for (int i = 0; i < passwordBytes.size(); ++i) {
        encrypted.append(passwordBytes[i] ^ keyBytes[i % keyBytes.size()]);
    }
    
    return encrypted.toBase64();
}

QString AutoLoginDialog::decryptPassword(const QString& encryptedPassword) {
    // 使用XOR解密（与LoginWindow保持一致）
    QString key = "AccessControlSystem2024!@#$";
    QByteArray encryptedBytes = QByteArray::fromBase64(encryptedPassword.toUtf8());
    QByteArray keyBytes = key.toUtf8();
    QByteArray decrypted;
    
    for (int i = 0; i < encryptedBytes.size(); ++i) {
        decrypted.append(encryptedBytes[i] ^ keyBytes[i % keyBytes.size()]);
    }
    
    return QString::fromUtf8(decrypted);
}

void AutoLoginDialog::updateStatus(const QString& message, bool isError) {
    if (message.isEmpty()) {
        m_statusLabel->clear();
        return;
    }
    
    if (isError) {
        m_statusLabel->setStyleSheet("color: #F44336; font-weight: bold; padding: 8px;");
    } else {
        m_statusLabel->setStyleSheet("color: #4CAF50; font-weight: bold; padding: 8px;");
    }
    
    m_statusLabel->setText(message);
}

} // namespace AccessControl 