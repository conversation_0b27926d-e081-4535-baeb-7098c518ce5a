#ifndef DATABASEFACTORY_H
#define DATABASEFACTORY_H

#include "IDatabaseProvider.h"
#include "../config/DatabaseConfig.h"
#include <memory>

namespace AccessControl {

/**
 * @brief 数据库工厂类
 * 根据配置创建相应的数据库提供者实例
 */
class DatabaseFactory {
public:
    /**
     * @brief 创建数据库提供者
     * @param type 数据库类型
     * @return 数据库提供者实例
     */
    static std::unique_ptr<IDatabaseProvider> createProvider(DatabaseConfig::DatabaseType type);
    
    /**
     * @brief 创建数据库提供者并连接
     * @param config 数据库配置
     * @return 已连接的数据库提供者实例，连接失败则返回nullptr
     */
    static std::unique_ptr<IDatabaseProvider> createAndConnect(const DatabaseConfig& config);
    
    /**
     * @brief 检查数据库类型是否支持
     * @param type 数据库类型
     * @return 是否支持
     */
    static bool isSupported(DatabaseConfig::DatabaseType type);
    
    /**
     * @brief 获取支持的数据库类型列表
     * @return 支持的数据库类型列表
     */
    static QList<DatabaseConfig::DatabaseType> getSupportedTypes();
    
    /**
     * @brief 获取数据库类型的显示名称
     * @param type 数据库类型
     * @return 显示名称
     */
    static QString getTypeName(DatabaseConfig::DatabaseType type);
    
    /**
     * @brief 测试数据库连接
     * @param config 数据库配置
     * @return 连接测试结果和错误信息
     */
    static QPair<bool, QString> testConnection(const DatabaseConfig& config);

private:
    DatabaseFactory() = delete; // 禁止实例化
};

} // namespace AccessControl

#endif // DATABASEFACTORY_H 