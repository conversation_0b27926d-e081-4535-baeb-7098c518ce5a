#ifndef DATABASECONFIG_H
#define DATABASECONFIG_H

#include <QString>
#include <QVariantMap>

namespace AccessControl {

/**
 * @brief 数据库配置结构
 * 支持SQLite、PostgreSQL、MySQL三种数据库类型
 */
struct DatabaseConfig {
    enum DatabaseType {
        SQLite = 0,
        PostgreSQL = 1,
        MySQL = 2
    };
    
    // 基本配置
    DatabaseType type = SQLite;         // 数据库类型
    QString host = "localhost";         // 主机地址
    int port = 0;                      // 端口号(0表示使用默认端口)
    QString database = "access_control"; // 数据库名
    QString username = "";             // 用户名
    QString password = "";             // 密码
    QString filePath = "access_control.db"; // SQLite文件路径
    
    // 连接池配置
    int maxConnections = 10;           // 最大连接数
    int minConnections = 2;            // 最小连接数
    int connectionTimeout = 30000;     // 连接超时(毫秒)
    
    // SSL配置(PostgreSQL/MySQL)
    bool useSSL = false;               // 是否使用SSL
    QString sslCert = "";              // SSL证书文件
    QString sslKey = "";               // SSL密钥文件
    QString sslCA = "";                // SSL CA文件
    
    // 其他选项
    QVariantMap options;               // 扩展配置选项
    
    /**
     * @brief 获取默认端口号
     * @return 数据库默认端口
     */
    int getDefaultPort() const;
    
    /**
     * @brief 生成连接字符串
     * @return 数据库连接字符串
     */
    QString getConnectionString() const;
    
    /**
     * @brief 验证配置有效性
     * @return 配置是否有效
     */
    bool isValid() const;
    
    /**
     * @brief 从JSON对象加载配置
     * @param json JSON配置对象
     */
    void fromJson(const QVariantMap& json);
    
    /**
     * @brief 转换为JSON对象
     * @return JSON配置对象
     */
    QVariantMap toJson() const;
    
    /**
     * @brief 获取数据库类型名称
     * @return 数据库类型字符串
     */
    QString getTypeName() const;
    
    /**
     * @brief 比较两个配置是否相等
     * @param other 另一个配置
     * @return 是否相等
     */
    bool operator==(const DatabaseConfig& other) const;
};

} // namespace AccessControl

#endif // DATABASECONFIG_H 