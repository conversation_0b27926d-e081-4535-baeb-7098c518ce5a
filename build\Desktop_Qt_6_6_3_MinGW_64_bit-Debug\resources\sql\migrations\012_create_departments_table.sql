-- 迁移文件：012_create_departments_table.sql
-- 描述：创建部门表
-- 创建时间：2025-07-26
-- 作者：AI Assistant

-- ========== 创建departments表 ==========

CREATE TABLE IF NOT EXISTS departments (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL,                    -- 部门名称
    code VARCHAR(20) NOT NULL UNIQUE,             -- 部门代码（唯一）
    parent_id INTEGER,                            -- 上级部门ID（NULL表示顶级部门）
    description TEXT,                             -- 部门描述
    status INTEGER NOT NULL DEFAULT 1,           -- 状态：1=启用，0=禁用
    sort_order INTEGER NOT NULL DEFAULT 0,       -- 排序顺序
    level INTEGER NOT NULL DEFAULT 0,            -- 部门层级
    full_path TEXT,                               -- 完整路径（如：公司/技术部/开发组）
    created_at DATETIME NOT NULL,                -- 创建时间
    updated_at DATETIME NOT NULL,                -- 更新时间
    
    -- 外键约束
    FOREIGN KEY (parent_id) REFERENCES departments(id) ON DELETE SET NULL
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_departments_parent_id ON departments(parent_id);
CREATE INDEX IF NOT EXISTS idx_departments_code ON departments(code);
CREATE INDEX IF NOT EXISTS idx_departments_status ON departments(status);
CREATE INDEX IF NOT EXISTS idx_departments_sort_order ON departments(sort_order);
CREATE INDEX IF NOT EXISTS idx_departments_name ON departments(name);

-- 插入示例数据
INSERT OR IGNORE INTO departments (name, code, parent_id, description, status, sort_order, level, full_path, created_at, updated_at) VALUES
('总公司', 'COMPANY', NULL, '总公司', 1, 1, 1, '总公司', datetime('now'), datetime('now')),
('技术部', 'TECH', 1, '技术研发部门', 1, 1, 2, '总公司/技术部', datetime('now'), datetime('now')),
('市场部', 'MARKET', 1, '市场营销部门', 1, 2, 2, '总公司/市场部', datetime('now'), datetime('now')),
('人事部', 'HR', 1, '人力资源部门', 1, 3, 2, '总公司/人事部', datetime('now'), datetime('now')),
('财务部', 'FINANCE', 1, '财务管理部门', 1, 4, 2, '总公司/财务部', datetime('now'), datetime('now')),
('开发组', 'DEV', 2, '软件开发组', 1, 1, 3, '总公司/技术部/开发组', datetime('now'), datetime('now')),
('测试组', 'TEST', 2, '软件测试组', 1, 2, 3, '总公司/技术部/测试组', datetime('now'), datetime('now')),
('运维组', 'OPS', 2, '系统运维组', 1, 3, 3, '总公司/技术部/运维组', datetime('now'), datetime('now')),
('前端组', 'FRONTEND', 6, '前端开发组', 1, 1, 4, '总公司/技术部/开发组/前端组', datetime('now'), datetime('now')),
('后端组', 'BACKEND', 6, '后端开发组', 1, 2, 4, '总公司/技术部/开发组/后端组', datetime('now'), datetime('now')); 