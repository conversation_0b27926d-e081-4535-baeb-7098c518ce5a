#ifndef IDATABASEPROVIDER_H
#define IDATABASEPROVIDER_H

#include <QString>
#include <QSqlQuery>
#include <QSqlDatabase>
#include <QVariant>
#include <QStringList>
#include <memory>
#include "../config/DatabaseConfig.h"

namespace AccessControl {

/**
 * @brief 数据库提供者接口
 * 定义统一的数据库操作接口，支持SQLite、PostgreSQL、MySQL
 */
class IDatabaseProvider {
public:
    virtual ~IDatabaseProvider() = default;
    
    // ========== 连接管理 ==========
    
    /**
     * @brief 连接数据库
     * @param config 数据库配置
     * @return 连接是否成功
     */
    virtual bool connect(const DatabaseConfig& config) = 0;
    
    /**
     * @brief 断开数据库连接
     */
    virtual void disconnect() = 0;
    
    /**
     * @brief 检查是否已连接
     * @return 是否已连接
     */
    virtual bool isConnected() const = 0;
    
    /**
     * @brief 重新连接数据库
     * @return 重连是否成功
     */
    virtual bool reconnect() = 0;
    
    /**
     * @brief 获取数据库连接对象
     * @return 数据库连接对象
     */
    virtual QSqlDatabase getDatabase() = 0;
    
    // ========== 查询操作 ==========
    
    /**
     * @brief 执行查询语句
     * @param sql SQL语句
     * @return 查询结果
     */
    virtual QSqlQuery query(const QString& sql) = 0;
    
    /**
     * @brief 执行SQL语句(无返回结果)
     * @param sql SQL语句
     * @return 执行是否成功
     */
    virtual bool execute(const QString& sql) = 0;
    
    /**
     * @brief 创建预编译查询
     * @param sql SQL语句模板
     * @return 预编译查询对象
     */
    virtual QSqlQuery prepareQuery(const QString& sql) = 0;
    
    /**
     * @brief 获取最后插入的ID
     * @return 最后插入的ID
     */
    virtual QVariant lastInsertId() = 0;
    
    /**
     * @brief 获取受影响的行数
     * @return 受影响的行数
     */
    virtual int numRowsAffected() = 0;
    
    // ========== 事务管理 ==========
    
    /**
     * @brief 开始事务
     * @return 是否成功
     */
    virtual bool transaction() = 0;
    
    /**
     * @brief 提交事务
     * @return 是否成功
     */
    virtual bool commit() = 0;
    
    /**
     * @brief 回滚事务
     * @return 是否成功
     */
    virtual bool rollback() = 0;
    
    // ========== 错误处理 ==========
    
    /**
     * @brief 获取最后错误信息
     * @return 错误信息
     */
    virtual QString lastError() const = 0;
    
    /**
     * @brief 获取错误代码
     * @return 错误代码
     */
    virtual int errorCode() const = 0;
    
    /**
     * @brief 检查是否有错误
     * @return 是否有错误
     */
    virtual bool hasError() const = 0;
    
    // ========== 数据库特性 ==========
    
    /**
     * @brief 获取数据库类型
     * @return 数据库类型名称
     */
    virtual QString databaseType() const = 0;
    
    /**
     * @brief 获取数据库版本
     * @return 数据库版本信息
     */
    virtual QString databaseVersion() const = 0;
    
    /**
     * @brief 获取所有表名
     * @return 表名列表
     */
    virtual QStringList tableNames() const = 0;
    
    /**
     * @brief 检查表是否存在
     * @param tableName 表名
     * @return 表是否存在
     */
    virtual bool tableExists(const QString& tableName) const = 0;
    
    /**
     * @brief 获取表的列信息
     * @param tableName 表名
     * @return 列信息列表
     */
    virtual QStringList columnNames(const QString& tableName) const = 0;
    
    // ========== 批量操作 ==========
    
    /**
     * @brief 批量执行SQL语句
     * @param sqlList SQL语句列表
     * @return 执行是否成功
     */
    virtual bool executeBatch(const QStringList& sqlList) = 0;
    
    /**
     * @brief 批量插入数据
     * @param tableName 表名
     * @param columns 列名列表
     * @param values 数据列表
     * @return 插入是否成功
     */
    virtual bool batchInsert(const QString& tableName, 
                           const QStringList& columns, 
                           const QList<QVariantList>& values) = 0;
    
    // ========== 数据库维护 ==========
    
    /**
     * @brief 优化数据库
     * @return 优化是否成功
     */
    virtual bool optimize() = 0;
    
    /**
     * @brief 检查数据库完整性
     * @return 检查结果
     */
    virtual bool checkIntegrity() = 0;
    
    /**
     * @brief 获取数据库大小(字节)
     * @return 数据库大小
     */
    virtual qint64 databaseSize() const = 0;
    
    // ========== 备份恢复 ==========
    
    /**
     * @brief 备份数据库
     * @param backupPath 备份文件路径
     * @return 备份是否成功
     */
    virtual bool backup(const QString& backupPath) = 0;
    
    /**
     * @brief 恢复数据库
     * @param backupPath 备份文件路径
     * @return 恢复是否成功
     */
    virtual bool restore(const QString& backupPath) = 0;
};

/**
 * @brief 数据库提供者智能指针类型定义
 */
using DatabaseProviderPtr = std::shared_ptr<IDatabaseProvider>;

} // namespace AccessControl

#endif // IDATABASEPROVIDER_H 