# This file is managed by Qt Creator, do not edit!

set("QT_MAINTENANCE_TOOL" "C:/Qt/MaintenanceTool.exe" CACHE "FILEPATH" "" FORCE)
set("CMAKE_CXX_COMPILER" "C:/Qt/Tools/mingw1120_64/bin/g++.exe" CACHE "FILEPATH" "" FORCE)
set("CMAKE_C_COMPILER" "C:/Qt/Tools/mingw1120_64/bin/gcc.exe" CACHE "FILEPATH" "" FORCE)
set("CMAKE_PROJECT_INCLUDE_BEFORE" "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/.qtc/package-manager/auto-setup.cmake" CACHE "FILEPATH" "" FORCE)
set("CMAKE_COLOR_DIAGNOSTICS" "ON" CACHE "BOOL" "" FORCE)
set("CMAKE_CXX_FLAGS_INIT" "" CACHE "STRING" "" FORCE)
set("CMAKE_BUILD_TYPE" "Debug" CACHE "STRING" "" FORCE)
set("CMAKE_GENERATOR" "Ninja" CACHE "STRING" "" FORCE)
set("CMAKE_PREFIX_PATH" "C:/Qt/6.6.3/mingw_64" CACHE "PATH" "" FORCE)
set("QT_QMAKE_EXECUTABLE" "C:/Qt/6.6.3/mingw_64/bin/qmake.exe" CACHE "FILEPATH" "" FORCE)