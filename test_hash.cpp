#include <QCoreApplication>
#include <QCryptographicHash>
#include <QString>
#include <QDebug>

int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    
    QString password = "admin";
    QString salt = "salt";
    QString combined = password + salt;
    
    QByteArray data = combined.toUtf8();
    QString hash = QCryptographicHash::hash(data, QCryptographicHash::Sha256).toHex();
    
    qDebug() << "Password:" << password;
    qDebug() << "Salt:" << salt;
    qDebug() << "Combined:" << combined;
    qDebug() << "SHA256 Hash:" << hash;
    
    return 0;
} 