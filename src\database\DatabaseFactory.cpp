#include "DatabaseFactory.h"
#include "providers/SQLiteProvider.h"
// #include "providers/PostgreSQLProvider.h"  // 暂时禁用避免MOC问题
// #include "providers/MySQLProvider.h"       // 后续实现
#include <QDebug>
#include <QSqlDatabase>

namespace AccessControl {

std::unique_ptr<IDatabaseProvider> DatabaseFactory::createProvider(DatabaseConfig::DatabaseType type) {
    switch (type) {
        case DatabaseConfig::SQLite:
            return std::make_unique<SQLiteProvider>();
            
        case DatabaseConfig::PostgreSQL:
            // PostgreSQL暂时禁用避免MOC问题
            qWarning() << "PostgreSQL提供者暂时禁用";
            return nullptr;
            // TODO: 重新启用PostgreSQL支持
            /*
            // 检查PostgreSQL驱动是否可用
            if (QSqlDatabase::isDriverAvailable("QPSQL")) {
                return std::make_unique<PostgreSQLProvider>();
            } else {
                qWarning() << "QPSQL驱动不可用，请安装PostgreSQL驱动";
                return nullptr;
            }
            */
            
        case DatabaseConfig::MySQL:
            // TODO: 实现MySQL提供者
            qWarning() << "MySQL提供者尚未实现";
            return nullptr;
            
        default:
            qWarning() << "不支持的数据库类型:" << type;
            return nullptr;
    }
}

std::unique_ptr<IDatabaseProvider> DatabaseFactory::createAndConnect(const DatabaseConfig& config) {
    auto provider = createProvider(config.type);
    if (!provider) {
        return nullptr;
    }
    
    if (!provider->connect(config)) {
        qWarning() << "数据库连接失败:" << provider->lastError();
        return nullptr;
    }
    
    return provider;
}

bool DatabaseFactory::isSupported(DatabaseConfig::DatabaseType type) {
    switch (type) {
        case DatabaseConfig::SQLite:
            return QSqlDatabase::isDriverAvailable("QSQLITE");
            
        case DatabaseConfig::PostgreSQL:
            return QSqlDatabase::isDriverAvailable("QPSQL");
            
        case DatabaseConfig::MySQL:
            return QSqlDatabase::isDriverAvailable("QMYSQL");
            
        default:
            return false;
    }
}

QList<DatabaseConfig::DatabaseType> DatabaseFactory::getSupportedTypes() {
    QList<DatabaseConfig::DatabaseType> types;
    
    // 动态检测SQLite驱动
    if (QSqlDatabase::isDriverAvailable("QSQLITE")) {
        types << DatabaseConfig::SQLite;
    }
    
    // 动态检测PostgreSQL驱动
    if (QSqlDatabase::isDriverAvailable("QPSQL")) {
        types << DatabaseConfig::PostgreSQL;
    }
    
    // 动态检测MySQL驱动
    if (QSqlDatabase::isDriverAvailable("QMYSQL")) {
        types << DatabaseConfig::MySQL;
    }
    
    return types;
}

QString DatabaseFactory::getTypeName(DatabaseConfig::DatabaseType type) {
    switch (type) {
        case DatabaseConfig::SQLite:
            return "SQLite";
        case DatabaseConfig::PostgreSQL:
            return "PostgreSQL";
        case DatabaseConfig::MySQL:
            return "MySQL";
        default:
            return "Unknown";
    }
}

QPair<bool, QString> DatabaseFactory::testConnection(const DatabaseConfig& config) {
    auto provider = createProvider(config.type);
    if (!provider) {
        return qMakePair(false, QString("不支持的数据库类型: %1").arg(getTypeName(config.type)));
    }
    
    if (!provider->connect(config)) {
        return qMakePair(false, provider->lastError());
    }
    
    // 测试基本查询
    try {
        QString testSql;
        switch (config.type) {
            case DatabaseConfig::SQLite:
                testSql = "SELECT sqlite_version()";
                break;
            case DatabaseConfig::PostgreSQL:
                testSql = "SELECT version()";
                break;
            case DatabaseConfig::MySQL:
                testSql = "SELECT VERSION()";
                break;
            default:
                testSql = "SELECT 1";
                break;
        }
        
        auto query = provider->query(testSql);
        if (provider->hasError()) {
            return qMakePair(false, provider->lastError());
        }
        
        if (query.next()) {
            QString version = query.value(0).toString();
            return qMakePair(true, QString("连接成功 - %1 版本: %2")
                           .arg(getTypeName(config.type))
                           .arg(version));
        }
        
        return qMakePair(true, QString("连接成功 - %1").arg(getTypeName(config.type)));
        
    } catch (const std::exception& e) {
        return qMakePair(false, QString("连接测试异常: %1").arg(e.what()));
    }
}

} // namespace AccessControl 