#ifndef SQLITEPROVIDER_H
#define SQLITEPROVIDER_H

#include "../IDatabaseProvider.h"
#include <QSqlDatabase>
#include <QMutex>

namespace AccessControl {

/**
 * @brief SQLite数据库提供者
 * 实现SQLite数据库的具体操作
 */
class SQLiteProvider : public IDatabaseProvider {
public:
    SQLiteProvider();
    ~SQLiteProvider() override;
    
    // ========== 连接管理 ==========
    bool connect(const DatabaseConfig& config) override;
    void disconnect() override;
    bool isConnected() const override;
    bool reconnect() override;
    QSqlDatabase getDatabase() override;
    
    // ========== 查询操作 ==========
    QSqlQuery query(const QString& sql) override;
    bool execute(const QString& sql) override;
    QSqlQuery prepareQuery(const QString& sql) override;
    QVariant lastInsertId() override;
    int numRowsAffected() override;
    
    // ========== 事务管理 ==========
    bool transaction() override;
    bool commit() override;
    bool rollback() override;
    
    // ========== 错误处理 ==========
    QString lastError() const override;
    int errorCode() const override;
    bool hasError() const override;
    
    // ========== 数据库特性 ==========
    QString databaseType() const override;
    QString databaseVersion() const override;
    QStringList tableNames() const override;
    bool tableExists(const QString& tableName) const override;
    QStringList columnNames(const QString& tableName) const override;
    
    // ========== 批量操作 ==========
    bool executeBatch(const QStringList& sqlList) override;
    bool batchInsert(const QString& tableName, 
                    const QStringList& columns, 
                    const QList<QVariantList>& values) override;
    
    // ========== 数据库维护 ==========
    bool optimize() override;
    bool checkIntegrity() override;
    qint64 databaseSize() const override;
    
    // ========== 备份恢复 ==========
    bool backup(const QString& backupPath) override;
    bool restore(const QString& backupPath) override;
    
    // ========== SQLite特有功能 ==========
    
    /**
     * @brief 启用外键约束
     * @param enable 是否启用
     * @return 操作是否成功
     */
    bool enableForeignKeys(bool enable = true);
    
    /**
     * @brief 设置WAL模式
     * @param enable 是否启用WAL模式
     * @return 操作是否成功
     */
    bool enableWALMode(bool enable = true);
    
    /**
     * @brief 执行VACUUM命令
     * @return 操作是否成功
     */
    bool vacuum();
    
    /**
     * @brief 设置页大小
     * @param pageSize 页大小(字节)
     * @return 操作是否成功
     */
    bool setPageSize(int pageSize);
    
    /**
     * @brief 设置缓存大小
     * @param cacheSize 缓存大小(页数)
     * @return 操作是否成功
     */
    bool setCacheSize(int cacheSize);

private:
    QString m_connectionName;      // 连接名称
    QSqlDatabase m_database;       // 数据库连接
    DatabaseConfig m_config;       // 数据库配置
    mutable QMutex m_mutex;        // 线程安全锁
    QString m_lastError;           // 最后错误信息
    int m_errorCode;               // 错误代码
    
    /**
     * @brief 生成唯一的连接名称
     * @return 连接名称
     */
    QString generateConnectionName();
    
    /**
     * @brief 初始化SQLite设置 (线程安全版本)
     * @return 初始化是否成功
     */
    bool initializeSettings();
    
    /**
     * @brief 检查并创建数据库文件目录 (线程安全版本)
     * @param filePath 数据库文件路径
     * @return 操作是否成功
     */
    bool ensureDatabaseDirectory(const QString& filePath);
    
    /**
     * @brief 设置错误信息 (线程安全版本)
     * @param error 错误信息
     * @param code 错误代码
     */
    void setError(const QString& error, int code = -1);
    
    /**
     * @brief 清除错误信息 (线程安全版本)
     */
    void clearError();
    
    // ========== 无锁版本方法 (仅限内部调用) ==========
    
    /**
     * @brief 初始化SQLite设置 (无锁版本 - 仅限内部调用)
     * @return 初始化是否成功
     */
    bool initializeSettingsUnsafe();
    
    /**
     * @brief 检查并创建数据库文件目录 (无锁版本 - 仅限内部调用)
     * @param filePath 数据库文件路径
     * @return 操作是否成功
     */
    bool ensureDatabaseDirectoryUnsafe(const QString& filePath);
    
    /**
     * @brief 设置错误信息 (无锁版本 - 仅限内部调用)
     * @param error 错误信息
     * @param code 错误代码
     */
    void setErrorUnsafe(const QString& error, int code = -1);
    
    /**
     * @brief 清除错误信息 (无锁版本 - 仅限内部调用)
     */
    void clearErrorUnsafe();
    
    /**
     * @brief 检查数据库连接状态 (无锁版本 - 仅限内部调用)
     * @return 是否已连接
     */
    bool isConnectedUnsafe() const;
    
    /**
     * @brief 断开数据库连接 (无锁版本 - 仅限内部调用)
     */
    void disconnectUnsafe();
    
    /**
     * @brief 执行SQL语句 (无锁版本 - 仅限内部调用)
     * @param sql SQL语句
     * @return 执行是否成功
     */
    bool executeUnsafe(const QString& sql);
    
    /**
     * @brief 开始事务 (无锁版本 - 仅限内部调用)
     * @return 操作是否成功
     */
    bool transactionUnsafe();
    
    /**
     * @brief 提交事务 (无锁版本 - 仅限内部调用)
     * @return 操作是否成功
     */
    bool commitUnsafe();
    
    /**
     * @brief 回滚事务 (无锁版本 - 仅限内部调用)
     * @return 操作是否成功
     */
    bool rollbackUnsafe();
};

} // namespace AccessControl

#endif // SQLITEPROVIDER_H 