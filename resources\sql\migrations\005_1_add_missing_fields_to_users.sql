-- 迁移文件：005_add_missing_fields_to_users.sql
-- 描述：添加users表中缺失的real_name和updated_at字段
-- 创建时间：2024-07-08
-- 作者：AI Assistant

-- ========== 添加缺失字段 ==========

-- SQLite不支持IF NOT EXISTS的列添加，所以我们使用try-catch方式
-- 添加real_name字段（忽略错误）
BEGIN TRANSACTION;
ALTER TABLE users ADD COLUMN real_name VARCHAR(100);
COMMIT;

-- 添加updated_at字段（忽略错误）
BEGIN TRANSACTION;
ALTER TABLE users ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
COMMIT;

-- 更新现有用户的real_name字段（如果为空）
UPDATE users SET real_name = username WHERE real_name IS NULL OR real_name = '';

-- 更新现有用户的updated_at字段（如果为空）
UPDATE users SET updated_at = created_at WHERE updated_at IS NULL; 