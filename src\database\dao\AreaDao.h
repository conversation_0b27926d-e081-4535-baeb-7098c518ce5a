#ifndef AREADAO_H
#define AREADAO_H

#include <QList>
#include <QString>
#include <QSqlDatabase>
#include <memory>
#include "../../models/Area.h"
#include "../IDatabaseProvider.h"

namespace AccessControl {

/**
 * @brief 区域数据访问对象
 * 提供区域的CRUD操作和树形结构查询
 */
class AreaDao {
public:
    explicit AreaDao(std::shared_ptr<IDatabaseProvider> dbProvider);
    ~AreaDao();

    // ========== 基本CRUD操作 ==========
    int createArea(Area& area);
    bool updateArea(const Area& area);
    bool deleteArea(int areaId);
    Area findById(int areaId);
    Area findByCode(const QString& code);

    // ========== 查询操作 ==========
    QList<Area> findAll();
    QList<Area> findActive();
    QList<Area> findByParentId(int parentId);
    QList<Area> findByName(const QString& name);
    QList<std::shared_ptr<AreaTreeNode>> getAreaTree();
    QList<Area> getAllChildren(int areaId);
    QList<Area> getAreaPath(int areaId);

    // ========== 业务逻辑方法 ==========
    bool moveArea(int areaId, int newParentId);
    bool isCodeExists(const QString& code, int excludeId = 0);
    bool canDelete(int areaId);
    struct AreaStats {
        int childCount;
        int directControllerCount;
        int totalControllerCount;
    };
    AreaStats getAreaStats(int areaId);
    bool updateAreaPaths(int areaId = 0);

    // ========== 批量操作 ==========
    int batchImport(const QList<Area>& areas);
    bool exportAreas(const QString& format, const QString& filePath);

private:
    std::shared_ptr<IDatabaseProvider> m_dbProvider;
    Area buildAreaFromQuery(const QSqlQuery& query);
    QList<std::shared_ptr<AreaTreeNode>> buildTreeNodes(const QList<Area>& areas, int parentId);
    void calculateAreaPath(int areaId, QList<Area>& pathList);
    QPair<bool, QString> validateArea(const Area& area);
};

} // namespace AccessControl

#endif // AREADAO_H 