#ifndef DEPARTMENTDAO_H
#define DEPARTMENTDAO_H

#include <QList>
#include <QString>
#include <QSqlDatabase>
#include <memory>
#include "../../models/Department.h"
#include "../IDatabaseProvider.h"

namespace AccessControl {

/**
 * @brief 部门数据访问对象
 * 提供部门的CRUD操作和树形结构查询
 */
class DepartmentDao {
public:
    /**
     * @brief 构造函数
     * @param dbProvider 数据库提供者
     */
    explicit DepartmentDao(std::shared_ptr<IDatabaseProvider> dbProvider);
    
    /**
     * @brief 析构函数
     */
    ~DepartmentDao();

    // ========== 基本CRUD操作 ==========
    
    /**
     * @brief 创建部门
     * @param department 部门对象
     * @return 创建成功返回部门ID，失败返回-1
     */
    int createDepartment(Department& department);
    
    /**
     * @brief 更新部门信息
     * @param department 部门对象
     * @return 更新成功返回true，失败返回false
     */
    bool updateDepartment(const Department& department);
    
    /**
     * @brief 删除部门
     * @param departmentId 部门ID
     * @return 删除成功返回true，失败返回false
     */
    bool deleteDepartment(int departmentId);
    
    /**
     * @brief 根据ID查找部门
     * @param departmentId 部门ID
     * @return 部门对象，如果不存在则返回空对象
     */
    Department findById(int departmentId);
    
    /**
     * @brief 根据部门代码查找部门
     * @param code 部门代码
     * @return 部门对象，如果不存在则返回空对象
     */
    Department findByCode(const QString& code);

    // ========== 查询操作 ==========
    
    /**
     * @brief 获取所有部门
     * @return 部门列表
     */
    QList<Department> findAll();
    
    /**
     * @brief 获取启用的部门
     * @return 启用的部门列表
     */
    QList<Department> findActive();
    
    /**
     * @brief 根据父部门ID获取子部门
     * @param parentId 父部门ID，0表示根部门
     * @return 子部门列表
     */
    QList<Department> findByParentId(int parentId);
    
    /**
     * @brief 根据名称搜索部门
     * @param name 部门名称（支持模糊匹配）
     * @return 匹配的部门列表
     */
    QList<Department> findByName(const QString& name);
    
    /**
     * @brief 获取部门树形结构
     * @return 根部门节点列表
     */
    QList<std::shared_ptr<DepartmentTreeNode>> getDepartmentTree();
    
    /**
     * @brief 获取指定部门的所有子部门（包括所有层级）
     * @param departmentId 部门ID
     * @return 所有子部门列表
     */
    QList<Department> getAllChildren(int departmentId);
    
    /**
     * @brief 获取部门的完整路径
     * @param departmentId 部门ID
     * @return 从根部门到指定部门的路径列表
     */
    QList<Department> getDepartmentPath(int departmentId);

    // ========== 业务逻辑方法 ==========
    
    /**
     * @brief 移动部门到新的父部门下
     * @param departmentId 要移动的部门ID
     * @param newParentId 新的父部门ID
     * @return 移动成功返回true，失败返回false
     */
    bool moveDepartment(int departmentId, int newParentId);
    
    /**
     * @brief 检查部门代码是否已存在
     * @param code 部门代码
     * @param excludeId 排除的部门ID（用于更新时检查）
     * @return 存在返回true，不存在返回false
     */
    bool isCodeExists(const QString& code, int excludeId = 0);
    
    /**
     * @brief 检查是否可以删除部门（没有子部门和关联用户）
     * @param departmentId 部门ID
     * @return 可以删除返回true，不可以返回false
     */
    bool canDelete(int departmentId);
    
    /**
     * @brief 获取部门统计信息
     * @param departmentId 部门ID
     * @return 统计信息：{子部门数量, 直接人员数量, 总人员数量}
     */
    struct DepartmentStats {
        int childCount;      // 子部门数量
        int directUserCount; // 直接人员数量
        int totalUserCount;  // 总人员数量（包括子部门）
    };
    DepartmentStats getDepartmentStats(int departmentId);
    
    /**
     * @brief 更新部门层级和路径信息
     * @param departmentId 部门ID，0表示更新所有部门
     * @return 更新成功返回true，失败返回false
     */
    bool updateDepartmentPaths(int departmentId = 0);

    // ========== 批量操作 ==========
    
    /**
     * @brief 批量导入部门
     * @param departments 部门列表
     * @return 成功导入的数量
     */
    int batchImport(const QList<Department>& departments);
    
    /**
     * @brief 导出部门数据
     * @param format 导出格式（"csv", "json"等）
     * @param filePath 输出文件路径
     * @return 导出成功返回true，失败返回false
     */
    bool exportDepartments(const QString& format, const QString& filePath);

private:
    std::shared_ptr<IDatabaseProvider> m_dbProvider;
    
    /**
     * @brief 从结果集构建部门对象
     * @param query 查询结果
     * @return 部门对象
     */
    Department buildDepartmentFromQuery(const QSqlQuery& query);
    
    /**
     * @brief 递归构建部门树
     * @param departments 部门列表
     * @param parentId 父部门ID
     * @return 子节点列表
     */
    QList<std::shared_ptr<DepartmentTreeNode>> buildTreeNodes(
        const QList<Department>& departments, int parentId);
    
    /**
     * @brief 递归计算部门路径
     * @param departmentId 部门ID
     * @param pathList 路径列表（输出参数）
     */
    void calculateDepartmentPath(int departmentId, QList<Department>& pathList);
    
    /**
     * @brief 验证部门数据
     * @param department 部门对象
     * @return 验证结果和错误消息
     */
    QPair<bool, QString> validateDepartment(const Department& department);
};

} // namespace AccessControl

#endif // DEPARTMENTDAO_H 