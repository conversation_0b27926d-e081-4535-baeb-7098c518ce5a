/****************************************************************************
** Meta object code from reading C++ file 'ConsumerCardDialog.h'
**
** Created by: The Qt Meta Object Compiler version 68 (Qt 6.6.3)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../src/views/ConsumerCardDialog.h"
#include <QtGui/qtextcursor.h>
#include <QtCore/qmetatype.h>

#if __has_include(<QtCore/qtmochelpers.h>)
#include <QtCore/qtmochelpers.h>
#else
QT_BEGIN_MOC_NAMESPACE
#endif


#include <memory>

#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'ConsumerCardDialog.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 68
#error "This file was generated using the moc from 6.6.3. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {

#ifdef QT_MOC_HAS_STRINGDATA
struct qt_meta_stringdata_CLASSAccessControlSCOPEConsumerCardDialogENDCLASS_t {};
constexpr auto qt_meta_stringdata_CLASSAccessControlSCOPEConsumerCardDialogENDCLASS = QtMocHelpers::stringData(
    "AccessControl::ConsumerCardDialog",
    "addCard",
    "",
    "editCard",
    "deleteCard",
    "validateCardNumber",
    "accept"
);
#else  // !QT_MOC_HAS_STRING_DATA
struct qt_meta_stringdata_CLASSAccessControlSCOPEConsumerCardDialogENDCLASS_t {
    uint offsetsAndSizes[14];
    char stringdata0[34];
    char stringdata1[8];
    char stringdata2[1];
    char stringdata3[9];
    char stringdata4[11];
    char stringdata5[19];
    char stringdata6[7];
};
#define QT_MOC_LITERAL(ofs, len) \
    uint(sizeof(qt_meta_stringdata_CLASSAccessControlSCOPEConsumerCardDialogENDCLASS_t::offsetsAndSizes) + ofs), len 
Q_CONSTINIT static const qt_meta_stringdata_CLASSAccessControlSCOPEConsumerCardDialogENDCLASS_t qt_meta_stringdata_CLASSAccessControlSCOPEConsumerCardDialogENDCLASS = {
    {
        QT_MOC_LITERAL(0, 33),  // "AccessControl::ConsumerCardDi..."
        QT_MOC_LITERAL(34, 7),  // "addCard"
        QT_MOC_LITERAL(42, 0),  // ""
        QT_MOC_LITERAL(43, 8),  // "editCard"
        QT_MOC_LITERAL(52, 10),  // "deleteCard"
        QT_MOC_LITERAL(63, 18),  // "validateCardNumber"
        QT_MOC_LITERAL(82, 6)   // "accept"
    },
    "AccessControl::ConsumerCardDialog",
    "addCard",
    "",
    "editCard",
    "deleteCard",
    "validateCardNumber",
    "accept"
};
#undef QT_MOC_LITERAL
#endif // !QT_MOC_HAS_STRING_DATA
} // unnamed namespace

Q_CONSTINIT static const uint qt_meta_data_CLASSAccessControlSCOPEConsumerCardDialogENDCLASS[] = {

 // content:
      12,       // revision
       0,       // classname
       0,    0, // classinfo
       5,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // slots: name, argc, parameters, tag, flags, initial metatype offsets
       1,    0,   44,    2, 0x08,    1 /* Private */,
       3,    0,   45,    2, 0x08,    2 /* Private */,
       4,    0,   46,    2, 0x08,    3 /* Private */,
       5,    0,   47,    2, 0x08,    4 /* Private */,
       6,    0,   48,    2, 0x08,    5 /* Private */,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

Q_CONSTINIT const QMetaObject AccessControl::ConsumerCardDialog::staticMetaObject = { {
    QMetaObject::SuperData::link<QDialog::staticMetaObject>(),
    qt_meta_stringdata_CLASSAccessControlSCOPEConsumerCardDialogENDCLASS.offsetsAndSizes,
    qt_meta_data_CLASSAccessControlSCOPEConsumerCardDialogENDCLASS,
    qt_static_metacall,
    nullptr,
    qt_incomplete_metaTypeArray<qt_meta_stringdata_CLASSAccessControlSCOPEConsumerCardDialogENDCLASS_t,
        // Q_OBJECT / Q_GADGET
        QtPrivate::TypeAndForceComplete<ConsumerCardDialog, std::true_type>,
        // method 'addCard'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'editCard'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'deleteCard'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'validateCardNumber'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'accept'
        QtPrivate::TypeAndForceComplete<void, std::false_type>
    >,
    nullptr
} };

void AccessControl::ConsumerCardDialog::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<ConsumerCardDialog *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->addCard(); break;
        case 1: _t->editCard(); break;
        case 2: _t->deleteCard(); break;
        case 3: _t->validateCardNumber(); break;
        case 4: _t->accept(); break;
        default: ;
        }
    }
    (void)_a;
}

const QMetaObject *AccessControl::ConsumerCardDialog::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *AccessControl::ConsumerCardDialog::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_CLASSAccessControlSCOPEConsumerCardDialogENDCLASS.stringdata0))
        return static_cast<void*>(this);
    return QDialog::qt_metacast(_clname);
}

int AccessControl::ConsumerCardDialog::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QDialog::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 5)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 5;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 5)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 5;
    }
    return _id;
}
QT_WARNING_POP
