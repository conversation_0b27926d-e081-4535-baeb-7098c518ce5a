#include "Consumer.h"
#include <QDebug>

namespace AccessControl {

Consumer::Consumer()
    : m_id(-1),
      m_workNumber(""),
      m_realName(""),
      m_phoneNumber(""),
      m_idNumber(""),
      m_departmentId(-1),
      m_status(Status::Inactive),
      m_accessEnabled(true),
      m_attendanceEnabled(true),
      m_shiftWork(false),
      m_validFrom(QDate::currentDate()),
      m_validUntil(QDate(2099, 12, 31)),
      m_createdAt(QDateTime::currentDateTime()),
      m_updatedAt(QDateTime::currentDateTime())
{
}

bool Consumer::isValid() const
{
    return m_status == Status::Active && isInValidPeriod();
}

bool Consumer::isExpired() const
{
    return QDate::currentDate() > m_validUntil;
}

bool Consumer::isInValidPeriod() const
{
    QDate today = QDate::currentDate();
    return today >= m_validFrom && today <= m_validUntil;
}

QString Consumer::getDisplayName() const
{
    if (!m_realName.isEmpty()) {
        return m_realName;
    }
    return m_workNumber;
}

QString Consumer::statusToString(Status status)
{
    switch (status) {
        case Status::Active: return "正常";
        case Status::Inactive: return "停用";
        case Status::Expired: return "过期";
        default: return "未知";
    }
}

} // namespace AccessControl 