-- 清理旧的user相关表脚本
-- 目的：删除重构前的所有user相关表，保持数据库清洁
-- 创建时间：2025-01-26
-- 说明：用户已完成consumers/operators分离重构，需删除所有user表

-- ========== 执行前检查 ==========

-- 1. 确认新表存在且有数据
SELECT 'consumers表记录数' as check_name, COUNT(*) as count FROM consumers
UNION ALL
SELECT 'operators表记录数', COUNT(*) FROM operators
UNION ALL 
SELECT 'consumer_cards表记录数', COUNT(*) FROM consumer_cards;

-- 2. 列出所有将要删除的user相关表
SELECT name as 'will_be_deleted' FROM sqlite_master 
WHERE type='table' AND (
    name LIKE '%user%' OR 
    name = 'user_profiles' OR
    name = 'user_cards' OR 
    name = 'user_cards_old' OR
    name = 'users'
);

-- ========== 删除旧表 ==========

-- 删除user相关的所有表
DROP TABLE IF EXISTS users;                    -- 原混合用户表
DROP TABLE IF EXISTS user_profiles;            -- 用户扩展信息表
DROP TABLE IF EXISTS user_cards;               -- 旧的用户卡片表
DROP TABLE IF EXISTS user_cards_old;           -- 备份的旧卡片表
DROP TABLE IF EXISTS user_cards_new;           -- 迁移中间表（如果存在）

-- 删除可能存在的其他user相关表
DROP TABLE IF EXISTS user_biometrics;          -- 用户生物识别表
DROP TABLE IF EXISTS user_photos;              -- 用户照片表
DROP TABLE IF EXISTS user_fingerprints;        -- 用户指纹表

-- ========== 删除相关视图（如果存在） ==========
DROP VIEW IF EXISTS users_view;
DROP VIEW IF EXISTS user_cards_view;

-- ========== 删除相关索引（如果独立存在） ==========
DROP INDEX IF EXISTS idx_users_username;
DROP INDEX IF EXISTS idx_users_real_name;
DROP INDEX IF EXISTS idx_users_role;
DROP INDEX IF EXISTS idx_users_status;
DROP INDEX IF EXISTS idx_users_department_id;
DROP INDEX IF EXISTS idx_users_work_number;
DROP INDEX IF EXISTS idx_users_phone_number;
DROP INDEX IF EXISTS idx_users_id_number;
DROP INDEX IF EXISTS idx_user_profiles_user_id;
DROP INDEX IF EXISTS idx_user_profiles_gender;
DROP INDEX IF EXISTS idx_user_profiles_education;
DROP INDEX IF EXISTS idx_user_profiles_organization;
DROP INDEX IF EXISTS idx_user_cards_user_id;
DROP INDEX IF EXISTS idx_user_cards_card_number;
DROP INDEX IF EXISTS idx_user_cards_card_type;
DROP INDEX IF EXISTS idx_user_cards_status;
DROP INDEX IF EXISTS idx_user_cards_is_primary_card;

-- ========== 删除相关触发器 ==========
DROP TRIGGER IF EXISTS trigger_users_update;
DROP TRIGGER IF EXISTS trigger_user_profiles_update;
DROP TRIGGER IF EXISTS trigger_user_cards_update;

-- ========== 验证删除结果 ==========

-- 确认所有user相关表已删除
SELECT 'user表删除检查' as check_type, 
       CASE WHEN COUNT(*) = 0 THEN '✓ 所有user表已删除' 
            ELSE '⚠ 仍有' || COUNT(*) || '个user表存在' 
       END as result
FROM sqlite_master 
WHERE type='table' AND name LIKE '%user%';

-- 确认核心表完整性
SELECT 'consumers' as table_name, COUNT(*) as record_count FROM consumers
UNION ALL
SELECT 'consumer_cards', COUNT(*) FROM consumer_cards  
UNION ALL
SELECT 'operators', COUNT(*) FROM operators
UNION ALL
SELECT 'departments', COUNT(*) FROM departments
UNION ALL
SELECT 'areas', COUNT(*) FROM areas;

-- 检查外键完整性
PRAGMA foreign_key_check;

-- ========== 完成信息 ==========
SELECT '✓ 用户表清理完成！' as status,
       'consumers和operators表保持完整' as note,
       datetime('now', 'localtime') as completed_at; 