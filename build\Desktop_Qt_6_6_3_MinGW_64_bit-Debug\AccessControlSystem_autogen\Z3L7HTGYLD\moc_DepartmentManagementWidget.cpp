/****************************************************************************
** Meta object code from reading C++ file 'DepartmentManagementWidget.h'
**
** Created by: The Qt Meta Object Compiler version 68 (Qt 6.6.3)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../src/views/DepartmentManagementWidget.h"
#include <QtGui/qtextcursor.h>
#include <QtCore/qmetatype.h>

#if __has_include(<QtCore/qtmochelpers.h>)
#include <QtCore/qtmochelpers.h>
#else
QT_BEGIN_MOC_NAMESPACE
#endif


#include <memory>

#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'DepartmentManagementWidget.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 68
#error "This file was generated using the moc from 6.6.3. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {

#ifdef QT_MOC_HAS_STRINGDATA
struct qt_meta_stringdata_CLASSAccessControlSCOPEDepartmentManagementWidgetENDCLASS_t {};
constexpr auto qt_meta_stringdata_CLASSAccessControlSCOPEDepartmentManagementWidgetENDCLASS = QtMocHelpers::stringData(
    "AccessControl::DepartmentManagementWidget",
    "onTreeSelectionChanged",
    "",
    "onAddDepartment",
    "onAddSubDepartment",
    "onEditDepartment",
    "onDeleteDepartment",
    "onSaveDepartment",
    "onCancelEdit",
    "onSearchDepartments",
    "onClearSearch",
    "onExpandAll",
    "onCollapseAll",
    "onImportDepartments",
    "onExportDepartments",
    "onShowContextMenu",
    "pos"
);
#else  // !QT_MOC_HAS_STRING_DATA
struct qt_meta_stringdata_CLASSAccessControlSCOPEDepartmentManagementWidgetENDCLASS_t {
    uint offsetsAndSizes[34];
    char stringdata0[42];
    char stringdata1[23];
    char stringdata2[1];
    char stringdata3[16];
    char stringdata4[19];
    char stringdata5[17];
    char stringdata6[19];
    char stringdata7[17];
    char stringdata8[13];
    char stringdata9[20];
    char stringdata10[14];
    char stringdata11[12];
    char stringdata12[14];
    char stringdata13[20];
    char stringdata14[20];
    char stringdata15[18];
    char stringdata16[4];
};
#define QT_MOC_LITERAL(ofs, len) \
    uint(sizeof(qt_meta_stringdata_CLASSAccessControlSCOPEDepartmentManagementWidgetENDCLASS_t::offsetsAndSizes) + ofs), len 
Q_CONSTINIT static const qt_meta_stringdata_CLASSAccessControlSCOPEDepartmentManagementWidgetENDCLASS_t qt_meta_stringdata_CLASSAccessControlSCOPEDepartmentManagementWidgetENDCLASS = {
    {
        QT_MOC_LITERAL(0, 41),  // "AccessControl::DepartmentMana..."
        QT_MOC_LITERAL(42, 22),  // "onTreeSelectionChanged"
        QT_MOC_LITERAL(65, 0),  // ""
        QT_MOC_LITERAL(66, 15),  // "onAddDepartment"
        QT_MOC_LITERAL(82, 18),  // "onAddSubDepartment"
        QT_MOC_LITERAL(101, 16),  // "onEditDepartment"
        QT_MOC_LITERAL(118, 18),  // "onDeleteDepartment"
        QT_MOC_LITERAL(137, 16),  // "onSaveDepartment"
        QT_MOC_LITERAL(154, 12),  // "onCancelEdit"
        QT_MOC_LITERAL(167, 19),  // "onSearchDepartments"
        QT_MOC_LITERAL(187, 13),  // "onClearSearch"
        QT_MOC_LITERAL(201, 11),  // "onExpandAll"
        QT_MOC_LITERAL(213, 13),  // "onCollapseAll"
        QT_MOC_LITERAL(227, 19),  // "onImportDepartments"
        QT_MOC_LITERAL(247, 19),  // "onExportDepartments"
        QT_MOC_LITERAL(267, 17),  // "onShowContextMenu"
        QT_MOC_LITERAL(285, 3)   // "pos"
    },
    "AccessControl::DepartmentManagementWidget",
    "onTreeSelectionChanged",
    "",
    "onAddDepartment",
    "onAddSubDepartment",
    "onEditDepartment",
    "onDeleteDepartment",
    "onSaveDepartment",
    "onCancelEdit",
    "onSearchDepartments",
    "onClearSearch",
    "onExpandAll",
    "onCollapseAll",
    "onImportDepartments",
    "onExportDepartments",
    "onShowContextMenu",
    "pos"
};
#undef QT_MOC_LITERAL
#endif // !QT_MOC_HAS_STRING_DATA
} // unnamed namespace

Q_CONSTINIT static const uint qt_meta_data_CLASSAccessControlSCOPEDepartmentManagementWidgetENDCLASS[] = {

 // content:
      12,       // revision
       0,       // classname
       0,    0, // classinfo
      14,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // slots: name, argc, parameters, tag, flags, initial metatype offsets
       1,    0,   98,    2, 0x08,    1 /* Private */,
       3,    0,   99,    2, 0x08,    2 /* Private */,
       4,    0,  100,    2, 0x08,    3 /* Private */,
       5,    0,  101,    2, 0x08,    4 /* Private */,
       6,    0,  102,    2, 0x08,    5 /* Private */,
       7,    0,  103,    2, 0x08,    6 /* Private */,
       8,    0,  104,    2, 0x08,    7 /* Private */,
       9,    0,  105,    2, 0x08,    8 /* Private */,
      10,    0,  106,    2, 0x08,    9 /* Private */,
      11,    0,  107,    2, 0x08,   10 /* Private */,
      12,    0,  108,    2, 0x08,   11 /* Private */,
      13,    0,  109,    2, 0x08,   12 /* Private */,
      14,    0,  110,    2, 0x08,   13 /* Private */,
      15,    1,  111,    2, 0x08,   14 /* Private */,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QPoint,   16,

       0        // eod
};

Q_CONSTINIT const QMetaObject AccessControl::DepartmentManagementWidget::staticMetaObject = { {
    QMetaObject::SuperData::link<QWidget::staticMetaObject>(),
    qt_meta_stringdata_CLASSAccessControlSCOPEDepartmentManagementWidgetENDCLASS.offsetsAndSizes,
    qt_meta_data_CLASSAccessControlSCOPEDepartmentManagementWidgetENDCLASS,
    qt_static_metacall,
    nullptr,
    qt_incomplete_metaTypeArray<qt_meta_stringdata_CLASSAccessControlSCOPEDepartmentManagementWidgetENDCLASS_t,
        // Q_OBJECT / Q_GADGET
        QtPrivate::TypeAndForceComplete<DepartmentManagementWidget, std::true_type>,
        // method 'onTreeSelectionChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onAddDepartment'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onAddSubDepartment'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onEditDepartment'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onDeleteDepartment'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onSaveDepartment'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onCancelEdit'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onSearchDepartments'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onClearSearch'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onExpandAll'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onCollapseAll'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onImportDepartments'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onExportDepartments'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onShowContextMenu'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QPoint &, std::false_type>
    >,
    nullptr
} };

void AccessControl::DepartmentManagementWidget::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<DepartmentManagementWidget *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->onTreeSelectionChanged(); break;
        case 1: _t->onAddDepartment(); break;
        case 2: _t->onAddSubDepartment(); break;
        case 3: _t->onEditDepartment(); break;
        case 4: _t->onDeleteDepartment(); break;
        case 5: _t->onSaveDepartment(); break;
        case 6: _t->onCancelEdit(); break;
        case 7: _t->onSearchDepartments(); break;
        case 8: _t->onClearSearch(); break;
        case 9: _t->onExpandAll(); break;
        case 10: _t->onCollapseAll(); break;
        case 11: _t->onImportDepartments(); break;
        case 12: _t->onExportDepartments(); break;
        case 13: _t->onShowContextMenu((*reinterpret_cast< std::add_pointer_t<QPoint>>(_a[1]))); break;
        default: ;
        }
    }
}

const QMetaObject *AccessControl::DepartmentManagementWidget::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *AccessControl::DepartmentManagementWidget::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_CLASSAccessControlSCOPEDepartmentManagementWidgetENDCLASS.stringdata0))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int AccessControl::DepartmentManagementWidget::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 14)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 14;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 14)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 14;
    }
    return _id;
}
QT_WARNING_POP
