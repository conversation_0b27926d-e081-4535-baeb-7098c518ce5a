#ifndef GLOBALSEARCHDIALOG_H
#define GLOBALSEARCHDIALOG_H

#include <QDialog>
#include <QLineEdit>
#include <QPushButton>
#include <QTableWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QLabel>
#include <memory>

namespace AccessControl {

class IDatabaseProvider;
class ConsumerDao;
class Consumer;

/**
 * @brief 全局搜索对话框
 *
 * 提供非模态的全局搜索功能，支持在门禁卡持有者的任意字段中进行模糊查询
 */
class GlobalSearchDialog : public QDialog
{
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param dbProvider 数据库提供者
     * @param parent 父窗口
     */
    explicit GlobalSearchDialog(std::shared_ptr<IDatabaseProvider> dbProvider, QWidget *parent = nullptr);

    /**
     * @brief 析构函数
     */
    ~GlobalSearchDialog();

signals:
    /**
     * @brief 门禁卡持有者选择信号
     * @param consumerId 选中的门禁卡持有者ID
     */
    void consumerSelected(int consumerId);

public slots:
    /**
     * @brief 显示对话框并清空搜索历史
     */
    void showAndClear();

protected:
    /**
     * @brief 键盘事件处理
     * @param event 键盘事件
     */
    void keyPressEvent(QKeyEvent *event) override;

private slots:
    /**
     * @brief 执行搜索
     */
    void performSearch();

    /**
     * @brief 清空搜索
     */
    void clearSearch();

    /**
     * @brief 表格双击事件
     * @param row 行号
     * @param column 列号
     */
    void onTableDoubleClicked(int row, int column);

    /**
     * @brief 搜索文本变化事件
     */
    void onSearchTextChanged();

private:
    /**
     * @brief 初始化UI
     */
    void initUI();

    /**
     * @brief 设置连接
     */
    void setupConnections();

    /**
     * @brief 填充搜索结果
     * @param consumers 门禁卡持有者列表
     */
    void populateResults(const std::vector<Consumer>& consumers);

    /**
     * @brief 填充门禁卡持有者行数据
     * @param row 行号
     * @param consumer 门禁卡持有者对象
     */
    void fillConsumerRow(int row, const Consumer& consumer);

    /**
     * @brief 获取部门名称
     * @param departmentId 部门ID
     * @return 部门名称
     */
    QString getDepartmentName(int departmentId);

    /**
     * @brief 获取主卡号
     * @param consumerId 门禁卡持有者ID
     * @return 主卡号
     */
    QString getPrimaryCardNumber(int consumerId);

private:
    // 数据库相关
    std::shared_ptr<IDatabaseProvider> m_databaseProvider;
    std::unique_ptr<ConsumerDao> m_consumerDao;

    // UI控件
    QLineEdit* m_searchEdit;
    QPushButton* m_searchButton;
    QPushButton* m_clearButton;
    QPushButton* m_closeButton;
    QTableWidget* m_resultTable;
    QLabel* m_statusLabel;

    // 布局
    QVBoxLayout* m_mainLayout;
    QHBoxLayout* m_searchLayout;
    QHBoxLayout* m_buttonLayout;
};

} // namespace AccessControl

#endif // GLOBALSEARCHDIALOG_H
