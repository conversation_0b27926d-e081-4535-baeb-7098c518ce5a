/****************************************************************************
** Meta object code from reading C++ file 'CameraDialog.h'
**
** Created by: The Qt Meta Object Compiler version 68 (Qt 6.6.3)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../src/views/CameraDialog.h"
#include <QtGui/qtextcursor.h>
#include <QtCore/qmetatype.h>

#if __has_include(<QtCore/qtmochelpers.h>)
#include <QtCore/qtmochelpers.h>
#else
QT_BEGIN_MOC_NAMESPACE
#endif


#include <memory>

#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'CameraDialog.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 68
#error "This file was generated using the moc from 6.6.3. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {

#ifdef QT_MOC_HAS_STRINGDATA
struct qt_meta_stringdata_CLASSAccessControlSCOPECameraDialogENDCLASS_t {};
constexpr auto qt_meta_stringdata_CLASSAccessControlSCOPECameraDialogENDCLASS = QtMocHelpers::stringData(
    "AccessControl::CameraDialog",
    "imageCaptured",
    "",
    "imageData",
    "captureImage",
    "acceptPhoto",
    "retakePhoto",
    "cancelPhoto",
    "switchCamera",
    "onImageCaptured",
    "id",
    "image",
    "onCameraError",
    "onCaptureError",
    "QImageCapture::Error",
    "error",
    "errorString"
);
#else  // !QT_MOC_HAS_STRING_DATA
struct qt_meta_stringdata_CLASSAccessControlSCOPECameraDialogENDCLASS_t {
    uint offsetsAndSizes[34];
    char stringdata0[28];
    char stringdata1[14];
    char stringdata2[1];
    char stringdata3[10];
    char stringdata4[13];
    char stringdata5[12];
    char stringdata6[12];
    char stringdata7[12];
    char stringdata8[13];
    char stringdata9[16];
    char stringdata10[3];
    char stringdata11[6];
    char stringdata12[14];
    char stringdata13[15];
    char stringdata14[21];
    char stringdata15[6];
    char stringdata16[12];
};
#define QT_MOC_LITERAL(ofs, len) \
    uint(sizeof(qt_meta_stringdata_CLASSAccessControlSCOPECameraDialogENDCLASS_t::offsetsAndSizes) + ofs), len 
Q_CONSTINIT static const qt_meta_stringdata_CLASSAccessControlSCOPECameraDialogENDCLASS_t qt_meta_stringdata_CLASSAccessControlSCOPECameraDialogENDCLASS = {
    {
        QT_MOC_LITERAL(0, 27),  // "AccessControl::CameraDialog"
        QT_MOC_LITERAL(28, 13),  // "imageCaptured"
        QT_MOC_LITERAL(42, 0),  // ""
        QT_MOC_LITERAL(43, 9),  // "imageData"
        QT_MOC_LITERAL(53, 12),  // "captureImage"
        QT_MOC_LITERAL(66, 11),  // "acceptPhoto"
        QT_MOC_LITERAL(78, 11),  // "retakePhoto"
        QT_MOC_LITERAL(90, 11),  // "cancelPhoto"
        QT_MOC_LITERAL(102, 12),  // "switchCamera"
        QT_MOC_LITERAL(115, 15),  // "onImageCaptured"
        QT_MOC_LITERAL(131, 2),  // "id"
        QT_MOC_LITERAL(134, 5),  // "image"
        QT_MOC_LITERAL(140, 13),  // "onCameraError"
        QT_MOC_LITERAL(154, 14),  // "onCaptureError"
        QT_MOC_LITERAL(169, 20),  // "QImageCapture::Error"
        QT_MOC_LITERAL(190, 5),  // "error"
        QT_MOC_LITERAL(196, 11)   // "errorString"
    },
    "AccessControl::CameraDialog",
    "imageCaptured",
    "",
    "imageData",
    "captureImage",
    "acceptPhoto",
    "retakePhoto",
    "cancelPhoto",
    "switchCamera",
    "onImageCaptured",
    "id",
    "image",
    "onCameraError",
    "onCaptureError",
    "QImageCapture::Error",
    "error",
    "errorString"
};
#undef QT_MOC_LITERAL
#endif // !QT_MOC_HAS_STRING_DATA
} // unnamed namespace

Q_CONSTINIT static const uint qt_meta_data_CLASSAccessControlSCOPECameraDialogENDCLASS[] = {

 // content:
      12,       // revision
       0,       // classname
       0,    0, // classinfo
       9,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       1,       // signalCount

 // signals: name, argc, parameters, tag, flags, initial metatype offsets
       1,    1,   68,    2, 0x06,    1 /* Public */,

 // slots: name, argc, parameters, tag, flags, initial metatype offsets
       4,    0,   71,    2, 0x08,    3 /* Private */,
       5,    0,   72,    2, 0x08,    4 /* Private */,
       6,    0,   73,    2, 0x08,    5 /* Private */,
       7,    0,   74,    2, 0x08,    6 /* Private */,
       8,    0,   75,    2, 0x08,    7 /* Private */,
       9,    2,   76,    2, 0x08,    8 /* Private */,
      12,    0,   81,    2, 0x08,   11 /* Private */,
      13,    3,   82,    2, 0x08,   12 /* Private */,

 // signals: parameters
    QMetaType::Void, QMetaType::QByteArray,    3,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Int, QMetaType::QImage,   10,   11,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Int, 0x80000000 | 14, QMetaType::QString,   10,   15,   16,

       0        // eod
};

Q_CONSTINIT const QMetaObject AccessControl::CameraDialog::staticMetaObject = { {
    QMetaObject::SuperData::link<QDialog::staticMetaObject>(),
    qt_meta_stringdata_CLASSAccessControlSCOPECameraDialogENDCLASS.offsetsAndSizes,
    qt_meta_data_CLASSAccessControlSCOPECameraDialogENDCLASS,
    qt_static_metacall,
    nullptr,
    qt_incomplete_metaTypeArray<qt_meta_stringdata_CLASSAccessControlSCOPECameraDialogENDCLASS_t,
        // Q_OBJECT / Q_GADGET
        QtPrivate::TypeAndForceComplete<CameraDialog, std::true_type>,
        // method 'imageCaptured'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QByteArray &, std::false_type>,
        // method 'captureImage'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'acceptPhoto'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'retakePhoto'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'cancelPhoto'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'switchCamera'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onImageCaptured'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QImage &, std::false_type>,
        // method 'onCameraError'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onCaptureError'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        QtPrivate::TypeAndForceComplete<QImageCapture::Error, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>
    >,
    nullptr
} };

void AccessControl::CameraDialog::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<CameraDialog *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->imageCaptured((*reinterpret_cast< std::add_pointer_t<QByteArray>>(_a[1]))); break;
        case 1: _t->captureImage(); break;
        case 2: _t->acceptPhoto(); break;
        case 3: _t->retakePhoto(); break;
        case 4: _t->cancelPhoto(); break;
        case 5: _t->switchCamera(); break;
        case 6: _t->onImageCaptured((*reinterpret_cast< std::add_pointer_t<int>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QImage>>(_a[2]))); break;
        case 7: _t->onCameraError(); break;
        case 8: _t->onCaptureError((*reinterpret_cast< std::add_pointer_t<int>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QImageCapture::Error>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[3]))); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (CameraDialog::*)(const QByteArray & );
            if (_t _q_method = &CameraDialog::imageCaptured; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 0;
                return;
            }
        }
    }
}

const QMetaObject *AccessControl::CameraDialog::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *AccessControl::CameraDialog::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_CLASSAccessControlSCOPECameraDialogENDCLASS.stringdata0))
        return static_cast<void*>(this);
    return QDialog::qt_metacast(_clname);
}

int AccessControl::CameraDialog::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QDialog::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 9)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 9;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 9)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 9;
    }
    return _id;
}

// SIGNAL 0
void AccessControl::CameraDialog::imageCaptured(const QByteArray & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}
QT_WARNING_POP
