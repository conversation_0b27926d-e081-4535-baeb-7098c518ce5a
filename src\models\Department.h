#ifndef DEPARTMENT_H
#define DEPARTMENT_H

#include <QString>
#include <QDateTime>
#include <QList>
#include <memory>

namespace AccessControl {

/**
 * @brief 部门数据模型
 * 支持树形结构，每个部门可以有上级部门和下级部门
 */
class Department {
public:
    /**
     * @brief 部门状态枚举
     */
    enum Status {
        Active = 1,    // 启用
        Inactive = 0   // 禁用
    };

    /**
     * @brief 构造函数
     */
    Department();
    
    /**
     * @brief 带参数的构造函数
     * @param name 部门名称
     * @param code 部门代码
     * @param parentId 上级部门ID
     */
    Department(const QString& name, const QString& code, int parentId = 0);
    
    /**
     * @brief 拷贝构造函数
     */
    Department(const Department& other);
    
    /**
     * @brief 赋值操作符
     */
    Department& operator=(const Department& other);
    
    /**
     * @brief 析构函数
     */
    ~Department();

    // ========== Getter 方法 ==========
    
    /**
     * @brief 获取部门ID
     */
    int id() const { return m_id; }
    
    /**
     * @brief 获取部门名称
     */
    QString name() const { return m_name; }
    
    /**
     * @brief 获取部门代码
     */
    QString code() const { return m_code; }
    
    /**
     * @brief 获取上级部门ID
     */
    int parentId() const { return m_parentId; }
    
    /**
     * @brief 获取部门描述
     */
    QString description() const { return m_description; }
    
    /**
     * @brief 获取部门状态
     */
    Status status() const { return m_status; }
    
    /**
     * @brief 获取排序顺序
     */
    int sortOrder() const { return m_sortOrder; }
    
    /**
     * @brief 获取创建时间
     */
    QDateTime createdAt() const { return m_createdAt; }
    
    /**
     * @brief 获取更新时间
     */
    QDateTime updatedAt() const { return m_updatedAt; }
    
    /**
     * @brief 获取部门层级（从根部门开始计算）
     */
    int level() const { return m_level; }
    
    /**
     * @brief 获取部门完整路径（如：公司/技术部/开发组）
     */
    QString fullPath() const { return m_fullPath; }

    // ========== Setter 方法 ==========
    
    /**
     * @brief 设置部门ID
     */
    void setId(int id) { m_id = id; }
    
    /**
     * @brief 设置部门名称
     */
    void setName(const QString& name) { m_name = name; }
    
    /**
     * @brief 设置部门代码
     */
    void setCode(const QString& code) { m_code = code; }
    
    /**
     * @brief 设置上级部门ID
     */
    void setParentId(int parentId) { m_parentId = parentId; }
    
    /**
     * @brief 设置部门描述
     */
    void setDescription(const QString& description) { m_description = description; }
    
    /**
     * @brief 设置部门状态
     */
    void setStatus(Status status) { m_status = status; }
    
    /**
     * @brief 设置排序顺序
     */
    void setSortOrder(int sortOrder) { m_sortOrder = sortOrder; }
    
    /**
     * @brief 设置创建时间
     */
    void setCreatedAt(const QDateTime& createdAt) { m_createdAt = createdAt; }
    
    /**
     * @brief 设置更新时间
     */
    void setUpdatedAt(const QDateTime& updatedAt) { m_updatedAt = updatedAt; }
    
    /**
     * @brief 设置部门层级
     */
    void setLevel(int level) { m_level = level; }
    
    /**
     * @brief 设置部门完整路径
     */
    void setFullPath(const QString& fullPath) { m_fullPath = fullPath; }

    // ========== 工具方法 ==========
    
    /**
     * @brief 检查是否为根部门
     */
    bool isRoot() const { return m_parentId == 0; }
    
    /**
     * @brief 检查是否为启用状态
     */
    bool isActive() const { return m_status == Active; }
    
    /**
     * @brief 获取状态文本
     */
    QString statusText() const;
    
    /**
     * @brief 检查部门数据是否有效
     */
    bool isValid() const;
    
    /**
     * @brief 转换为调试字符串
     */
    QString toString() const;

private:
    int m_id;                    // 部门ID
    QString m_name;              // 部门名称
    QString m_code;              // 部门代码
    int m_parentId;              // 上级部门ID（0表示根部门）
    QString m_description;       // 部门描述
    Status m_status;             // 部门状态
    int m_sortOrder;             // 排序顺序
    QDateTime m_createdAt;       // 创建时间
    QDateTime m_updatedAt;       // 更新时间
    int m_level;                 // 部门层级
    QString m_fullPath;          // 完整路径
};

/**
 * @brief 部门树节点
 * 用于构建部门树形结构
 */
class DepartmentTreeNode {
public:
    DepartmentTreeNode(const Department& dept);
    ~DepartmentTreeNode();
    
    // 部门数据
    Department department;
    
    // 父节点
    DepartmentTreeNode* parent;
    
    // 子节点列表
    QList<std::shared_ptr<DepartmentTreeNode>> children;
    
    /**
     * @brief 添加子节点
     */
    void addChild(std::shared_ptr<DepartmentTreeNode> child);
    
    /**
     * @brief 移除子节点
     */
    bool removeChild(int departmentId);
    
    /**
     * @brief 查找子节点
     */
    std::shared_ptr<DepartmentTreeNode> findChild(int departmentId);
    
    /**
     * @brief 获取所有后代节点
     */
    QList<std::shared_ptr<DepartmentTreeNode>> getAllDescendants();
    
    /**
     * @brief 获取节点路径（从根到当前节点）
     */
    QList<int> getPath();
};

} // namespace AccessControl

#endif // DEPARTMENT_H 